<?php
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'ss_12345_generica_ispe_cr_01', 'Pagella Scolastica Scuole Superiori', 1, 'ispe cr', 1);
*/


$orientamento = 'P';
$formato = 'A4';
$periodo = 9;
if ($periodo_pagella == 'intermedia') {
    $periodo = 7;
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'                     => $id_classe,
    'periodo_pagella'               => $periodo_pagella,
    'periodo'               => $periodo,
    'orientamento'                  => $orientamento,

    'data_attestato_day'            => $data_attestato_Day,
    'data_attestato_month'          => $data_attestato_Month,
    'data_attestato_year'           => $data_attestato_Year,

];


function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">

    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_classe = $parametri_stampa['id_classe'];
    $data_attestato = $parametri_stampa['data_attestato_day'] . '/' . $parametri_stampa['data_attestato_month'] . '/' . $parametri_stampa['data_attestato_year'];
    $id_studente = $studente['id_studente'];
    $periodo = $parametri_stampa['periodo'];
    $periodo_pagella = $parametri_stampa['periodo_pagella'];

    $num_quad = 'II';
    if ($periodo_pagella == 'intermedia') {
        $num_quad ='I';
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe, $periodo, $studente['id_studente']);

    $array_stampa = $arr_recuperi = array();
    $condotta_voto = $condotta_giudizio = $peipdp = '';

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        if ($materia['in_media_pagelle'] != 'NV'
            && (
               !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA','OPZIONALE', 'RELIGIONE'])
                )
            )
        {
            $array_stampa[$id_materia] = $voti_pagella[$id_materia];
        }

        if ($materia['tipo_materia'] == 'CONDOTTA') {
            foreach ($voti_pagella[$id_materia]['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voti_pagella[$id_materia]['voto_pagellina'])
                {
                   $condotta_voto .= $significato['valore'];
                }
            }
        }

        foreach ($voti_pagella[$id_materia]['campi_liberi'] as $campo_libero) {
            if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                if (strpos(strtoupper($campo_libero['nome']), 'CONDOTTA') !== false )
                {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($value !== '') {
                        $condotta_giudizio .= $value;
                    }
                }
                if (strpos(strtoupper($campo_libero['nome']), 'PEI/PDP') !== false )
                {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($value !== '') {
                        $peipdp = "<b>".decode($campo_libero['nome'])."</b>: ".$value;
                    }
                }
            }
        }
        if ($voti_pagella[$id_materia]['tipo_recupero'] != '') {
            $arr_recuperi[$id_materia] =$voti_pagella[$id_materia];
        }
    }

    $aree['Linguistica'] = [
        'ITALIANO',
        'INGLESE',
        'LINGUA ITALIANA'
    ];
    $aree['Storico socio-economica'] = [
        'ECONOMIA E DIRITTO',
        'PARI OPPORTUNITA',
        'CULTURA DEL LAVORO',
        'SICUREZZA AMBIENTE',
        'SICUREZZA E AMBIENTE',
        'STORIA',
        'GEOGRAFIA',
        'SICUREZZA AMBIENTE '
    ];
    $aree['Matematico - scientifica'] = [
        'MATEMATICA',
        'SCIENZE DELL&#039;ALIMENTAZIONE',
        'SCIENZE',
        "SCIENZE DELL'ALIMENTAZIONE"
    ];
    $aree['Tecnologia'] = [
        'INFORMATICA'
    ];
    $aree['Flessibilità'] = [
        'FLESSIBILITA&#039; PERSONALIZZAZIONE',
        'FISICA',
        'ED. FISICA',
//        'EDUCAZIONE CIVICA',
//        'EDUCAZIONE  CIVICA',
        'RELIGIONE-IRC',
        "FLESSIBILITA' PERSONALIZZAZIONE"
    ];
    $aree['Tecnico professionale'] = [
    ];

    $array_stampa_new = [];
    foreach ($array_stampa as $id_mat => $voto) {
        $incluso = false;

        foreach ($aree as $titolo => $area_materie) {

            if (
                    in_array($voto['descrizione'], $area_materie)
                )
            {
                $array_stampa_new[$titolo][$id_mat] = $voto;
                $incluso = true;
            }
        }

        if (!$incluso and stripos($voto['descrizione'], 'tutor') === false) {
            $array_stampa_new['Tecnico professionale'][$id_mat] = $voto;
        }
    }
    $array_stampa_new_ord['LINGUISTICA'] = $array_stampa_new['Linguistica'];
    $array_stampa_new_ord['STORICO SOCIO-ECONOMICA'] = $array_stampa_new['Storico socio-economica'];
    $array_stampa_new_ord['MATEMATICO - SCIENTIFICA'] = $array_stampa_new['Matematico - scientifica'];
    $array_stampa_new_ord['TECNOLOGIA'] = $array_stampa_new['Tecnologia'];
    $array_stampa_new_ord['TECNICO PROFESSIONALE'] = $array_stampa_new['Tecnico professionale'];
    $array_stampa_new_ord['FLESSIBILITÀ'] = $array_stampa_new['Flessibilità'];
    $array_stampa_new = $array_stampa_new_ord;
    foreach ($array_stampa_new as $titolo => $area_materie) {
        if ( empty($area_materie) ) {
            unset($array_stampa_new[$titolo]);
        }
    }

    $labels = [
        "alunno"                   => "ALUNN||max_oa||:",

    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    $studente['esito'] = str_replace('di stato', '', $studente['esito']);
    $studente['esito'] = str_replace("esame", "all'esame", $studente['esito']);
    //}}} </editor-fold>

    $sezione = substr($studente['sezione'], 2);
    if (stripos($sezione, 'DELLACCONCIATURA') !== false) {
        $sezione = "TECNICO DELL'ACCONCIATURA";
    }

    switch ($studente['classe']) {
        case 1:
            $trad_anno = 'I';
            break;
        case 2:
            $trad_anno = 'II';
            break;
        case 3:
            $trad_anno = 'III';
            break;
        case 4:
            $trad_anno = 'IV';
            break;
        case 5:
            $trad_anno = 'V';
            break;
    }

    if (stripos($sezione, 'OPERATORE DEL BENESSERE') !== false || 
        stripos($sezione, 'OP. BEN.') !== false) {
        $desc_anno = "Percorso di qualifica triennale IeFP";
        if (stripos($sezione, 'ACCONCIATURA') !== false || 
        stripos($sezione, ' 300') !== false) {
            $sezione = "<big>CORSO : OPERATORE DEL BENESSERE</big><br>
Indirizzo : Erogazione dei Trattamenti di Acconciatura";
        } else {
            $sezione = "<big>CORSO : OPERATORE DEL BENESSERE</big><br>
Indirizzo : Erogazione dei Servizi di Trattamento Estetico";
        }
    }
    else {
        $desc_anno = "$trad_anno ANNO di IeFP";
    }

    $font = 'helvetica';
    $pdf->AddPage('P', "A4");
    $pdf->SetAutoPageBreak("off", 1);
    inserisci_intestazione_pdf($pdf, $id_classe, 50, 0);

    $pdf->ln(7);
    $pdf->SetFont($font, 'B', 16);
    $pdf->writeHTMLCell(80, 0, 65, '', "<b>PAGELLA SCOLASTICA</b><br><b>A.F. $anno_scolastico_attuale</b>", 1, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->SetFont($font, 'B', 13);
    $pdf->writeHTMLCell(0, 0, '', '', $sezione, 0, 1, false, true, 'C');
    $pdf->SetFont($font, 'B', 12);
    $pdf->CellFitScale(0, 0, $desc_anno, 0, 1, 'C');
    $pdf->ln(10);
    $pdf->SetFont($font, 'B', 14);
    $pdf->CellFitScale(0, 0, $labels['alunno'].' '.$studente['cognome'].' '.$studente['nome'], 0, 1, 'C');
    $pdf->ln(3);
    $pdf->CellFitScale(40, 0, "Classe: ".$studente['classe'].$studente['sezione'][0], 0, 0, 'L');
    $pdf->CellFitScale(0, 0, "$num_quad Quadrimestre", 0, 1, 'R');

    $pdf->ln(4);
    $pdf->SetFont($font, 'B', 10);
    $pdf->CellFitScale(40, 4, "AREA FORMATIVA", 1, 0, 'C');
    $pdf->CellFitScale(80, 4, "MATERIA", 1, 0, 'C');
    $pdf->CellFitScale(20, 4, "VOTO", 1, 0, 'C');
    $pdf->CellFitScale(0, 4, "MEDIA", 1, 1, 'C');

    foreach ($array_stampa_new as $area => $materie_area) {

        if ( !empty($materie_area) )
        {
            $pdf->ln(3);

            $area_voti = $area_cont = $area_media = $h_area = 0;

            $pdf->SetFont($font, '', 8);
            $y_st_area = $pdf->GetY();
            foreach ($materie_area as $voto) {

                $voto_unico = '';
                foreach ($voto['significati_voto'] as $significato)
                {
                    if ($significato['voto'] == $voto['voto_pagellina'])
                    {
                        $voto_unico = $significato['valore'];
                    }
                }

                if ($voto['voto_pagellina']>0) {
                    $area_cont++;
                    $area_voti+=$voto['voto_pagellina'];
                }

                $voto['descrizione'] = $voto['descrizione'] == 'RELIGIONE-IRC' ? "I.R.C. - INSEGNAMENTO RELIGIONE CATTOLICA" : $voto['descrizione'];

                $h_alt = $pdf->getStringHeight( 80, $voto['descrizione'], false, true, '', '');
                $h_area += $h_alt;

                $pdf->SetX(50);
                $pdf->CellFitScale(80, $h_alt, $voto['descrizione'], 1, 0, 'L');
                $pdf->CellFitScale(20, $h_alt, $voto_unico, 1, 1, 'C');
            }

            $alt_area = $pdf->GetY() - $y_st_area;
            $pdf->SetXY(10,$y_st_area);
            $pdf->SetFont($font, 'B', 8);

            if ($area_cont>0)
            {
                $area_media = round($area_voti/$area_cont, 2);
                $area_media = round($area_voti/$area_cont, 0);
            }

//            $pdf->MultiCell(40, $h_area, $area, 1, 'L', 0, 0, 10, '', true, 1, false, true, $h_area, 'M');
            $pdf->CellFitScale(40, $alt_area, $area, 1, 0, 'L');
            $pdf->SetX(150);
            $pdf->CellFitScale(0, $alt_area, ($area_media != 0 ? $area_media : ''), 1, 1, 'C');
        }
    }

    // condotta
    $pdf->ln(4);
    $pdf->SetFont($font, '', 10);
    $pdf->writeHTMLCell(0, 0, '', '', "<b>Condotta</b>: $condotta_voto<br>$condotta_giudizio", 0, 1);

    // pei / pdp
    if ($peipdp != '') {
        $pdf->ln(2);
        $pdf->writeHTMLCell(0, 0, '', '', decode($peipdp), 0, 1);
    }

    if (!empty($arr_recuperi)) {
        $pdf->ln(4);
        $pdf->SetFont($font, 'B', 10);
        $pdf->writeHTMLCell(0, 0, '', '', "<b>Materie con debiti:</b>", 0, 1);
        $pdf->SetFont($font, '', 8);
        foreach ($arr_recuperi as $id_materia => $recupero) {
            $pdf->writeHTMLCell(0, 0, '', '', "{$recupero['descrizione']}: {$recupero['tipo_recupero_tradotto']}", 0, 1);
        }
    }

    $pdf->SetY(270);
    $pdf->SetFont($font, '', 6);
//    $pdf->writeHTMLCell(0, 0, '', '', $footer_html, 0, 0, false, true, 'C');
    $pdf->Image('/var/www-source/mastercom/immagini_scuola/footer.png', '', 275, 190, '', 'png');
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
