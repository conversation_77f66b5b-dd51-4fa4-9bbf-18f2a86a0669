<!DOCTYPE html>
<html lang="it">
    <head>
        <title>{$nome}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="google" value="notranslate">
        <link rel="shortcut icon" type="image/x-icon" href="icone/mastercom.ico">
        <link rel="stylesheet" type="text/css" href="css/jquery-ui-1.8.16.custom.css" />
        <link rel="stylesheet" href="css/style.css">
        <script src="https://accounts.google.com/gsi/client" async defer></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jsrsasign/8.0.20/jsrsasign-all-min.js"></script>
        {* <script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script> *}
        <script type="text/javascript" src="libs/jquery/jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
        <script type="text/javascript" src="javascript/messenger.js?v={$js_version}"></script>
        <script type="text/javascript" src="/mastertek-api/ckeditor-mastercom/ckeditor.js"></script>
        <script type="text/javascript" src="javascript/anylink.js?v={$js_version}"></script>
        <script type="text/javascript" src="javascript/microsoft.js?v={$js_version}"></script>
        <script type="text/javascript" src="javascript/professore/include_funzioni_registro.js?v={$js_version}"></script>

		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.base.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.darkblue.css" type="text/css" />
        <link rel="stylesheet" href="libs/jqwidgets/styles/jqx.bootstrap.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.office.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.metro.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.material.css" type="text/css" />
        <link href="assets/plugins/font-awesome/5.0/css/fontawesome-all.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="assets/fontawesome-free-5.5.0-web/css/all.css">
		<!--link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"-->

		<script type="text/javascript" src="libs/jqwidgets/jqx-all.js"></script>
		<script type="text/javascript" src="libs/jqwidgets/globalization/globalize.js"></script>
        <script type="text/javascript" src="libs/jqwidgets/globalization/globalize.culture.it-IT.js"></script>

		<!-- {*{{{*} Questo pezzo di javascript ha il compito di monitorare il tempo di caricamento complessivo di una pagina. In caso di login, invia l'informazione ad un webservice ajax per essere loggato nel db ed effettuare statistichee monitoraggio omogeneo del tempo di esecuzione delle pagine. -->
	   	<script type="text/javascript">
			var login_iniziale 	= '{$login_iniziale}';
			var current_user 	= '{$current_user}';
			var current_key 	= '{$current_key}';
			var tipo_utente 	= '{$form_stato}';

            CLIENT_ID   = '{$client_id_google}';
            API_KEY     = '{$api_key_google}';

			{literal}
			window.addEventListener("load", loadTime, false);
			function loadTime() {
				var now = new Date().getTime();
				var tempo_caricamento = now - performance.timing.navigationStart;
				if (window.console ) console.log(tempo_caricamento);
				if(login_iniziale == 'SI') {
					var query = {
							current_user:  current_user,
							current_key: current_key,
							form_tipo_utente: tipo_utente,
							form_cosa:  tempo_caricamento,
							form_tipo: 'LOGIN LOAD TIME',
							form_azione: 'registra_log_storico'
						};
					$.ajax({ type: "POST",
						 url: "ajat_manager.php",
						 data: query,
						 cache: false,
						 success: function(response) {
						 },
						 complete: function(response) {
						 },
					});
				}
			}

            function signOutGoogle() {
                /*{{{ */
                try {
                    //var auth2 = gapi.auth2.getAuthInstance();
//
                    //var isSignedIn = auth2.isSignedIn.get();
                    //if (isSignedIn === true){
                    //    auth2.signOut().then(function () {
                    //        console.log('User signed out.');
                    //        window.open("https://accounts.google.com/logout", "_blank");
                    //    });
                    //}
                } catch (e) { };
                /*}}}*/
            }

            /*function initGoogle() {
                try {
                    gapi.load('auth2', function () {
                        gapi.auth2.init({ client_id: "************-tplrvljplria9qdl35ettvcbkke5r89r.apps.googleusercontent.com" });
                        gapi.auth2.init({ client_id: "************-pqd3gkioesph8lnrlbnbp9mntcjoo4k8.apps.googleusercontent.com" });
                    });
                } catch (e) {
                    console.log(e);
                }
            }*/

            $(document).ready(function()
            {
                $('#jqxGeneralModalLoader').jqxLoader({ theme: 'material', isModal: true, text: "", width: 60, height: 60 });
                $('#jqxGeneralLoader').jqxLoader({ theme: 'material', text: "", width: 60, height: 60 });

                $("#search").keypress(function(event) {
                    if (event.which == 13) {
                        event.preventDefault();
                        value = $("#search").val();
                        if (value.trim() != '')
                        {
                            form = document.getElementById('search').form;
                            form.stato_principale.value = 'cerca';
                            form.submit();
                        }
                    }
                });

                $("form").append("<input type='hidden' name='st' value='{/literal}{$st}{literal}'>");

                {/literal}
                    {if $autenticazione_google == 'SI'}
                        initGoogle();
                    {/if}
                {literal}
            });
			{/literal}
		</script>
		<!-- {*}}}*} -->

    <script type="text/javascript">
        window.name = 'mastercom_main_window';
    </script>
    {literal}
        <script type="text/javascript">
            window.name = 'mastercom_main_window';
        </script>
        <link type="text/css" rel="stylesheet" href="css/dhtmlgoodies_calendar.css?random=20051112" media="screen" />
        <script type="text/javascript" src="javascript/dhtmlgoodies_calendar.js?random=20060118"></script>
    <style>
        /*body {font-family: "Roboto", "Arial", sans-serif; background-color: #aaa; margin: 0px; {/literal}{if $natale == 'SI'}background-color: #FAFAFA; background-image: url('icone/christmas_background.png'); background-position: center bottom; background-attachment: fixed; background-size: 100%; background-repeat: no-repeat; {/if}{literal}}*/
        body {font-family: "Roboto", "Arial", sans-serif; background-color: #aaa; margin: 0px; {/literal}{if $natale == 'SI'}background-color: #fffef8; background-image: url('icone/background_christmas_pattern.png'); background-attachment: fixed; background-position-y: bottom; background-size: 20%;  {/if}{literal}}
        button {font-family: "Roboto", "Arial", sans-serif;}
        textarea {font-family: "Roboto", "Arial", sans-serif;}
    </style>
    {/literal}
</head>
<body id='body'>
    <input type='hidden' value='NO' id='modal-open'>
    <input type='hidden' id='login_centralizzato' value='{$login_centralizzato}'>

   <!--<table width='100%' class='{if $natale == "SI"}sfondo_natale{else}sfondo_scuro{/if}' {if $natale == "SI"}style="background-image: url('icone/striscia_fiocchi.png'); background-repeat-y: no-repeat; background-size: 10%;"{/if}>-->
   <table width='100%' class='{if $natale == "SI"}sfondo_natale{else}sfondo_scuro{/if}' {if $natale == "SI"}style="background-image: url('icone/striscia_fiocchi2.png'); background-repeat: repeat-x; background-position-y: top; background-size: 6%;"{/if}>
        <tr valign='middle' align='center' style="font-size: 110%;">
            <td width="103">
                <img src='images/logo3.png' height="45" style="cursor: pointer;" title="{mastercom_label}Home{/mastercom_label}"
                     onclick="
                        document.getElementById('navbar_form').stato_principale.value='home';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();">
            </td>
            <td align="left" width="90%" class='ombra_testo' style="color: white;">
                <b>{$descrizione_pagina}</b>
            </td>
            <td align="right" style="color: white; white-space: nowrap;">
                {$info_ultimo_login}
            </td>
            <td width='1%' align='right'>
                     {foreach $elenco_votazioni_aperte as $votazione}
                         <form method='post' action='../votazioni/index.php' target='new_tab_{math equation='rand(10,1000)'}'>

                             <input type='hidden' name='form_user' value='{$votazione.user}'>
                                <input type='hidden' name='form_code' value='{$votazione.code}'>
                             <input type='submit'  value='{$votazione.label}'>
                         </form>
                     {/foreach}
            </td>
            <td align="right" style="color: white; white-space: nowrap; padding-left: 20px;">
                <div class="desktop dropdown noselect" style="float: none;" onclick="">
                    <span class='ombra_testo' style="position: relative;{if $stato_principale != 'documenti_classi'}  top: -13px;{/if}">{$nome_professore}</span>
                    <img src='{if $natale == 'SI'}icone/account-circle-xmas.png{else}icone/account-circle.png{/if}' height="40" class="dropbtn" style="padding: 5px 14px 0px 5px;">
                    <div class="desktop dropdown-content sfondo_bianco {if $natale == 'SI'}sel_verde{else}sel_blu{/if}" style="right: 10px;">
                        <a onclick="
                            document.getElementById('navbar_form').stato_principale.value='cambio_password';
                            document.getElementById('navbar_form').submit();
                            $('#jqxGeneralModalLoader').jqxLoader('open');
                            ">{mastercom_label}Password{/mastercom_label} <i class="fa fa-fw fa-lock right"></i></a>
                        <a onclick="document.getElementById('navbar_form').stato_principale.value='cambio_lingua';
                                    document.getElementById('navbar_form').submit();
                                    "
                            style='vertical-align: middle;'>{mastercom_label}Lingua{/mastercom_label} <img src='icone/flag_{$lingua}.png' height="20" style='float: right;'></a>
                        {if $funzione_mac_address == '1'}
                            <a onclick="
                                document.getElementById('navbar_form').stato_principale.value='mac_address';
                                document.getElementById('navbar_form').submit();
                                $('#jqxGeneralModalLoader').jqxLoader('open');
                                ">{mastercom_label}MAC Address{/mastercom_label}</a>
                        {/if}
                        {if $roles_utente['sadmin'] > 0
                            || $roles_utente['amministratore'] > 0
                            || $roles_utente['segreteria'] > 0
                            || $roles_utente['lettore'] > 0
                        }
                            <a onclick="accediAmministrazione();"
                            style='vertical-align: middle;'><i class="fa fa-fw fa-arrow-right testo_verde"></i> <b>{mastercom_label}MasterCom{/mastercom_label}</b></a>
                        {/if}
                        {if $roles_utente['studente'] > 0}
                            <a onclick="accediAlQuaderno();"
                            style='vertical-align: middle;'><i class="fa fa-fw fa-arrow-right testo_azzurro"></i> <b>{mastercom_label}Quaderno{/mastercom_label}</b></a>
                        {/if}
                        <a onclick="
                            {if $autenticazione_google == 'SI'}
                                signOutGoogle();
                            {/if}
                            document.getElementById('navbar_form').form_stato.value='logout';
                            document.getElementById('navbar_form').submit();
                            $('#jqxGeneralModalLoader').jqxLoader('open');
                            ">{mastercom_label}Esci{/mastercom_label} <i class="fa fa-fw fa-sign-out-alt right testo_rosso"></i></a>
                    </div>
                </div>
            </td>
        </tr>
    </table>

    {if $privilegi == "2"}
        <table width='100%' class="sfondo_base_generico bordo_inferiore_rilievo">
            <tr valign='middle'>
                <td width='50%'>
                    <form method='post' action='{$SCRIPT_NAME}'>
						{mastercom_auto_button
							icona="uscita"
							size=48
							label="ESCI"
							label_bg='d11a2e'
                            descrizione="Esci da Mastercom"
						}
                        <input type='hidden' name='form_stato' value='logout'>
                        <input type='hidden' name='stato_sesabilita' value='1'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>
                <td  width='50%' align='center' class='sottotitolo_testo'>
                    Siamo spiacenti ma il suo utente non ha i privilegi per accedere all'area riservata di sua competenza,
                    per modificare i propri privilegi rivolgersi al responsabile scolastico.
                </td>
            </tr>
        </table>
    {else}
        <form method='post' id='navbar_form' action='{$SCRIPT_NAME}'>
            <div class="navbar {if $natale == 'SI'}sfondo_natale{else}sfondo_scuro{/if}" style='display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap;'>
                <div>
                    <a class='ombra_testo'
                       style="{if $stato_principale == 'home'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='home';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        $('#jqxGeneralModalLoader').jqxLoader('open');
                        ">{mastercom_label}Home{/mastercom_label}</a>
                    <a class='ombra_testo'
                       style="{if $stato_principale == 'agenda'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='agenda';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        $('#jqxGeneralModalLoader').jqxLoader('open');
                        ">{mastercom_label}Agenda{/mastercom_label}</a>
                    <div class="desktop dropdown {if $natale == 'SI'}sfondo_natale{else}sfondo_scuro{/if}"  onclick="">
                      <button class="dropbtn ombra_testo" style="{if $stato_principale|in_array:['registri_classe', 'registro_docente', 'corsi_coordinamento', 'corsi_principale']}color: yellow;{/if}" disabled>{mastercom_label}Registri{/mastercom_label}</button>
                      <div class="desktop dropdown-content sfondo_bianco {if $natale == 'SI'}sel_verde{else}sel_blu{/if}">
                            <a onclick="
                                document.getElementById('navbar_form').stato_principale.value='registri_classe';
                                document.getElementById('navbar_form').stato_secondario.value='';
                                document.getElementById('navbar_form').submit();
                              ">{if $natale == 'SI'}
                                    <img src='icone/christmas-tree.png' height="20" style="vertical-align: middle;">
                                {/if}
                                {mastercom_label}Registri di classe{/mastercom_label}</a>
                            <a onclick="
                                document.getElementById('navbar_form').stato_principale.value='registro_docente';
                                document.getElementById('navbar_form').stato_secondario.value='';
                                document.getElementById('navbar_form').submit();
                                $('#jqxGeneralModalLoader').jqxLoader('open');
                                ">{if $natale == 'SI'}
                                    <img src='icone/christmas-ball.png' height="20" style="vertical-align: middle;">
                                {/if}
                                {mastercom_label}Registro docente{/mastercom_label}</a>
                            {if $funzione_corsi == "1" and $abilita_corsi == 'SI'}
                            <a onclick="
                                document.getElementById('navbar_form').stato_principale.value='corsi_principale';
                                document.getElementById('navbar_form').stato_secondario.value='';
                                document.getElementById('navbar_form').submit();
                                $('#jqxGeneralModalLoader').jqxLoader('open');
                                ">{if $natale == 'SI'}
                                    <img src='icone/christmas-sweater.png' height="20" style="vertical-align: middle;">
                                {/if}
                                {mastercom_label}Gestione Corsi{/mastercom_label}</a>
                            {/if}
                            {if $funzione_corsi_coordinamento == "1"}
                            <a onclick="
                                document.getElementById('navbar_form').stato_principale.value='corsi_coordinamento';
                                document.getElementById('navbar_form').stato_secondario.value='';
                                document.getElementById('navbar_form').submit();
                                $('#jqxGeneralModalLoader').jqxLoader('open');
                                ">{if $natale == 'SI'}
                                    <img src='icone/christmas-stick.png' height="20" style="vertical-align: middle;">
                                {/if}
                                {mastercom_label}Corsi di Coordinamento{/mastercom_label}</a>
                            {/if}
                      </div>
                    </div>
                    <a class='ombra_testo'
                       style="{if $stato_principale == 'assenze_new'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='assenze_new';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        ">{mastercom_label}Assenze{/mastercom_label}</a>
                    <a class='ombra_testo'
                       style="{if $stato_principale == 'voti_new'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='voti_new';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        ">{mastercom_label}Voti{/mastercom_label}</a>
                    <a class='ombra_testo'
                       style="{if $stato_principale == 'argomenti_compiti'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='argomenti_compiti';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        ">{mastercom_label}Argomenti e Compiti{/mastercom_label}</a>
                    {if $funzione_pannello_firme == '1'}
                    <a class='ombra_testo'
                       style="{if $stato_principale == 'pannello_firme'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='pannello_firme';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        $('#jqxGeneralModalLoader').jqxLoader('open');
                        ">{mastercom_label}Firme{/mastercom_label}</a>
                    {/if}
                    {if ($funzione_messaggi == "1" && $param_abilita_messaggi_registro == 'SI' && ($versione_messenger == 'VERSIONE_2' || ($versione_messenger == 'VERSIONE_1' && $messenger_name != '')))}
                    <a class='ombra_testo'
                       style="{if $stato_principale == 'materiale_didattico'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='materiale_didattico';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        ">{mastercom_label}Materiale Didattico{/mastercom_label}</a>
                    {/if}
                    <a  class='ombra_testo'
                        style="{if $stato_principale == 'pagelle_principale'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='pagelle_principale';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        ">{mastercom_label}Pagelle{/mastercom_label}</a>
                    <a  class='ombra_testo'
                        style="{if $stato_principale == 'stampe'}color: yellow;{/if}"
                        onclick="
                        document.getElementById('navbar_form').stato_principale.value='stampe';
                        document.getElementById('navbar_form').stato_secondario.value='';
                        document.getElementById('navbar_form').submit();
                        ">{mastercom_label}Stampe{/mastercom_label}</a>
                    {*{if $tasto_altre_funzioni == 'SI'}*}
                        <div class="desktop dropdown {if $natale == 'SI'}sfondo_natale{else}sfondo_scuro{/if}" onclick="">
                            <button class="dropbtn ombra_testo" disabled>{mastercom_label}Altre Funzioni{/mastercom_label}</button>
                            <div class="desktop dropdown-content sfondo_bianco {if $natale == 'SI'}sel_verde{else}sel_blu{/if}">
                                {if $funzione_note == '1'}
                                <a onclick="
                                    document.getElementById('navbar_form').stato_principale.value='note_disciplinari';
                                    document.getElementById('navbar_form').stato_secondario.value='';
                                    document.getElementById('navbar_form').submit();
                                    $('#jqxGeneralModalLoader').jqxLoader('open');
                                  ">{if $natale == 'SI'}
                                    <img src='icone/christmas-present.png' height="20" style="vertical-align: middle;">
                                {/if}
                                {mastercom_label}Note{/mastercom_label}</a>
                                {/if}
                                {if $optional_prenota_risorse_professore == 'OK' && $funzione_prenota_risorse == '1'}
                                <a onclick="
                                    document.getElementById('navbar_form').stato_principale.value='prenotazione_risorse_2';
                                    document.getElementById('navbar_form').stato_secondario.value='';
                                    document.getElementById('navbar_form').submit();
                                    $('#jqxGeneralModalLoader').jqxLoader('open');
                                  ">{if $natale == 'SI'}
                                        <img src='icone/christmas-reindeer.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Prenota Risorse{/mastercom_label}</a>
                                {/if}
                                {if $param_attivazione_moodle == 'SI'}
                                <a onclick="
                                    accessoMoodle('{$moodle_state}');
                                  ">{if $natale == 'SI'}
                                        <img src='icone/christmas-hat.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Moodle{/mastercom_label}</a>
                                    {/if}
                                {if $param_visualizza_pulsante_alternanza == 'SI'}
                                <a onclick="
                                    document.getElementById('btn-alternanza').submit();
                                  ">{if $natale == 'SI'}
                                        <img src='icone/christmas-snowman.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Alternanza{/mastercom_label}</a>
                                    {/if}
                                {if $funzione_mensa == "1" and $abilita_servizio_mensa == 'SI'}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='mense_principale';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                  ">{if $natale == 'SI'}
                                        <img src='icone/christmas-sleigh.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Mensa{/mastercom_label}</a>
                                {/if}
                                {if $optional_adozione_libri == "1" && $login_centralizzato == "SI"}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='adozione_libri';
                                        document.getElementById('navbar_form').stato_secondario.value='selezione_classi';
                                        document.getElementById('navbar_form').submit();
                                  ">{if $natale == 'SI'}
                                        <img src='icone/christmas-stick.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Adozione libri{/mastercom_label}</a>
                                {/if}
                                {if $funzione_funzioni_sidi_registro == "1"}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='funzioni_sidi';
                                        document.getElementById('navbar_form').stato_secondario.value='home';
                                        document.getElementById('navbar_form').submit();
                                  ">{if $natale == 'SI'}
                                        <img src='icone/christmas-ball.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Funzioni SIDI{/mastercom_label}</a>
                                {/if}
                                {if $param_abilita_hotspot == "SI"}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='connettivita';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                    ">{if $natale == 'SI'}
                                        <img src='icone/christmas-tree.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Abilitazione internet{/mastercom_label}</a>
                                {/if}
                                {if $param_programmazione_didattica == "SI" && $login_centralizzato == 'SI'}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='programmazione_didattica';
                                        document.getElementById('navbar_form').stato_secondario.value='elenco_programmazioni';
                                        document.getElementById('navbar_form').submit();
                                    ">{if $natale == 'SI'}
                                        <img src='icone/christmas-present.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Progr. Didattica{/mastercom_label}</a>
                                {/if}
                                {if $optional_google_classroom == "OK"}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='google_classroom';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                    ">{if $natale == 'SI'}
                                        <img src='icone/christmas-sweater.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                {mastercom_label}Google Classroom{/mastercom_label}</a>
                                {/if}
                                {if $funzione_dad_professore == "1" || $funzione_dad_coordinatore == "1"}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='dad_ddi';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                    ">{if $natale == 'SI'}
                                        <img src='icone/christmas-hat.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}DAD e DDI{/mastercom_label}</a>
                                {/if}
                                {if $funzione_esami_stato == '1'}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='esami_stato_principale';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                  ">{if $natale == 'SI'}
                                        <img src='icone/christmas-sleigh.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Esami di Stato{/mastercom_label}</a>
                                {/if}
                                {if $param_modifica_servizi_giornalieri == 'SI'}
                                    {if $funzione_servizi_giornalieri_registro == '1'}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='servizi_giornalieri';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                    ">{if $natale == 'SI'}
                                        <img src='icone/christmas-reindeer.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Servizi giornalieri{/mastercom_label}</a>
                                    {/if}
                                {/if}
                                {if $funzione_tutor_registro == '1'}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='tutor_registro';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                    ">{if $natale == 'SI'}
                                        <img src='icone/christmas-snowman.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Tutor{/mastercom_label}</a>
                                {/if}
                                {if $versione_messenger == 'VERSIONE_2'}
                                    <a onclick="
                                        document.getElementById('navbar_form').stato_principale.value='documenti_classi';
                                        document.getElementById('navbar_form').stato_secondario.value='';
                                        document.getElementById('navbar_form').submit();
                                    ">{if $natale == 'SI'}
                                        <img src='icone/christmas-sweater.png' height="20" style="vertical-align: middle;">
                                    {/if}
                                    {mastercom_label}Documenti Classi{/mastercom_label}</a>
                                {/if}
                            </div>
                        </div>
                    {*{/if}*}
                </div>
                <div style="padding: 6px 14px;">
                    <input type='search' placeholder="{mastercom_label}Cerca{/mastercom_label}" id="search" name="search" class="input_text" style="text-align: left;">

                    {if $funzione_messaggi == "1" && $param_abilita_messaggi_registro == 'SI'}
                        <div style="display: inline-block;">
                            <div class='messenger-notify-2'></div>
                            {if ($versione_messenger == 'VERSIONE_1') && ($messenger_name != '' || $current_key != '')}
                                <img src='icone/message2.png'
                                    height="25"
                                    title="{mastercom_label}Messaggi{/mastercom_label}"
                                    style="padding-left: 10px; vertical-align: middle; cursor: pointer;"
                                    onclick="$('#btn-messenger').submit();"
                                    ></img>
                                <script type="text/javascript" language="JavaScript">
                                    $(document).ready( function(){
                                        init_messenger('{$messenger_name}', '{$messenger_certificate}', '{$current_key}');

                                        if (('{$messenger_name}' !== '' && '{$messenger_certificate}' === '') || '{$current_key}' !== '') {
                                            setTimeout(function () {
                                                Messenger.ctrl = get_new_messages();
                                            }, 2000);
                                        }
                                    });
                                </script>
                            {elseif $versione_messenger == 'VERSIONE_2'}
                                {*<img src='icone/message2.png'
                                    height="25"
                                    title="{mastercom_label}Messaggi{/mastercom_label}"
                                    style="padding-left: 10px; vertical-align: middle; cursor: pointer;"
                                    onclick="$('#btn-messenger2').submit();"
                                    ></img>*}
                                <img src='icone/quaderno/messaggi.svg'
                                    height="30"
                                    title="{mastercom_label}Messaggi{/mastercom_label}"
                                    style="padding-left: 10px; vertical-align: middle; cursor: pointer;"
                                    onclick="$('#btn-messenger2').submit();"
                                    ></img>
                                <script type="text/javascript" language="JavaScript">
                                    $(document).ready( function(){
                                        checkMessaggiNuovoMessenger();

                                        var intervalMessaggi = setInterval(function () {
                                            checkMessaggiNuovoMessenger();
                                        }, 60000); // ogni 1 minuto
                                    });
                                </script>
                            {/if}
                        </div>
                    {/if}

                    <span class='ombra_testo pointer' style='padding-left: 10px; vertical-align: middle; cursor: pointer; font-size: 30px;' title='{mastercom_label}Nuova scheda{/mastercom_label}'
                        onclick="
                            document.getElementById('navbar_form').target = '_blank';
                            document.getElementById('navbar_form').submit();
                            document.getElementById('navbar_form').target = '_self';
                        ">+</span>
                </div>
            </div>

            {* Loader *}
            <div id="jqxGeneralModalLoader"></div>
            <div id="jqxGeneralLoader"></div>

            {* Hidden Input *}
            <input type='hidden' name='form_stato' value='{$form_stato}'>
            <input type='hidden' name='stato_principale' value=''>
            <input type='hidden' name='stato_secondario' value=''>
            <input type='hidden' name='current_user' id='current_user' value='{$current_user}'>
            <input type='hidden' name='current_key' id='current_key' value='{$current_key}'>
            <input type='hidden' id='db_key' value='{$db_key}'>
            <input type='hidden' id='tipo_utente' value='{$form_stato}'>
            <input type='hidden' name='tipo_accesso'>
        </form>

        <form id="btn-messenger2" method="POST" target='_blank' action="{$SCRIPT_NAME}">
            <input type='hidden' name='stato_principale' value='messenger'>
            <input type='hidden' name='form_stato' value='{$form_stato}'>
            <input type='hidden' name='current_user' value='{$current_user}'>
            <input type='hidden' name='current_key' value='{$current_key}'>
        </form>

        <form id="messenger-auth">
            <input type="hidden" name="certificate">
            <input type="hidden" name="name">
        </form>
        <form id="btn-messenger" method="GET" target='MASTERCOM-MESSENGER' action="/messaggi"></form>
        <form id="btn-alternanza" method="GET" target='_blank' action="https://alternanza.registroelettronico.com/{$mastercom_id}"></form>
    {/if}
	</div>

