<!DOCTYPE html>
<html lang="it">
    <head>
        <title>{$titolo} - {$nome}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="google" value="notranslate">
        <link rel="shortcut icon" type="image/x-icon" href="icone/mastercom.ico">
        <link rel="stylesheet" type="text/css" href="css/jquery-ui-1.8.16.custom.css" />
        <link rel="stylesheet" href="css/style.css">
        <script src="https://accounts.google.com/gsi/client" async defer></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jsrsasign/8.0.20/jsrsasign-all-min.js"></script>
        {* <script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script> *}
        <script type="text/javascript" src="libs/jquery/jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
        {*<script type="text/javascript" src="libs/jquery-ui-1.11.0/jquery-ui.min.js"></script>*}
        <script type="text/javascript" src="javascript/messenger.js?v={$js_version}"></script>
        <script type="text/javascript" src="/mastertek-api/ckeditor-mastercom/ckeditor.js"></script>
        <script type="text/javascript" src="javascript/anylink.js?v={$js_version}"></script>

		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.base.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.darkblue.css" type="text/css" />
		<!--link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"-->

		<script type="text/javascript" src="libs/jqwidgets/jqx-all.js"></script>
		<script type="text/javascript" src="libs/jqwidgets/globalization/globalize.culture.it-IT.js"></script>
		<script type="text/javascript" src="libs/jqwidgets/globalization/globalize.js"></script>

		<!-- {*{{{*} Questo pezzo di javascript ha il compito di monitorare il tempo di caricamento complessivo di una pagina. In caso di login, invia l'informazione ad un webservice ajax per essere loggato nel db ed effettuare statistichee monitoraggio omogeneo del tempo di esecuzione delle pagine. -->
	   	<script type="text/javascript">
			var login_iniziale 	= '{$login_iniziale}';
			var current_user 	= '{$current_user}';
			var current_key 	= '{$current_key}';
			var tipo_utente 	= '{$form_stato}';

            CLIENT_ID   = '{$client_id_google}';
            API_KEY     = '{$api_key_google}';

			{literal}
            function signOutGoogle() {
                /*{{{ */
                try {
                    //var auth2 = gapi.auth2.getAuthInstance();
//
                    //var isSignedIn = auth2.isSignedIn.get();
                    //if (isSignedIn === true){
                    //    auth2.signOut().then(function () {
                    //        console.log('User signed out.');
                    //        window.open("https://accounts.google.com/logout", "_blank");
                    //    });
                    //}
                } catch (e) { };
                /*}}}*/
            }

            /*function initGoogle() {
                gapi.load('auth2', function(){
                    gapi.auth2.init({client_id: "************-tplrvljplria9qdl35ettvcbkke5r89r.apps.googleusercontent.com"});
                    gapi.auth2.init({client_id: "************-pqd3gkioesph8lnrlbnbp9mntcjoo4k8.apps.googleusercontent.com"});
                });
            }*/

			window.addEventListener("load", loadTime, false);
			function loadTime() {
				var now = new Date().getTime();
				var tempo_caricamento = now - performance.timing.navigationStart;
				if (window.console ) console.log(tempo_caricamento);
				if(login_iniziale == 'SI') {
					var query = {
							current_user:  current_user,
							current_key: current_key,
							form_tipo_utente: tipo_utente,
							form_cosa:  tempo_caricamento,
							form_tipo: 'LOGIN LOAD TIME',
							form_azione: 'registra_log_storico'
						};
					$.ajax({ type: "POST",
						 url: "ajat_manager.php",
						 data: query,
						 cache: false,
						 success: function(response) {
						 },
						 complete: function(response) {
						 },
					});
				}
			}
			{/literal}

            $(document).ready(function()
            {
                {if $autenticazione_google == 'SI'}
                    initGoogle();
                {/if}
            });
		</script>
		<!-- {*}}}*} -->

    <script type="text/javascript">
        window.name = 'mastercom_main_window';
    </script>
    {literal}
        <script type="text/javascript">
            window.name = 'mastercom_main_window';
        </script>
        <link type="text/css" rel="stylesheet" href="css/dhtmlgoodies_calendar.css?random=20051112" media="screen" />
        <script type="text/javascript" src="javascript/dhtmlgoodies_calendar.js?random=20060118"></script>
    <style>
        body {font-family: "Arial"; background-color: #aaa;}
    </style>
    {/literal}
</head>
<body>
    <input type='hidden' id='login_centralizzato' value='{$login_centralizzato}'>
    <table width='100%' class='sfondo_scuro_generico bordo_superiore_rilievo'>
        <tr valign='middle' align='center' >
            <td width="103">
                <img src='images/logo2.png' height="60" width="103">
            </td>
            <td class='titolo_principale' height="54px" width="50%">
                <b>{$descrizione_pagina}</b>
            </td>
            <td class="sottotitolo_testo" height="54px">
                {$info_ultimo_login}
            </td>
            <td width='1%' align='right'>
                     {foreach $elenco_votazioni_aperte as $votazione}
                         <form method='post' action='../votazioni/index.php' target='new_tab_{math equation='rand(10,1000)'}'>

                             <input type='hidden' name='form_user' value='{$votazione.user}'>
                                <input type='hidden' name='form_code' value='{$votazione.code}'>
                             <input type='submit'  value='{$votazione.label}'>
                         </form>
                     {/foreach}
            </td>
        </tr>
    </table>

    {if $privilegi == "2"}
        <table width='100%' class="sfondo_base_generico bordo_inferiore_rilievo">
            <tr valign='middle'>
                <td width='50%'>
                    <form method='post' action='{$SCRIPT_NAME}'>
						{mastercom_auto_button
							icona="uscita"
							size=48
							label="ESCI"
							label_bg='d11a2e'
                            descrizione="Esci da Mastercom"
						}
                        <input type='hidden' name='form_stato' value='logout'>
                        <input type='hidden' name='stato_sesabilita' value='1'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>
                <td  width='50%' align='center' class='sottotitolo_testo'>
                    Siamo spiacenti ma il suo utente non ha i privilegi per accedere all'area riservata di sua competenza,
                    per modificare i propri privilegi rivolgersi al responsabile scolastico.
                </td>
            </tr>
        </table>
    {else}
        <table width='100%' class="sfondo_base_generico bordo_inferiore_rilievo">
            <tr valign='middle'>
                {if $autenticazione_crs != 'NO'}
                    <td>
                        <a href='https://idpcrl.crs.lombardia.it/scauth/SSLAuthServlet?TARGET=https://{$local_ip}/mastercom/Shibboleth.sso/Logout?return=https://{$local_ip}/mastercom/index.php'><img src='icone/icone_dinamiche/dispimg_icone_menu.php?icona=Error&text=M&text2=USCITA' alt='USCITA'></a>
                    </td>
                {else}
                    <td>
                        <form method='post' action='{$SCRIPT_NAME}'>
						{mastercom_auto_button
							icona="uscita"
							size=48
							label="ESCI"
							label_bg='d11a2e'
                            onclick='signOutGoogle();'
						}
                            <input type='hidden' name='form_stato' value='logout'>
                            <input type='hidden' name='stato_sesabilita' value='1'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                <td  width='50%'>
                    <form method='post' action='{$SCRIPT_NAME}' name='form_cambio_pwd'>
						{mastercom_auto_button
							icona="password"
							size=48
							label="PASSWORD"
							tipo_bottone="image"
							label_bg='cc5e00'
							onclick="if (confirm('Si desidera procedere al cambio password?'))
                                    {
                                        document.forms['form_cambio_pwd'].submit();
                                    }"
						}
                        <input type='hidden' name='form_stato' value='change_password'>
                        <input type='hidden' name='stato_sesabilita' value='1'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>

                {if $funzione_assenze == "1" && 1 == 2}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
                        		estensione='NO'
                        		nome_pulsante="visualizzazione_assenze"
                        		immagine="icone_dinamiche/dispimg_icone_menu.php?icona=Window&text=10&text2=ASSENZE"
                            	icona="calendario"
								bullet="01"
								size=48
								label="AGENDA"
                       			contesto="istituto"
                        		tipo_bottone="input"
                        		descrizione="Gestione Risorse"
                        		utente_corrente=$current_user
								label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='assenze_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_voti == "1"}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_voti"
								immagine="icona.php?icon=voti&bullet=08&size=48&label=VOTI"
								icona="voti"
								bullet="02"
								size=48
								label="VOTI"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Gestione Voti"
								utente_corrente=$current_user
								label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='voti_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $funzione_comunicazioni == "1" && $abilita_area_comunicazioni == 'SI'}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
                                estensione='NO'
                                nome_pulsante="visualizzazione_comunicazioni"
                                icona="comunicazioni"
                                bullet="03"
                                size=48
                                label="COMUNICAZIONI"
                                contesto="istituto"
                                tipo_bottone="input"
                                descrizione="Gestione Comunicazioni"
                                utente_corrente=$current_user
							label_bg='1d8eb6'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='comunicazioni_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_studenti == "1"}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_studenti"
								immagine="icona.php?icon=studente&bullet=07&size=48&label=STUDENTI"
								icona="studente"
								bullet="03"
								size=48
								label="STUDENTI"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Gestione Dati Studenti"
								utente_corrente=$current_user
								label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='classi_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_assenze == "1"}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_assenze"
                        immagine="icone_dinamiche/dispimg_icone_menu.php?icona=Window&text=10&text2=ASSENZE"
                            icona="assenza"
							bullet="04"
							size=48
							label="ASSENZE"
                       contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Assenze"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='assenze_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $funzione_giustifica_assenze == "1" or $funzione_giustifica_assenze_professore == "1"}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_giustificazioni"
                            icona="giustifica"
							bullet="05"
							size=48
							label="GIUST."
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Giustificazioni"
                        utente_corrente=$current_user
							label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='giustificazioni_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $funzione_pagelle == "1"}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_pagelle"
                            icona="pagella"
							bullet="05"
							size=48
							label="PAGELLE"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione Pagelle e Pagelline"
                        utente_corrente=$current_user
							label_bg='3c2f67'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='pagelle_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

                {if $funzione_esami_stato == "1"}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
                        estensione='NO'
                        nome_pulsante="visualizzazione_pagelle"
                            icona="esami"
							bullet="06"
							size=48
							label="ESAMI"
                        contesto="istituto"
                        tipo_bottone="input"
                        descrizione="Gestione esami di stato"
                        utente_corrente=$current_user
							label_bg='3c2f67'
                            }                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='esami_stato_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}


                {if $funzione_progetti == "1"}
                    <td align='center' >
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="sezione_dirigenti"
								icona="dirigenti"
								bullet="07"
								size=48
								label="PROGETTI"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Sezione progetti"
								utente_corrente=$current_user
								label_bg='3c2f67'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='progetti_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
				{if $funzione_corsi == "1" and $abilita_corsi == 'SI'}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="sezione_corsi"
								icona="corsi"
								bullet="08"
								size=48
								label="CORSI"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Gestione Corsi"
								utente_corrente=$current_user
								label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='corsi_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
				{if $funzione_mensa == "1" and $abilita_servizio_mensa == 'SI'}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {counter print=false assign='cont'}
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_eventi"
								icona="mense"
								bullet="09"
								size=48
								label="MENSE"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Gestione Mense"
								utente_corrente=$current_user
								label_bg='44621c'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='mense_principale'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $funzione_pannello_firme == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_eventi"
								immagine="icona.php?icon=firme&bullet=10&size=48&label=FIRME"
								icona="firme"
								bullet="10"
								size=48
								label="FIRME"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Pannello Firme"
								utente_corrente=$current_user
								label_bg='cc5e00'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='pannello_firme'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}
                {if $funzione_corsi_coordinamento == "1"}
                    <td align='center'>
                        <form method='post' action='{$SCRIPT_NAME}'>
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_eventi"
								immagine="icona.php"
								icona="copia_orario"
								bullet="11"
								size=48
								label="Coord."
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Corsi Coordinamento"
								utente_corrente=$current_user
								label_bg='cc5e00'
                            }
                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                            <input type='hidden' name='stato_principale' value='corsi_coordinamento'>
                            <input type='hidden' name='current_user' value='{$current_user}'>
                            <input type='hidden' name='current_key' value='{$current_key}'>
                        </form>
                    </td>
                {/if}

            </tr>
        </table>
    {/if}
	</div>

