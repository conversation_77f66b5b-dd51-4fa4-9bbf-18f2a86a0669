<?php
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'mm_registro_voti_iislagos_01', 'Registro voti Scheda studente scuola secondaria di I grado', 1, 'registro voti Scheda studente medie iislagos', 10);
*/

$orientamento = 'P';
$formato = 'A4';

if ($periodo ==27) {
    $periodo_pagella = "intermedia" ;
}
elseif ($periodo ==29) {
    $periodo_pagella = "finale" ;
}


$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'                     => $id_classe,
    'periodo'                       => $periodo,
    'periodo_pagella'               => $periodo_pagella,
    'orientamento'                  => $orientamento,
    'data_day'            => $data_Day,
    'data_month'          => $data_Month,
    'data_year'           => $data_Year
];


function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_classe = $parametri_stampa['id_classe'];
    $orientamento = $parametri_stampa['orientamento'];
    $data_attestato = $parametri_stampa['data_day'] . '/' . $parametri_stampa['data_month'] . '/' . $parametri_stampa['data_year'];
    $periodo = $parametri_stampa['periodo'];

    $id_studente = $studente['id_studente'];
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = "prov. {$studente['provincia_nascita_da_comune']}";
    }
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_p1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 27, $studente['id_studente']);
    if ($periodo == 29) {
        $voti_p2 = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);
    }

    $arr_voti = $voti_condotta = $giudizi = [];
    $ass1 = $ass2 = $asstot = 0;
    $note = '………………………………………<br>
………………………………………<br>
………………………………………<br>
………………………………………<br>
………………………………………';

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $ass1 += $voti_p1[$id_materia]['ore_assenza'];
        $ass2 += $voti_p2[$id_materia]['ore_assenza'];

        if ($materia['in_media_pagelle'] != 'NV' &&
           !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO', 'OPZIONALE'])
            )
        {
            $arr_voti[$id_materia]['p1'] = $voti_p1[$id_materia];
            $arr_voti[$id_materia]['fin'] = $voti_p2[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $arr_voti[$id_materia]['p1'] = $voti_p1[$id_materia];
            $arr_voti[$id_materia]['fin'] = $voti_p2[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA' )
        {
            $arr_voti[$id_materia]['p1'] = $voti_p1[$id_materia];
            $arr_voti[$id_materia]['fin'] = $voti_p2[$id_materia];

            foreach ($voti_p1[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    if (strpos(strtoupper($campo_libero['nome']), 'GIUDIZIO GLOBALE') !== false)
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $giudizi['p1'] .= $value;
                        }
                    }
                }
            }
            foreach ($voti_p2[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    if (strpos(strtoupper($campo_libero['nome']), 'GIUDIZIO GLOBALE') !== false)
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value !== '') {
                            $giudizi['p2'] .= "<br>$value";
                        }
                    }
                }
            }
        }
    }

    $anno_scolastico_corrente = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $a = explode('/', $anno_scolastico_corrente);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];
    $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
    $anno_scolastico_corrente = $anno_inizio . "/" . $anno_fine;
    $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
    //estraggo i dati di provenienza e di quante volte è ripetente
    $provenienza = '';
    for($cont_curr=0; $cont_curr <count($curriculum_studente); $cont_curr++)
    {
        if($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente)
        {
            if($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_desc'].' '.$curriculum_studente[$cont_curr]['nome_scuola'];
            }
            else
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
            }
        }
        else {
            $classe_attuale = $curriculum_studente[$cont_curr]['classe'];

            if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_corrente
                && $classe_attuale == $curriculum_studente[$cont_curr]['classe']
                && $studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola']
                && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                if (($curriculum_studente[$cont_curr]['esito'] == 'Iscritto') || ($cont_curr <count($curriculum_studente) -1)) {

                    // Nel caso nel curriculum non sia stata inserita la scuola di provenienza
                    if($curriculum_studente[$cont_curr]['nome_scuola'] != "") {
                        $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                    } else {
                        $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
                    }
                }
            }
        }
    }

    $esito_precedente = estrai_esito_e_volte_iscritto_da_curriculum_studente((int) $studente['id_studente'], $anno_scolastico_precedente);
    $titolo_ammissione = $esito_precedente['esito'];

    // Dizionario temporaneo
    $labels = [
        "anag_txt"  => "N {$studente['registro']} L’alunn||min_oa|| {$studente['cognome']} {$studente['nome']}<br>
nat||min_oa|| a $luogo_nascita ($provincia_nascita) il {$studente['data_nascita_ext']}<br>
Indirizzo {$studente['indirizzo']} telefono {$studente['telefono']}<br>
Proveniente da $provenienza iscritt||min_oa|| per la {$studente['mat_esito']['numero_volte_iscritto']} volta alla classe {$studente['classe']} Sez {$studente['sezione']}",
        "anno_scolastico"           => "Anno Scolastico $anno_scolastico_attuale",
        "esito"     => "L'alunn||min_oa|| è stat||min_oa|| {$studente['esito']}",
        "txt1"  => "RILEVAZIONE DEI PROGRESSI NELL’APPRENDIMENTO E NELLO SVILUPPO PERSONALE E SOCIALE DELL’ALUNN||max_oa||"
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf">
    $f = 'helvetica';
    $fd = 8;
    $pdf->AddPage($orientamento);
    $pdf->SetAutoPageBreak("off", 0);
    $pdf->SetFont($f, '', $fd);
    $ys = $pdf->getY();
    $page_width = $pdf->getPageWidth();
    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];
    $half_page = $page_width / 2;
    $w = $half_page - $m_destro - $m_sinistro;
    $w2 = $page_width / 2;

    $pdf->writeHTMLCell($w2, 0, '', '', $labels['anno_scolastico'], 0, 0, false, true);
    $pdf->writeHTMLCell($w2, 0, '', '', "Classe: {$studente['classe']}{$studente['sezione']} Media", 0, 1, false, true);
    $pdf->ln(2);
    $tbl_anag = '<table border="0.1px" cellpadding="3" align="center">'
            . '<tr>'
                . '<td width="75%" align="left" rowspan="3" style="line-height: 18px;">'.
                    $labels['anag_txt']
                . '</td>'
                . '<td width="25%">ASSENZE</td>'
            . '</tr>'
            . '<tr>'
                .'<td width="8%">1</td>'
                .'<td width="8%">2</td>'
                .'<td width="9%">TOT</td>'
            . '</tr>'
            . '<tr>'
                .'<td>'.traduci_minuti_in_ore_minuti($ass1, 'ORE_MINUTI').'</td>'
                .'<td>'.($ass2 != 0 ? traduci_minuti_in_ore_minuti($ass2-$ass1, 'ORE_MINUTI'): '').'</td>'
                .'<td>'.($ass2 != 0 ? traduci_minuti_in_ore_minuti($ass2, 'ORE_MINUTI'): '').'</td>'
            . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_anag, 0, 1, false, true);
    $pdf->ln(10);
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->CellFitScale(0, 0, 'GIUDIZI PER DISCIPLINA', 0, 1, 'C');
    $pdf->ln(3);
    $pdf->SetFont($f, '', $fd);
    $tbl_voti = '<table align="center" border="0.1px" cellpadding="2">'
            . '<tr>'
                . '<td width="20%"><b>MATERIE</b></td>'
                . '<td width="30%"><b>PRIMO QUADRIMESTRE</b></td>'
                . '<td width="30%"><b>SECONDO QUADRIMESTRE</b></td>'
                . '<td width="20%" rowspan="20" align="left" style="line-height: 12px;">Concessi nulla osta<br><br>'
                    . 'il .............. <br><br><br><br>'
                    . 'Per il trasferimento<br>a ..............
<br><br><br><br>
Rilasciata scheda<br>il '.$data_attestato.'<br><br>
a Lagos</td>'
            . '</tr>'
            . '<tr>'
                . '<td></td>'
                . '<td><b>Unico</b></td>'
                . '<td><b>Unico</b></td>'
            . '</tr>';
    foreach($arr_voti as $voto)
    {
        $dsc = $u1 = $o1 = $s1 = $p1 = $u2 = $o2 = $s2 = $p2 = '';
        $dsc = $voto['p1']['descrizione'];
//        echo_debug($voto['p1']);
        foreach ($voto['p1']['significati_voto'] as $significato)
        {
            if ( $significato['voto'] == $voto['p1']['voto_pagellina'] ) {
                $u1 = $significato['valore_pagella'];
            }
            if ( $significato['voto'] == $voto['p1']['voto_scritto_pagella'] ) {
                $s1 = $significato['valore_pagella'];
            }
            if ( $significato['voto'] == $voto['p1']['voto_orale_pagella'] ) {
                $o1 = $significato['valore_pagella'];
            }
            if ( $significato['voto'] == $voto['p1']['voto_pratico_pagella'] ) {
                $p1 = $significato['valore_pagella'];
            }
        }
        foreach ($voto['fin']['significati_voto'] as $significato)
        {
            if ( $significato['voto'] == $voto['fin']['voto_pagellina'] ) {
                $u2 = $significato['valore_pagella'];
            }
            if ( $significato['voto'] == $voto['fin']['voto_scritto_pagella'] ) {
                $s2 = $significato['valore_pagella'];
            }
            if ( $significato['voto'] == $voto['fin']['voto_orale_pagella'] ) {
                $o2 = $significato['valore_pagella'];
            }
            if ( $significato['voto'] == $voto['fin']['voto_pratico_pagella'] ) {
                $p2 = $significato['valore_pagella'];
            }
        }

        $tbl_voti .= '<tr>'
                . "<td>$dsc</td>"
                . "<td>$u1</td>"
                . "<td>$u2</td>"
            . '</tr>';
    }
    $tbl_voti .= '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_voti, 0, 1, false, true);
    $pdf->ln(10);

    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', $labels['txt1'], 0, 1, false, true, 'C');
    $pdf->ln(2);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', '<table cellpadding="2" border="0.1px">'
            . '<tr>'
                . '<td>PRIMO QUADRIMESTRE</td>'
                . '<td>SECONDO QUADRIMESTRE</td>'
            . '</tr>'
            . '<tr>'
                . '<td>'.$giudizi['p1'].'</td>'
                . '<td>'.$giudizi['p2'].'</td>'
            . '</tr>'
        . '</table>', 0, 1, false, true);
    $pdf->ln(10);

    $tbl_att = '<table cellpadding="3" border="0.1px" style="line-height: 16px;">'
            . '<tr>'
                . '<td width="25%">Note<br>'
                . $note
                . '</td>'
                . '<td width="75%" style="line-height: 16px;">'
                    . 'ATTESTATO<br>'
                    . 'Visti la valutazione e il giudizio finale deliberati dal Consiglio di Classe<br><br>'
                    . $labels['esito']
                    . '<br><br><div align="right">FIRMA DEL COORDINATORE DIDATTICO</div>'
            . '</td>'
            . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_att, 0, 1, false, true);
    //}}} </editor-fold>
    //}}} </editor-fold>
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Registro voti ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . "_".$id_classe."_" . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Registro voti ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Registro voti inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
