<?php
/*
 * Tipo: Pagella Fine Anno Scuola Secondaria di II Grado in A3 - Barbarigo
 * Nome: ss_12345_finale_A3_barbarigo_pd_01
 * Richiesta da: Barbarigo PD
 * Data: 30/05/2019
 *
 * Materie particolari:
 *
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'ss_12345_finale_A3_barbarigo_pd_01', 'Pagella Fine Anno Scuola Secondaria di II Grado', 1, 'barbarigo-pd superiori', 2);
 */

$stampa_personalizzata = 'SI';
$orientamento = 'L';
$formato = 'A3';
$periodo_pagella = 'finale';

# modifiche da apportare

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati">
    // -- PARAMETRI
    $anno_scolastico_corrente = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $id_classe = $parametri_stampa['id_classe'];
    $classe = $studente['classe'];
    $sezione = $studente['sezione'];
    $data_attestato = $parametri_stampa['data_attestato_day'] . '/' . $parametri_stampa['data_attestato_month'] . '/' . $parametri_stampa['data_attestato_year'];
    $esclusione_studenti_sospesi = $parametri_stampa['esclusione_studenti_sospesi'];
//    if (
//            ($esclusione_studenti_sospesi == 'SI' && strpos(strtolower($studente['esito']), 'giudizio sospeso') !== false)
//
//        ) {
//        return 'NO';
//    }
    $orientamento = $parametri_stampa['orientamento'];

    $a = explode('/', $anno_scolastico_corrente);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];

    $id_studente = (int) $studente['id_studente'];
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    // Estrazione voti
    $voti_pagella_primo_quadrimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $studente['id_studente']);
    $voti_pagella_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);
//    echo '<pre>', print_r($voti_pagella_finale);

    $arr_voti_finale = [];
    $arr_voti_finale_opt = [];
    $arr_voti_giudizi = [];
    $religione = [];
    $capacita_relazionale = [];
    $annotazioni = [];
    $materie_IQ_da_recuperare = '';
    $materie_IIQ_da_recuperare = '';
    $materie_IIQ_recuperate = '';
    $materie_IQ_recuperate = '';
    $materie_opzionali = '';
    $scrutinato = false;
    $monteore_finale = 0;
    $assenze_finale = 0;
    $da_giudizio_sospeso = 'NO';
    $recuperi_inseriti = 'NO';
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $monteore_finale += $voti_pagella_finale[$id_materia]['monteore_totale'];
        $assenze_finale += $voti_pagella_finale[$id_materia]['ore_assenza'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
               !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA','OPZIONALE', 'RELIGIONE'])
//               ||
//               ($studente['esonero_religione'] == 1 && $materia['tipo_materia'] == 'ALTERNATIVA')
                )
            )
        {
            if (strpos($materia['descrizione'], 'GIUDIZIO') === false)
            {
                $arr_voti_finale[$id_materia]['id_materia'] = $id_materia;
                $arr_voti_finale[$id_materia]['descrizione'] = strtoupper( $materia['descrizione'] );
                $arr_voti_finale[$id_materia]['primo_quadrimestre'] = $voti_pagella_primo_quadrimestre[$id_materia];
                $arr_voti_finale[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];

                if ($voti_pagella_primo_quadrimestre[$id_materia]['tipo_recupero'] != '') {
                    $materie_IQ_da_recuperare .= $materia['descrizione'] . ', ';

                    if (
            $voti_pagella_primo_quadrimestre[$id_materia]['esito_recupero'] != 'NO'
            && $voti_pagella_primo_quadrimestre[$id_materia]['esito_recupero'] != 'ASSENTE') {
                        $materie_IQ_recuperate .= $materia['descrizione'] . ', ';
                    }
                }

                if ($voti_pagella_finale[$id_materia]['tipo_recupero'] != '') {

                    $materie_IIQ_da_recuperare .= $materia['descrizione'] . ', ';

                    if (
                        $voti_pagella_finale[$id_materia]['esito_recupero'] != ''
                            &&
                        $voti_pagella_finale[$id_materia]['esito_recupero'] != 'NO'
                            &&
                        $voti_pagella_finale[$id_materia]['esito_recupero'] != 'ASSENTE') {
                        $materie_IIQ_recuperate .= $materia['descrizione'] . ', ';
                    }
                }
            }
        }

        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $religione['id_materia'] = $id_materia;
            $religione['descrizione'] = $materia['descrizione'];

            if ($voti_pagella_primo_quadrimestre[$id_materia]['voto_pagellina'] && $voti_pagella_primo_quadrimestre[$id_materia]['voto_pagellina'] !== '')
            {
                $religione['primo_quadrimestre']['voti'] = $voti_pagella_primo_quadrimestre[$id_materia];
            }
            if ($voti_pagella_finale[$id_materia]['voto_pagellina'] && $voti_pagella_finale[$id_materia]['voto_pagellina'] !== '')
            {
                $religione['finale']['voti'] = $voti_pagella_finale[$id_materia];
            }
        }

        // Materie opzionali
        if ($materia['in_media_pagelle'] != 'NV' &&
                in_array($materia['tipo_materia'], ['OPZIONALE']))
        {
            $materie_opzionali .= strtoupper($materia['descrizione']). ', ';

            $arr_voti_finale_opt[$id_materia]['id_materia'] = $id_materia;
            $arr_voti_finale_opt[$id_materia]['descrizione'] = strtoupper($materia['descrizione']);
            $arr_voti_finale_opt[$id_materia]['opzionale'] = 'SI';

            if ($voti_pagella_primo_quadrimestre[$id_materia]['voto_pagellina'] && $voti_pagella_primo_quadrimestre[$id_materia]['voto_pagellina'] !== '') {
                $arr_voti_finale_opt[$id_materia]['primo_quadrimestre'] = $voti_pagella_primo_quadrimestre[$id_materia];
            }

            if ($voti_pagella_finale[$id_materia]['voto_pagellina'] && $voti_pagella_finale[$id_materia]['voto_pagellina'] !== '') {
                $arr_voti_finale_opt[$id_materia]['finale'] = $voti_pagella_finale[$id_materia];
            }
        }

        // Giudizi globali I e II Quadrimestre
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {

            $capacita_relazionale['id_materia'] = $id_materia;
            $capacita_relazionale['descrizione'] = $materia['descrizione'];
            if ($voti_pagella_primo_quadrimestre[$id_materia]['voto_pagellina'] && $voti_pagella_primo_quadrimestre[$id_materia]['voto_pagellina'] !== '')
            {
                $capacita_relazionale['primo_quadrimestre']['voti'] = $voti_pagella_primo_quadrimestre[$id_materia];
            }
            if ($voti_pagella_finale[$id_materia]['voto_pagellina'] && $voti_pagella_finale[$id_materia]['voto_pagellina'] !== '')
            {
                $capacita_relazionale['finale']['voti'] = $voti_pagella_finale[$id_materia];
            }

            $arr_voti_giudizi['giudizio_primo_quadrimestre']['voto']['descrizione'] = $materia['descrizione'];
            $arr_voti_giudizi['giudizio_primo_quadrimestre']['voto'] = $voti_pagella_primo_quadrimestre[$id_materia];
            foreach ($voti_pagella_primo_quadrimestre[$id_materia]['campi_liberi'] as $campo_libero) {
                if (strpos(strtoupper($campo_libero['nome']), 'VALUTAZIONE') !== false) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $arr_voti_giudizi['giudizio_primo_quadrimestre']['giudizio'] .= $value . " ";
                    }
                }
                if (stripos($campo_libero['nome'], 'annotazioni') !== false) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $annotazioni['p1'] .= $value;
                    }
                }
            }

            $arr_voti_giudizi['giudizio_finale']['voto']['descrizione'] = $materia['descrizione'];
            $arr_voti_giudizi['giudizio_finale']['voto'] = $voti_pagella_finale[$id_materia];
            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero) {
                if (strpos(strtoupper($campo_libero['nome']), 'VALUTAZIONE') !== false) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $arr_voti_giudizi['giudizio_finale']['giudizio'] .= $value . " ";
                    }
                }
                if (stripos($campo_libero['nome'], 'annotazioni') !== false) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $annotazioni['p2'] .= $value;
                    }
                }
            }
        }

        // scrutinio
        if ($voti_pagella_finale[$id_materia]['voto_pagellina'] > 0 && $voti_pagella_finale[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }

        // recuperi inseriti
        if ($materia['in_media_pagelle'] != 'NV')
        {
            if ($voti_pagella_finale[$id_materia]['tipo_recupero']!=''
                    ||
                $voti_pagella_finale[$id_materia]['esito_recupero']!='')
            {
                $recuperi_inseriti = 'SI';
            }
        }
    }

    if($recuperi_inseriti == 'SI')
    {
        $da_giudizio_sospeso = 'SI';
    }
    if ($esclusione_studenti_sospesi == 'NI' &&
            (
            strpos(strtolower($studente['esito']), 'giudizio sospeso') !== false
                ||
            $da_giudizio_sospeso != 'SI'
            )
        ) {
        return 'NO';
    }
    if ($esclusione_studenti_sospesi == 'SI' &&
        $da_giudizio_sospeso == 'SI'
        ) {
        return 'NO';
    }

    $arr_voti_finale+= $arr_voti_finale_opt;
    $materie_opzionali =  substr($materie_opzionali, 0, -2);
    // aggiungo religione in coda ai voti se popolata
    if (!empty($religione))
    {
        $arr_voti_finale[$religione['id_materia']]['id_materia'] = $religione['id_materia'];
        $arr_voti_finale[$religione['id_materia']]['descrizione'] =  strtoupper( $religione['descrizione'] );
        $arr_voti_finale[$religione['id_materia']]['primo_quadrimestre'] = $religione['primo_quadrimestre']['voti'];
        $arr_voti_finale[$religione['id_materia']]['finale'] = $religione['finale']['voti'];
    }
    // aggiungo capacità relazionale in coda ai voti se popolata
    if (!empty($capacita_relazionale))
    {
        $arr_voti_finale[$capacita_relazionale['id_materia']]['id_materia'] = $capacita_relazionale['id_materia'];
        $arr_voti_finale[$capacita_relazionale['id_materia']]['descrizione'] =  strtoupper( $capacita_relazionale['descrizione'] );
        $arr_voti_finale[$capacita_relazionale['id_materia']]['primo_quadrimestre'] = $capacita_relazionale['primo_quadrimestre']['voti'];
        $arr_voti_finale[$capacita_relazionale['id_materia']]['finale'] = $capacita_relazionale['finale']['voti'];
    }
    // Voti totali
    $arr_voti_tot = $arr_voti_finale;

    //{{{ <editor-fold defaultstate="collapsed" desc="Validazione Anno">
    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = ($scrutinato) ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }
    //}}} </editor-fold>

    // CURRICULUM
    $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
    $anno_scolastico_corrente = $anno_inizio . "/" . $anno_fine;
    $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
    //estraggo i dati di provenienza e di quante volte è ripetente
    $provenienza = '';
    for($cont_curr=0; $cont_curr <count($curriculum_studente); $cont_curr++)
    {
        if ($curriculum_studente[$cont_curr]['esito'] != 'Iscritto')
        {
            if($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente)
            {
                if($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
                {
                        $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                }
                else
                {
                        $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
                }
            }
            else {
                $classe_attuale = $curriculum_studente[$cont_curr]['classe'];

                if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_corrente
                    && $classe_attuale == $curriculum_studente[$cont_curr]['classe']
                    && $studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola']
                    && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
                {
                    if (($curriculum_studente[$cont_curr]['esito'] == 'Iscritto') || ($cont_curr <count($curriculum_studente) -1)) {

                        // Nel caso nel curriculum non sia stata inserita la scuola di provenienza
                        if($curriculum_studente[$cont_curr]['nome_scuola'] != "") {
                            $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                        } else {
                            $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
                        }
                    }
                }
            }
        }
    }
    //

    // titolo di ammissione
    $esito_precedente = estrai_esito_e_volte_iscritto_da_curriculum_studente((int) $studente['id_studente'], $anno_scolastico_precedente);
    $titolo_ammissione = $esito_precedente['esito'];

    // numero volte iscritto alla classe
    $numero_volte_iscritto_studente_classe = $studente['mat_esito']['numero_volte_iscritto'];
    $numero_volte_iscritto_studente_classe_trad = '';
    switch ($numero_volte_iscritto_studente_classe) {
        case 1: $numero_volte_iscritto_studente_classe_trad = 'PRIMA';break;
        case 1: $numero_volte_iscritto_studente_classe_trad = 'SECONDA';break;
        case 1: $numero_volte_iscritto_studente_classe_trad = 'TERZA';break;
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Dizionario">
// Dizionario temporaneo
    $labels = [
        "ministero"                 => "MINISTERO DELL'ISTRUZIONE E DEL MERITO",

        "scuola_desc_1_tit"         => "Istituzione<br>scolastica",
        "scuola_desc_1_val"         => "COLLEGIO VESCOVILE BARBARIGO<br>PADOVA",
        "scuola_desc_2_tit"         => "Scuola<br>Secondaria di<br>Secondo Grado",

        "titolo_docum"              => "Pagella Scolastica",
        "anagrafica_titolo"         => "Dati anagrafici dello studente",
        "anagrafica_cognome"        => "COGNOME",
        "anagrafica_nome"           => "NOME",
        "anagrafica_cf"             => "CODICE FISCALE",
        "anagrafica_nome"           => "NOME",
        "anagrafica_data_nascita"   => "DATA DI NASCITA",
        "anagrafica_comune_nascita" => "COMUNE DI NASCITA",
        "anagrafica_prov"           => "PROV. O STATO ESTERO",

        "dati_titolo"               => "Posizione scolastica dello studente",
        "dati_anno"                 => "Anno Scolastico ",
        "dati_registro"             => "N. Registro Generale",
        "dati_classe"               => "Classe",
        "dati_sezione"              => "Sezione",
        "dati_provenienza"          => "Provenienza",
        "dati_ammissione"           => "Titolo di Ammissione <sup>(1)</sup>",
        "dati_indirizzo"            => "Indirizzo",
        "iscrizione_n_volte"        => "Iscrizione per la " . $numero_volte_iscritto_studente_classe_trad .  " volta <sup>(2)</sup>",
        "dati_firma_dirigente"      => "Il Dir. Serv. Gen. E Amm <sup>(3)</sup>",

        ///
        "scrutinio_tit_1"           => "VALIDITÀ DELL’ANNO SCOLASTICO",
        "scrutinio_tit_2"           => "(Art. 14, comma 7 del D.P.R. n. 122/2009)",
        "scrutinio_desc"            => "Ai fini della validità dell’anno e dell’ammissione allo scrutinio finale, l’alunn||min_oa||:",
        "scrutinio_desc_val_1"      => "ha frequentato per almeno tre quarti dell’orario annuale;",
        "scrutinio_desc_val_2"      => "non ha frequentato per almeno tre quarti dell’orario annuale, ma ha usufruito della deroga;",
        "scrutinio_desc_val_3"      => "non ha frequentato per almeno tre quarti dell’orario annuale.",
        "finale_titolo"             => "RISULTATO FINALE",
        "finale_desc"               => "Visti i risultati conseguiti si dichiara che <sup>(7)</sup>",
        "finale_alunno"             => "l'alunn||min_oa|| è ",
        "finale_firma_genitore"     => "Il (i) genitore (i) o chi ne fa le veci",
        "finale_firma_dirigente"        => "Il Dirigente Scolastico <sup>(3)</sup>",


        "note_titolo"               => "NOTE",
        "note_tot"                  =>
            "<table>
                <tr>
                  <td width=\"4%\"><i>(1)</i></td>
                  <td width=\"96%\">PROMOZIONE; IDONEITA’; QUALIFICA; Idoneità all’ultima classe a seguito di esito positivo dell’esame preliminare e mancato superamento esami di Stato.</td>
                </tr>
                <tr>
                  <td><i>(2)</i></td>
                  <td>PRIMA; SECONDA; TERZA.</td>
                </tr>
                <tr>
                  <td><i>(3)</i></td>
                  <td>La firma è omessa ai sensi dell’Art. 3, D.to Lgs. 12/02/1993, n. 39.</td>
                </tr>
                <tr>
                  <td><i>(4)</i></td>
                  <td>“Il riquadro può essere utilizzato anche:<br>
              - per l’annotazione delle materie Art. 4, comma 6 del D.P.R. 122/2009;<br>
              - per l’annotazione prevista dall’Art. 9, comma 1 del D.P.R. 122/2009;<br>
              - per eventuali altre annotazioni o indicazione di rilascio di certificazione”.</td>
                </tr>
                <tr>
                  <td><i>(5)</i></td>
                  <td>Per le classi terminali indicare: ammess||min_oa|| agli esami – non ammess||min_oa|| agli esami.</td>
                </tr>
                <tr>
                  <td><i>(6)</i></td>
                  <td>Solo per esami di qualifica professionale.</td>
                </tr>
                <tr>
                  <td><i>(7)</i></td>
                  <td>promoss||min_oa|| – non promoss||min_oa||.<br>Per le classi terminali indicare: ammess||min_oa|| – non ammess||min_oa||.</td>
                </tr>
          </table>",
        "note_finale"               => "<i>\"Il presente documento non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi\"</i>",

        "annotazioni_titolo"        => "<b>ANNOTAZIONI</b> <sup>(4)</sup>"
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Verifica ammissione scrutinio">
    // Validazione ammissione scrutinio
    switch ($validazione_anno) {
        case 'SI':
            $scrutinio_testo = $labels['scrutinio_desc_val_1'] ;
            break;
        case 'DEROGA':
            $scrutinio_testo = $labels['scrutinio_desc_val_2'];
            break;
        case 'NO':
            $scrutinio_testo = $labels['scrutinio_desc_val_3'];
            break;
    }
    $esito = $labels['finale_alunno'] . $studente['esito']; # l'alunno/a è stato/a Promosso/a (!= ammesso ammessa)
    //}}} </editor-fold>

    // PDF parametri
    $font = 'helvetica';
    $font_dim = 11;

    $pdf->AddPage($orientamento);
    $pdf->SetAutoPageBreak("off", 1);
    $y_start = $pdf->getY();
    $page_width = $pdf->getPageWidth();
    $page_height = $pdf->getPageHeight();

    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];

    $half_page = $page_width / 2;
    $w_page_A3 = $page_width - $m_destro - $m_sinistro;
    $h_page_A3 = $page_height - $m_top ;
    $w_page_parts = $half_page - $m_destro - $m_sinistro;
    $x_start2 = $half_page + $m_sinistro;


    $pdf->Line($half_page, 0, $half_page, $page_height); # da togliere

    //   PAGINA 1
    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1: Informazioni Pagella, risultati, note">
    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1, Parte 1: Risultato finale e note">
    //{{{ <editor-fold defaultstate="collapsed" desc="Scrutinio">
    $tbl_scrut = '<table>';
    $scrut_label_1 = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . $labels['scrutinio_desc_val_1'];
    $scrut_label_2 = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' . $labels['scrutinio_desc_val_2'];
    $scrut_label_3 = '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' .  $labels['scrutinio_desc_val_3'];
    switch ($validazione_anno) {
        case 'SI':
            $scrut_label_1 = '&nbsp;X&nbsp;&nbsp;' . $scrut_label_1;
            break;
        case 'DEROGA':
            $scrut_label_2 = '&nbsp;X&nbsp;&nbsp;' . $scrut_label_2;
            break;
        case 'NO':
            $scrut_label_3 = '&nbsp;X&nbsp;&nbsp;' . $scrut_label_3;
            break;
    }
    $scrutinio_tot = $labels['scrutinio_desc'] . '<br>' . $scrut_label_1 . '<br>' . $scrut_label_2 . '<br>' . $scrut_label_3 . '<br>' . '<font size="-2">'. '<br> * barrare la voce che interessa'.'</font>';
    $y_attestato_box = $pdf->GetY();
    $pdf->Ln(4);
    $pdf->SetFont($font, 'B', $font_dim+4);
    $pdf->Cell($w_page_parts, 0, $labels['scrutinio_tit_1'], 0, 1, 'C');
    $pdf->SetFont($font, 'B', $font_dim+1);
    $pdf->Cell($w_page_parts, 0, $labels['scrutinio_tit_2'], 0, 1, 'C');
    $pdf->Ln(4);
    $pdf->SetCellHeightRatio(2);
    $pdf->SetFont($font, '', $font_dim);
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $scrutinio_tot, 0, 1);
    $pdf->Ln(2);
    $pdf->writeHTMLCell($w_page_parts, $pdf->GetY()-$y_attestato_box, '', $y_attestato_box, '', 1, 1);
    $pdf->Ln(10);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Risultato finale">
    $pdf->SetFont($font, 'B', $font_dim + 2);
    $pdf->Cell($w_page_parts, 0, $labels['finale_titolo'], 0, 1, 'C');
    $pdf->SetCellHeightRatio(1.25);
    $pdf->Ln(5);
    $pdf->SetFont($font, '', $font_dim);
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $labels['finale_desc'], 0, 1, false, true, 'C');
    $pdf->Ln(1);
    $pdf->Cell($w_page_parts, 0, $esito, 0, 1, 'C');
    $pdf->Ln(5);
    $pdf->Cell($w_page_parts, 0, $studente['descrizione_comuni'] . ', ' . $data_attestato, 0, 1);
    $pdf->Ln(17);
    $tbl_firme =
        '<table>
            <tr>
                <td width="35%"></td>
                <td width="30%"></td>
                <td width="35%" align="center" style="border-bottom-width:0.1px;">' . $studente['nome_dirigente'] .'</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td align="center"><font size="-2">'. $labels['finale_firma_dirigente'] . '</font></td>
            </tr>
        </table>';
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $tbl_firme, 0, 1);
    $pdf->Ln(12);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Note">
    $curr_x_ln = $pdf->GetX() + 5;
    $curr_y_ln = $pdf->GetY();
    $pdf->Line($curr_x_ln, $curr_y_ln, $curr_x_ln + $w_page_parts-5, $curr_y_ln);
    $pdf->Ln(4);
    $pdf->SetFont($font, 'B', $font_dim + 1);
    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $labels['note_titolo'], 0, 1, false, true, 'C');
    $pdf->Ln(4);
    $pdf->SetCellHeightRatio(1.25);
    $pdf->SetFont($font, '', $font_dim-3);
    $pdf->writeHTMLCell($w_page_parts-20, 0, $m_sinistro+15, '', $labels['note_tot'], 0, 1, false, true, 'L');
    $pdf->SetFont($font, '', $font_dim);
    $pdf->SetCellHeightRatio(1.25);
    $pdf->Ln(8);
//    $pdf->writeHTMLCell($w_page_parts, 0, '', '', $labels['note_finale'], 0, 1, false, true, 'C');
    //}}} </editor-fold>
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1, Parte 2: Informazioni Pagella">
    //{{{ <editor-fold defaultstate="collapsed" desc="Intestazione">
    $pdf->setY($m_top);
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x_start2 + $w_page_parts/2-10, $m_top, 20, 20, '', '', '', false, 300, '');
    $pdf->SetXY($x_start2, $m_top+25);
    $pdf->SetFont($font, 'I', $font_dim + 8);
    $pdf->CellFitScale(0, 0, $labels['ministero'], 0, 1, 'C');
    $pdf->ln(8);

    $tbl_scuola_desc_1 =
        '<table border="1" style="height:40px;">
            <tr>
                <td width="25%"><b><font size="-2"><br>' . $labels['scuola_desc_1_tit'] . '<br></font></b></td>
                <td width="75%">&nbsp;<br><b>' . $labels['scuola_desc_1_val'] . '</b><br></td>
            </tr>
        </table>';
//    $pdf->SetFont($font, '', $font_dim + 3);
//    $pdf->SetX($x_start2);
//    $pdf->writeHTMLCell(0, 0, '', '', $tbl_scuola_desc_1, 0, 1, false, true, 'C');

//    $pdf->Ln(5);
    $desc_tipo_scuola = $studente['descrizione_tipi_scuole'];
    if (strpos(strtolower($studente['descrizione_tipi_scuole']), 'liceo classico') === true
            ||
            strpos(strtolower($studente['descrizione_tipi_scuole']), 'ginnasio') === true){
        $desc_tipo_scuola = 'GINNASIO - LICEO CLASSICO';
    }
    if ($studente['codice_indirizzi']=='LSA') { $desc_tipo_scuola = 'LICEO SCIENTIFICO OPZ. SCIENZE APPLICATE';}
    if ($studente['codice_indirizzi']=='ITE') { $desc_tipo_scuola = 'TECNICO ECONOMICO BIENNIO COMUNE';}
//    $scuola_desc_2_val = $desc_tipo_scuola.' PARITARIO "BARBARIGO"<br>'
//            . 'Cod.Min. '.$studente['codice_meccanografico'].'<br>'
//            . 'VIA ROGATI, 17 - 35122 PADOVA (PD)'; #
//    $tbl_scuola_desc_2 =
//        '<table border="1">
//            <tr>
//                <td width="25%"><b><font size="-2"><br>' . $labels['scuola_desc_2_tit'] . '<br></font></b></td>
//                <td width="75%"><font size="-2">&nbsp;</font><br><b>' . $scuola_desc_2_val . '</b></td>
//            </tr>
//        </table>';
//    $pdf->SetFont($font, '', $font_dim + 3);
//    $pdf->SetX($x_start2);
//    $pdf->writeHTMLCell(0, 0, '', '', $tbl_scuola_desc_2, 0, 1, false, true, 'C');
//file_put_contents('/tmp/debug.txt', print_r($studente,true));
    $desc_scuola = "FONDAZIONE GIROLAMO BORTIGNON
ISTITUTO BARBARIGO - $desc_tipo_scuola
Via dei Rogati 17
35122 Padova
cod. mecc. {$studente['codice_meccanografico']}";
    $pdf->SetFont($font, '', $font_dim + 3);
    $pdf->SetX($x_start2);
    $pdf->MultiCell(0, 35, $desc_scuola, 1, 'C', false, 1, '', '', true, 0, false, true, 35, 'M');

    $pdf->Ln(6);
    $pdf->SetFont($font, 'B', $font_dim + 12);
    $pdf->SetX($x_start2);
    $pdf->Cell(0, 0, $labels['titolo_docum'], 0, 1, 'C');
    $pdf->Ln(6);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Anagrafica">
    $y_attestato_box = $pdf->GetY();
    $pdf->setCellHeightRatio(1.25);
    $pdf->SetFont($font, '', $font_dim);
    $tbl_anagrafica =
        '<table style="border:0.1px solid black" cellpadding="2">
            <tr>
                <td align="center" colspan="3"><b>' . $labels['anagrafica_titolo'] . '</b></td>
            </tr>

            <tr>
                <td width="36%"><b>' . $studente['cognome'] . '</b></td>
                <td width="36%"><b>' . $studente['nome'] . '</b></td>
                <td width="27%"><b>' . $studente['codice_fiscale'] . '</b></td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['anagrafica_cognome'] . '</font></td>
                <td><font size="-2">' . $labels['anagrafica_nome'] . '</font></td>
                <td><font size="-2">' . $labels['anagrafica_cf'] . '</font></td>
            </tr>
            <tr>
                <td align="center" colspan="3">' . '' . '</td>
            </tr>
            <tr>
                <td><b>' . $studente['data_nascita_ext'] . '</b></td>
                <td><b>' . $luogo_nascita . '</b></td>
                <td><b>' . $provincia_nascita . '</b></td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['anagrafica_data_nascita'] . '</font></td>
                <td><font size="-2">' . $labels['anagrafica_comune_nascita'] . '</font></td>
                <td><font size="-2">' . $labels['anagrafica_prov'] . '</font></td>
            </tr>
        </table>';
    $pdf->writeHTMLCell(0, 0, $x_start2, '', $tbl_anagrafica, 0, 1);
    $pdf->Ln(8);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="dati studente">
    // titolo di ammissione
    $esito_precedente = estrai_esito_e_volte_iscritto_da_curriculum_studente((int) $studente['id_studente'], $anno_scolastico_precedente);
    $titolo_ammissione = $esito_precedente['esito'];
    $tbl_dati = # tablre border to style="border:1px solid"
        '<table  style="border:0.1px solid black" cellpadding="3">
            <tr>
                <td width="50%"><b>' . $labels['dati_titolo'] . '</b></td>
                <td width="50%" align="center"><b>' . $labels['dati_anno'] .'</b>' . $anno_scolastico_corrente . '</td>
            </tr>

            <tr>
                <td width="25%">&nbsp;' . $studente['matricola'] . '</td>
                <td width="25%">' . $classe. '^ '.ucwords(strtolower($studente['descrizione_indirizzi'])) . '</td>
                <td width="20%">' . $sezione. '</td>
                <td width="30%">' . ($anno_scolastico_corrente == '2020/2021' ? '' : $provenienza) . '</td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_registro'] . '</font></td>
                <td><font size="-2">' . $labels['dati_classe'] . '</font></td>
                <td><font size="-2">' . $labels['dati_sezione'] . '</font></td>
                <td><font size="-2">' . ($anno_scolastico_corrente == '2020/2021' ? '' : $labels['dati_provenienza']) . '</font></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="100%">&nbsp;' . $titolo_ammissione . '</td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_ammissione'] . '</font></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="40%">&nbsp;' . ucwords(strtolower($studente['descrizione_indirizzi'])) . '</td>
                <td width="60%" align="center">' . $labels['iscrizione_n_volte'] . '</td>
            </tr>
            <tr>
                <td><font size="-2">' . $labels['dati_indirizzo'] . '</font></td>
                <td></td>
            </tr>

            <tr>
                <td>&nbsp;</td>
            </tr>

            <tr>
                <td width="100%" align="right"><font size="-2">' . $labels['dati_firma_dirigente'] . '</font></td>
            </tr>
        </table>';
    $pdf->writeHTMLCell(0, 0, $x_start2, '', $tbl_dati, 0, 1);
    //}}} </editor-fold>
    //}}} </editor-fold>
    //}}} </editor-fold>


    //   PAGINA 2
    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 2: Tabelle Voti">
    $pdf->AddPage($orientamento);
    $pdf->Line($half_page, 0, $half_page, $page_height); # da togliere?
    //{{{ <editor-fold defaultstate="collapsed" desc="Tabella VOTI I e II Quadrimestre">
    $voti_tot_somma_finale = 0;
    $numero_materie = 0;
    $pdf->SetFont($font, '', $font_dim);
    // Intestazione Tabella
    $cp = 3; if ($studente['id_studente'] == 1002516) {$cp = 1;}
//    $tbl =
//        '<table cellpadding="'.$cp.'" align="center">
//            <tr >
//              <td rowspan="3" width="14%" border="1">&nbsp;<br>DISCIPLINE</td>
//              <td rowspan="40" width="1%"></td>
//              <td colspan="5" width="33%" border="1">VALUTAZIONE PERIODICA</td>
//              <td rowspan="40" width="4%"></td>
//
//              <td rowspan="3" width="14%" border="1">&nbsp;<br>DISCIPLINE</td>
//              <td rowspan="40" width="1%"></td>
//              <td colspan="2" width="16%" border="1">SCRUTINIO FINALE</td>
//
//              <td rowspan="40" width="1%" ></td>
//              <td rowspan="40" border="1" width="16%"></td>
//            </tr>
//
//            <tr align="center">
//              <td colspan="5" border="1">Trimestre</td>
//              <td rowspan="2" width="9%" border="1">VOTO UNICO<sup>(4)</sup><br>(in lettere)</td>
//              <td rowspan="2" width="7%" border="1">Totale<br><font size="-3">ore</font><br>assenza</td>
//            </tr>
//            <tr align="center" >
//              <td border="1">Scritto</td>
//              <td border="1">Orale</td>
//              <td border="1">Pratico</td>
//              <td border="1">Voto Unico</td>
//              <td border="1">Ass.</td>
//            </tr>
//            <tr> <td></td> <td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>';
    $tbl =
        '<table cellpadding="'.$cp.'" align="center">
            <tr >
              <td rowspan="3" width="29%" border="1">&nbsp;<br>DISCIPLINE</td>
              <td rowspan="40" width="1%"></td>
              <td colspan="5" width="18%" border="1">VALUTAZIONE PERIODICA</td>
              <td rowspan="40" width="4%"></td>

              <td rowspan="3" width="14%" border="1">&nbsp;<br>DISCIPLINE</td>
              <td rowspan="40" width="1%"></td>
              <td colspan="2" width="16%" border="1">SCRUTINIO FINALE</td>

              <td rowspan="40" width="1%" ></td>
              <td rowspan="40" border="1" width="16%"></td>
            </tr>

            <tr align="center">
              <td colspan="2" width="18%" border="1">Trimestre</td>
              <td rowspan="2" width="10%" border="1">VOTO UNICO<sup>(4)</sup><br>(in lettere)</td>
              <td rowspan="2" width="6%" border="1">Totale<br><font size="-3">ore</font><br>assenza</td>
            </tr>
            <tr align="center" >
              <td border="1">Voto Unico</td>
              <td border="1">Ass.</td>
            </tr>
            <tr> <td></td> <td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr>';
    $tipo_visualizzazione_IQ = identifica_periodo_tipo_voto(7, (int) $studente['id_classe']);
    foreach ($arr_voti_tot as $id_materia_voti)
    {
        $voto_tradotto_IQ_scritto = '';
        $voto_tradotto_IQ_orale = '';
        $voto_tradotto_IQ_pratico = '';
        $voto_tradotto_IQ_unico = '';
        $voto_tradotto_IIQ = '';


        foreach ($id_materia_voti['primo_quadrimestre']['significati_voto'] as $significato)
        {
            if ( $id_materia_voti['id_materia'] != $capacita_relazionale['id_materia'] ) {
                if (
                        ($tipo_visualizzazione_IQ == 'voto_singolo')
                        or ( $tipo_visualizzazione_IQ == 'personalizzato' and $id_materia_voti['primo_quadrimestre']['tipo_voto_personalizzato'] == '1')
                    )
                {
                    if ($significato['voto'] == $id_materia_voti['primo_quadrimestre']['voto_pagellina'])
                    {
                        $voto_tradotto_IQ_unico = decode(strtoupper($significato['valore_pagella']));
                    }
                }
                else
                {
                    if ($significato['voto'] == $id_materia_voti['primo_quadrimestre']['voto_scritto_pagella'])
                    {
                        $voto_tradotto_IQ_scritto = decode(strtoupper($significato['valore_pagella']));
                    }

                    if ($significato['voto'] == $id_materia_voti['primo_quadrimestre']['voto_orale_pagella'])
                    {
                        $voto_tradotto_IQ_orale = decode(strtoupper($significato['valore_pagella']));
                    }

                    if ($significato['voto'] == $id_materia_voti['primo_quadrimestre']['voto_pratico_pagella'])
                    {
                        $voto_tradotto_IQ_pratico = decode(strtoupper($significato['valore_pagella']));
                    }
                }
            }
            else {
                if ($significato['voto'] == $id_materia_voti['primo_quadrimestre']['voto_pagellina'])
                {
                    $voto_tradotto_IQ_capacita_relazionale = decode(strtoupper($significato['valore_pagella']));
                }
            }
        }
        foreach ($id_materia_voti['finale']['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $id_materia_voti['finale']['voto_pagellina'])
            {
                $voto_tradotto_IIQ = decode(strtoupper($significato['valore_pagella']));
            }
        }

        $cella_descrizione_materia = $id_materia_voti['descrizione'];
        $ore_assenza_IQ = stampa_ore_o_minuti( $id_materia_voti['primo_quadrimestre']['ore_assenza'] );
        $ore_assenza_IIQ = stampa_ore_o_minuti( $id_materia_voti['finale']['ore_assenza'] );

        if ($id_materia_voti['finale']['in_media_pagelle'] == 'SI' && $id_materia_voti['finale']['voto_pagellina'] != '') {
                $voti_tot_somma_finale += $id_materia_voti['finale']['voto_pagellina'];
                $numero_materie++;
        }

        if ( $id_materia_voti['id_materia'] != $capacita_relazionale['id_materia'] ) {

            if (
                $voto_tradotto_IIQ != ''
                    ||
                $voto_tradotto_IQ_scritto != ''
                    ||
                $voto_tradotto_IQ_orale != ''
                    ||
                $voto_tradotto_IQ_pratico != ''
                    ||
                $voto_tradotto_IQ_unico != ''
                )
            {

//                $tbl .= '
//                <tr>
//                    <td align="left" border="1">' . ($cella_descrizione_materia=='LEADERSHIP/COMUNICAZIONE' ? '<font size="-1">' . $cella_descrizione_materia . '</font>' : $cella_descrizione_materia) . '</td>
//                    <td border="1">' . '<font size="-1">' . $voto_tradotto_IQ_scritto . '</font>' . '</td>
//                    <td border="1">' . '<font size="-1">' . $voto_tradotto_IQ_orale . '</font>' . '</td>
//                    <td border="1">' . '<font size="-1">' . $voto_tradotto_IQ_pratico . '</font>' . '</td>
//                    <td border="1">' . '<font size="-1">' . $voto_tradotto_IQ_unico . '</font>' . '</td>
//                    <td border="1">' . '<font size="-1">' . $ore_assenza_IQ . '</font>' . '</td>
//
//                    <td align="left" border="1">' . ($cella_descrizione_materia=='LEADERSHIP/COMUNICAZIONE' ? '<font size="-1">' . $cella_descrizione_materia . '</font>' : $cella_descrizione_materia) . '</td>
//                    <td border="1">' . '<font size="-1">' . $voto_tradotto_IIQ . '</font>' . '</td>
//                    <td border="1">' . '<font size="-1">' . $ore_assenza_IIQ . '</font>' . '</td>
//                </tr>';
                $tbl .= '
                <tr>
                    <td align="left" border="1">' . ($cella_descrizione_materia=='LEADERSHIP/COMUNICAZIONE' ? '<font size="-1">' . $cella_descrizione_materia . '</font>' : $cella_descrizione_materia) . '</td>
                    <td border="1">' . '<font size="-1">' . $voto_tradotto_IQ_unico . '</font>' . '</td>
                    <td border="1">' . '<font size="-1">' . $ore_assenza_IQ . '</font>' . '</td>

                    <td width="14%" align="left" border="1">' . ($cella_descrizione_materia=='LEADERSHIP/COMUNICAZIONE' ? '<font size="-1">' . $cella_descrizione_materia . '</font>' : $cella_descrizione_materia) . '</td>
                    <td width="10%" border="1">' . '<font size="-1">' . $voto_tradotto_IIQ . '</font>' . '</td>
                    <td width="6%" border="1">' . '<font size="-1">' . $ore_assenza_IIQ . '</font>' . '</td>
                </tr>';


//            <tr align="center">
//              <td colspan="2" width="18%" border="1">Trimestre</td>
//              <td rowspan="2" width="10%" border="1">VOTO UNICO<sup>(4)</sup><br>(in lettere)</td>
//              <td rowspan="2" width="6%" border="1">Totale<br><font size="-3">ore</font><br>assenza</td>
//            </tr>
//            <tr align="center" >
//              <td border="1">Voto Unico</td>
//              <td border="1">Ass.</td>
//            </tr>
            }
        }
        else {
            $cella_descrizione_materia = $id_materia_voti['descrizione'] ;
//            $tbl .= '
//            <tr>
//                <td align="left" border="1"><b>' . $cella_descrizione_materia . '</b></td>
//                <td colspan="5" border="1">' . $voto_tradotto_IQ_capacita_relazionale . '</td>
//                <td align="left" border="1"><b>' . $cella_descrizione_materia . '</b></td>
//                <td colspan="2" border="1">' . $voto_tradotto_IIQ . '</td>
//            </tr>';
            $tbl .= '
            <tr>
                <td align="left" border="1"><b>' . $cella_descrizione_materia . '</b></td>
                <td colspan="2" border="1">' . $voto_tradotto_IQ_capacita_relazionale . '</td>
                <td align="left" border="1"><b>' . $cella_descrizione_materia . '</b></td>
                <td colspan="2" border="1">' . $voto_tradotto_IIQ . '</td>
            </tr>';
        }
    }
    $tbl .= '</table>';
    $pdf->setCellHeightRatio( 1.65 );
    $pdf->writeHTML($tbl);
    $pdf->setCellHeightRatio( 1.25 );
    $pdf->Ln(10);
    $y_fine_tab_voti = $pdf->GetY();
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Tabella Crediti Scolastici">
    $media = round($voti_tot_somma_finale / $numero_materie, 2);
    $tot_crediti = $studente['crediti_prima'] + $studente['crediti_reintegrati_prima'] +
            $studente['crediti_seconda'] + $studente['crediti_reintegrati_seconda'] +
            $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'] +
            $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'] +
            $studente['crediti_quinta'] + $studente['crediti_reintegrati_quinta'];
    switch ($classe) {
        case 1: $crediti_anno_corso = $studente['crediti_prima'] + $studente['crediti_reintegrati_prima'];
            break;
        case 2: $crediti_anno_corso = $studente['crediti_seconda'] + $studente['crediti_reintegrati_seconda'];
            break;
        case 3: $crediti_anno_corso = $studente['crediti_terza'] + $studente['crediti_reintegrati_terza'];
            break;
        case 4: $crediti_anno_corso = $studente['crediti_quarta'] + $studente['crediti_reintegrati_quarta'];
            break;
        case 5: $crediti_anno_corso = $studente['crediti_quinta'] + $studente['crediti_reintegrati_quinta'];
            break;
    }
    $pdf->SetFont($font, '', $font_dim-2);
    $tbl_crediti = '
        <table cellpadding="6" width="100%" align="center">
            <tr>
                <td><b>CREDITO SCOLASTICO</b> <sup>(7)</sup></td>
            </tr>
            <tr>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>Media dei voti conseguiti nello scrutinio finale:</td>
            </tr>
            <tr>
                <td><font size="+2">' . $media . '</font></td>
            </tr>
            <tr>
                <td>Credito Scolastico attribuito nell’anno scolastico in corso:</td>
            </tr>
            <tr>
                <td><font size="+2">' . $crediti_anno_corso . '</font></td>
            </tr>
            <tr>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td>Totale Credito Scolastico</td>
            </tr>
            <tr>
                <td><font size="+2">' . $tot_crediti . '</font></td>
            </tr>
        </table>
        ';
    // 16% usabile
    if ($studente['classe']>2) {
        $pdf->writeHTMLCell($w_page_A3*0.16, 0, $m_destro+$w_page_A3*0.84, 41, $tbl_crediti, 1, 1);
    }
    $pdf->SetFont($font, '', $font_dim);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Annotazioni 1">
    ////{{{ <editor-fold defaultstate="collapsed" desc=" SERVE Debiti del I Quadrimestre, le Sospensioni del II Quadrimestre ( giugno) e il loro recupero ( settembre )."> TIPO RECUPERO e ESITO RECUPERO
    //}}} </editor-fold>
    $pdf->SetY($y_fine_tab_voti);

    $tot_note_debiti = $tot_note_debiti_2 = '';
    if (!empty($materie_IQ_da_recuperare)) {
        $tot_note_debiti .= 'Debiti del Trimestre: ' . substr($materie_IQ_da_recuperare, 0, -2) . '.<br><br>';
    }
    if (!empty($materie_IQ_recuperate)) {
        $tot_note_debiti .= 'Materie con debito del Trimestre recuperate: ' . substr($materie_IQ_recuperate, 0, -2) . '.<br><br>';
    }
    if (!empty($materie_IIQ_da_recuperare)) {
        $tot_note_debiti_2 .= 'Materie insufficienti del Pentamestre: ' . substr($materie_IIQ_da_recuperare, 0, -2) . '.<br><br>';
    }
    if (!empty($materie_IIQ_recuperate)) {
        $tot_note_debiti_2 .= 'Materie con debito del Pentamestre recuperate: ' . substr($materie_IIQ_recuperate, 0, -2) . '.<br><br>';
    }
    $annotazioni_materie = '<table cellpadding="5">
            <tr>
                <td align="center" style="border-bottom-width:0.1px;">' . $labels['annotazioni_titolo'] . '</td>
            </tr>
            <tr>
                <td>' . $tot_note_debiti . '<br><br>' . $annotazioni['p1'] . '</td>
            </tr>
        </table>';
    $dim_cell = $h_page_A3 - $y_fine_tab_voti;
    $pdf->writeHTMLCell($w_page_parts, $dim_cell, '', $y_fine_tab_voti, $annotazioni_materie, 1);
//    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Annotazioni 2">
    $annotazioni2 = '<table cellpadding="5">
            <tr>
                <td align="center" style="border-bottom-width:0.1px;">' . $labels['annotazioni_titolo'] . '</td>
            </tr>'.
            '<tr>
'.$tot_note_debiti_2.'<br><br>'.($materie_opzionali != '' ? "Attività o laboratori opzionali: $materie_opzionali" : '')
.'<br><br>' . $annotazioni['p2'].
             '</tr>'.
//            '<tr>
//                <td><br>Monte ore annuo previsto dalla normativa ministeriale: ' . stampa_ore_o_minuti($monteore_finale) . '.'
//           // <br><br>Ai fini della validità dell\'anno, per la valutazione è richiesta la frequenza di almeno tre quarti dell\'orario annuale personalizzato di ciascun alunno. Totale ore di assenza: ' . stampa_ore_o_minuti($assenze_finale) .
//            .'</td>
//            </tr>
        '</table>';
    $dim_cell = $h_page_A3 - $y_fine_tab_voti;
    $pdf->writeHTMLCell($w_page_parts, $dim_cell, $x_start2, $y_fine_tab_voti, $annotazioni2, 1);
    //}}} </editor-fold>
    //}}} </editor-fold>

}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'                     => $id_classe,
    'periodo_pagella'               => $periodo_pagella,
    'orientamento'                  => $orientamento,

    'data_attestato_day'            => $data_attestato_Day,
    'data_attestato_month'          => $data_attestato_Month,
    'data_attestato_year'           => $data_attestato_Year,

    'esclusione_studenti_sospesi'   => $esclusione_studenti_sospesi
];



switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);

            if ($da_includere != "NO")
            {
                $file_name = $nome_pagella_per_file . '.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
                $content = file_get_contents($pagella);

                // NOTA: presume vi sia al max un solo file per i criteri di ricerca
                if ($file[0]['id']) {
                    messengerUpdateFile($file[0]['id'], $content);
                } else {
                    // Destinatari: Studente + genitori
                    $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                    messengerSaveFile([
                        'content'    => $content,
                        'hidden'     => false,
                        'mime'       => 'application/pdf',
                        'name'       => $file_name,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year'   => "{$anno_inizio}/{$anno_fine}",
                            'period' => $periodo_pagella
                        ],
                        'recipients' => $recipients,
                        'tags'       => ['PAGELLE']
                    ]);
                }

                if (file_exists($pagella)) {
                    $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                    unlink($pagella);
                }
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);
            if ($da_includere != "NO") {
                $pdf->Output($file, "F");
            }
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            $da_includere = genera_stampa($pdf, $studente, $parametri_stampa);

            if ($da_includere != "NO") {
                $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
                $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

                if (file_exists($pagella)) {
                    unlink($pagella);
                }
                $pdf->Output($pagella, "F");

                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
                $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

                foreach ($pagelle as $pagella) {
                    $external_data[basename($pagella)] = [
                        'hidden'     => false,
                        'mime'       => 'application/pdf',
                        'name'       => basename($pagella),
                        'class'      => $classe_studente,
                        'owner'      => messengerGetUserID($current_user),
                        'properties' => [
                            'userId' => $id_stud_per_stampa_sito,
                            'year'   => "{$anno_inizio}/{$anno_fine}",
                            'period' => $descrizione_periodo
                        ],
                        'recipients' => $recipients,
                        'tags'       => ['PAGELLE']
                    ];
                }
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
