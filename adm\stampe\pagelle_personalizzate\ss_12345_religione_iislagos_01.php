<?php
/*
 * Nome: ss_12345_religione_iislagos_01
 * Richie<PERSON> da: iislagos
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G','ss_12345_religione_iislagos_01','Pagella Religione Scuola Secondaria di II Grado', 1, 'iislagos', 5);
 */

$orientamento = 'P';
$formato = 'A4';
if ($periodo_pagella == 'finale') {
    $periodo = 9;
}
elseif ($periodo_pagella == 'intermedia') {
    $periodo = 7;
}
$elenco_studenti = estrai_studenti_classe($id_classe, true, false, 0);
$parametri_stampa = [
    'id_classe'             => $id_classe,
    'data_p1_day'              => $data_p1_Day,
    'data_p1_month'            => $data_p1_Month,
    'data_p1_year'             => $data_p1_Year,
    'data_p2_day'              => $data_p2_Day,
    'data_p2_month'            => $data_p2_Month,
    'data_p2_year'             => $data_p2_Year,
    'periodo_pagella'       => $periodo_pagella,
    'periodo'               => $periodo,
    'orientamento'          => $orientamento
];

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    // -- PARAMETRI
    $as_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $periodo = $parametri_stampa['periodo'];

    $id_classe = $parametri_stampa['id_classe'];
    $data_p1 = $parametri_stampa['data_p1_day'].'/'.$parametri_stampa['data_p1_month'].'/'.$parametri_stampa['data_p1_year'];
    $data_p2 = ($periodo == 9?$parametri_stampa['data_p2_day'].'/'.$parametri_stampa['data_p2_month'].'/'.$parametri_stampa['data_p2_year'] : '');
    $id_studente = $studente['id_studente'];
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $comune_nascita = $studente['citta_nascita_straniera'];
        $provincia_nascita = '';
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $comune_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagella_p1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $studente['id_studente']);
    if ($periodo == 9) {
        $voti_pagella_p2 = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);
    } else { $voti_pagella_p2 = []; }
    $arr_voti = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        // Materia religione

        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $arr_voti['descrizione'] = $materia['descrizione'];
            $arr_voti['p1'] = $voti_pagella_p1[$id_materia];
            $arr_voti['p2'] = $voti_pagella_p2[$id_materia];
        }
    }

    $voto_p1 = $voto_p2 = $ass_p1 = $ass_p2 ='';
    foreach ($arr_voti['p1']['significati_voto'] as $significato_voto)
    {
        if ($arr_voti['p1']['voto_pagellina'] == $significato_voto['voto']) {
            $voto_p1 = $significato_voto['valore_pagella'];
        }
        if ($arr_voti['p2']['voto_pagellina'] == $significato_voto['voto']) {
            $voto_p2 = $significato_voto['valore_pagella'];
        }
    }

    // Dizionario temporaneo
    $labels = [
        "alunno"    => "Alunn||min_oa||",
        "alunno_1"  => "l’alunn||min_oa||",
        "nato"      => "Nat||min_oa|| a",
        "iscritto"  => "Iscritt||min_oa|| alla classe",
        "attestato_ammissione_1"    => "l’alunn||min_oa|| ",
        "attestato_ammissione_2"    => "è stat||min_oa|| ",
        "apprendimento" => "RILEVAZIONE DEI PROGRESSI NELL’APPRENDIMENTO E DELLO SVILUPPO PERSONALE E SOCIALE DELL’ALUNN||max_oa||"
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
        $labels[$k] = str_replace("||cognome_nome_studente||", $studente['cognome'] . ' ' . $studente['nome'], $labels[$k]);
    }
    //}}} </editor-fold>


    $f = 'Times';
    $fd = 10;
    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak(2);

    //{{{ <editor-fold defaultstate="collapsed" desc="Inserimento logo e Intestazione">
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', '', 20, 0, 18, '', '', '', false, 300, 'C');
    $pdf->setY(40);
    $pdf->SetFont($f, 'I', $fd+4);
    $pdf->CellFitScale(0, 0, "Ministero Degli Affari Esteri", 0, 1, 'C');
    $pdf->SetFont($f, '', $fd+2);
    $pdf->ln(10);
    $pdf->writeHTMLCell(0, 0, '', '',"Scuola Italiana Paritaria “ Enrico Mattei”<br>
Sikiru Alade Oloko Street off Admiralty way (road 14)<br>
Lekki Peninsula-Lagos<br>
D.M. n. 267/3633 del 19-4-2004", 0, 1, false, true, 'C');
    $pdf->ln(10);
    $pdf->SetFont($f, '', $fd+7);
    $pdf->CellFitScale(0, 0, "Scuola Secondaria di Secondo Grado", 0, 1, 'C');
    $pdf->ln(3);
    $pdf->SetFont($f, 'B', $fd+3);
    $pdf->CellFitScale(0, 0, "Anno Scolastico $as_attuale", 0, 1, 'C');
    $pdf->ln(5);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '',"{$labels['alunno']} <b>{$studente['cognome']} {$studente['nome']}</b>", 0, 1);
    $pdf->ln(3);
    $pdf->writeHTMLCell(0, 0, '', '',"{$labels['nato']} <b>$comune_nascita ($provincia_nascita)</b> il <b>{$studente['data_nascita_ext']}</b>", 0, 1);
    $pdf->ln(3);
    $pdf->writeHTMLCell(0, 0, '', '',"{$labels['iscritto']} <b>{$studente['classe']}{$studente['sezione']} {$studente['descrizione_indirizzi']}</b>", 0, 1);
    $pdf->ln(10);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->CellFitScale(0, 0, "Nota per la valutazione relativa all’ attivita’ alternativa", 0, 1, 'C');
    $pdf->SetFont($f, '', $fd);
    $pdf->CellFitScale(0, 0, "all’insegnamento della Religione Cattolica", 0, 1, 'C');
    $pdf->ln(6);
    $pdf->CellFitScale(95, 9, "I Quadrimestre", 1, 0, 'C');
    $pdf->CellFitScale(95, 9, "II Quadrimestre", 1, 1, 'C');
    $pdf->CellFitScale(95, 9, $voto_p1, 1, 0, 'L');
    $pdf->CellFitScale(95, 9, $voto_p2, 1, 1, 'L');
    $pdf->CellFitScale(95, 9, "Data: $data_p1", 1, 0, 'L');
    $pdf->CellFitScale(95, 9, "Data: $data_p2", 1, 1, 'L');
    $pdf->writeHTMLCell(95, 35, '', '',"Firma del docente", 1, 0);
    $pdf->writeHTMLCell(95, 35, '', '',"Firma del docente", 1, 1);
    $pdf->writeHTMLCell(95, 35, '', '',"Firma del genitore o persona che ne fa le veci", 1, 0);
    $pdf->writeHTMLCell(95, 35, '', '',"Firma del genitore o persona che ne fa le veci", 1, 1);
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella Religione ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella Religione ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;