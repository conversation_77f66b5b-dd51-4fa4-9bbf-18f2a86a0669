<?php

/*
 * Tipo: Pagella fine anno secondaria secondo grado
 * Nome: ss_12345_finale_rogazionisti_pd_01
 * Richiesta da: Rogazionisti PD
 * Data: 2019/02/19
 *
 * Materie particolari:
 * -
 * -
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'ss_12345_finale_rogazionisti_pd_01', 'Pagella Fine Anno Scuola Secondaria di II Grado', 1, 'Rogazionisti PD', 5);

 */

$stampa_personalizzata = 'SI';
$orientamento = 'P';
$formato = 'A4';

$elenco_studenti = estrai_studenti_classe($id_classe, true);
$parametri_stampa = [
    'id_classe'             => $id_classe,
    'data_primo_q_day'      => $data_primo_q_Day,
    'data_primo_q_month'    => $data_primo_q_Month,
    'data_primo_q_year'     => $data_primo_q_Year,
    'periodo_pagella'       => $periodo_pagella,
    'orientamento'          => $orientamento,
    'firma_autografa'       => $firma_autografa,
    'data_fine_anno_day'    => $data_fine_anno_Day,
    'data_fine_anno_month'  => $data_fine_anno_Month,
    'data_fine_anno_year'   => $data_fine_anno_Year,
    'numero_protocollo'     => $numero_protocollo,
    'stampa_lettere'        => $stampa_lettere,
    'stampa_solo_lettere_sospesi' => $stampa_solo_lettere_sospesi,
    'stampa_solo_studenti_sospesi'  => $stampa_solo_studenti_sospesi

];


function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    // -- PARAMETRI
    $periodo_finale='SI';

    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $anni = explode("/", $anno_scolastico_attuale);

    $id_classe = $parametri_stampa['id_classe'];
    $data_primo_q_Day = $parametri_stampa['data_primo_q_day'];
    $data_primo_q_Month = $parametri_stampa['data_primo_q_month'];
    $data_primo_q_Year = $parametri_stampa['data_primo_q_year'];
    $data_fine_anno_Day = $parametri_stampa['data_fine_anno_day'];
    $data_fine_anno_Month = $parametri_stampa['data_fine_anno_month'];
    $data_fine_anno_Year = $parametri_stampa['data_fine_anno_year'];
    $data_Day = $parametri_stampa['data_fine_anno_day'];
    $data_Month = $parametri_stampa['data_fine_anno_month'];
    $data_Year = $parametri_stampa['data_fine_anno_year'];
    $periodo_pagella = $parametri_stampa['periodo_pagella'];
    $num_prot = $parametri_stampa['numero_protocollo'];
    $stampa_solo_lettere_sospesi = $parametri_stampa['stampa_solo_lettere_sospesi'];

    $arr_voti_finale['primo_quadrimestre'] = [];
    $arr_voti_finale['giudizio_primo_quadrimestre'] = '';
    $id_studente = $studente['id_studente'];
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $elenco_id_materie = [];

    // Estrazione voti
    $voti_pagellina_primo_trimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $studente['id_studente']);
    $voti_pagella_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);
    $corso_recupero = 'NO';
    $studio_individuale = 'NO';
    $monteore_finale = 0;
    $assenze_finale_tmp = 0;
    $scrutinato = false;

    $materie_aiuto = [];
    $materie_aiuto_setted = 'NO';
    $materie_sospensione = [];
    $materie_da_recuperare = [];
    $mat_recupero_settembre = [];
    $materie_sospensione_setted = 'NO';

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $elenco_id_materie[$id_materia] = $id_materia;
        $monteore_finale += $voti_pagella_finale[$id_materia]['monteore_totale'];
        $assenze_finale_tmp += $voti_pagella_finale[$id_materia]['ore_assenza'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
               !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'SOSTEGNO', 'RELIGIONE'])
//               ||
//               ($studente['esonero_religione'] == 0 && $materia['tipo_materia'] == 'RELIGIONE')
                )
            )
        {
            $arr_voti_finale['normali'][$id_materia]['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['normali'][$id_materia]['primo_quadrimestre'] = $voti_pagellina_primo_trimestre[$id_materia];
            $arr_voti_finale['normali'][$id_materia]['fine_anno'] = $voti_pagella_finale[$id_materia];

            if ($periodo_finale == 'SI') {
                if (strtolower($voti_pagella_finale[$id_materia]['tipo_recupero_tradotto']) == 'con aiuto' || strtolower($voti_pagella_finale[$id_materia]['tipo_recupero']) == 'con aiuto')
                {
                    $materie_aiuto_setted = 'SI';
                    $materie_aiuto[$id_materia] = $voti_pagella_finale[$id_materia];
                }
                if (strtolower($voti_pagella_finale[$id_materia]['tipo_recupero_tradotto']) == 'studio individuale' && $voti_pagella_finale[$id_materia]['voto_pagellina']<6)
                {
                    $materie_sospensione_setted = 'SI';
                    $materie_sospensione[$id_materia] = $voti_pagella_finale[$id_materia];
                }
                if (strtolower($voti_pagella_finale[$id_materia]['tipo_recupero']) != ''
                        &&
                    $voti_pagella_finale[$id_materia]['voto_pagellina']<6)
                {
                    $materie_da_recuperare[$id_materia] = $voti_pagella_finale[$id_materia];
                }

                if ($voti_pagella_finale[$id_materia]['tipo_recupero'] != ''
                        &&
                    strtolower($voti_pagella_finale[$id_materia]['tipo_recupero']) != 'con aiuto'
                        &&
                    strtolower($voti_pagella_finale[$id_materia]['tipo_recupero_tradotto']) != 'con aiuto')
                {
                    $mat_recupero_settembre[$id_materia] = $voti_pagella_finale[$id_materia];
                }
            }
        }

        // Giudizio globale
        if ($materia['in_media_pagelle'] != 'NV' && (
                                                        ($materia['tipo_materia'] == 'CONDOTTA')
                                                        ||
                                                        (strpos(strtoupper($materia['descrizione']), 'COMPORTAMENTO') !== false)
                                                    )
            )
            {
            $arr_voti_finale['giudizi'][$id_materia]['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['giudizi'][$id_materia]['giudizio_primo_quadrimestre']['voto']['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['giudizi'][$id_materia]['giudizio_primo_quadrimestre']['voto'] = $voti_pagellina_primo_trimestre[$id_materia];
            $arr_voti_finale['giudizi'][$id_materia]['giudizio_fine_anno']['voto']['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['giudizi'][$id_materia]['giudizio_fine_anno']['voto'] = $voti_pagella_finale[$id_materia];

            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie'])) {
                    $valore_selezionato = "";
                    foreach ($campo_libero['valori_precomp'] as $valore)
                    {
                        if ($valore['selezionato'] == 'SI')
                        {
                            if (strpos(strtoupper($campo_libero['nome']), 'COMUNICAZION') !== false)
                            {
                                $valore_selezionato = (!$valore['valore_testuale'] && $valore['descrizione'] != 'Altro') ? $valore['codice'] : $valore['valore_testuale'];
                            }
                            else
                            {
                                $valore_selezionato = (!$valore['valore_testuale'] && $valore['descrizione'] != 'Altro') ? $valore['descrizione'] : $valore['valore_testuale'];
                            }
                        }
                    }

                    if ($valore_selezionato !== '')
                    {
                        $arr_voti_finale['giudizio_fine_anno']['giudizio'] .= $valore_selezionato;
                        $arr_voti_finale['giudizio_fine_anno']['giudizio'] .= " ";
                    }
                }
            }
        }
        if ($voti_pagella_finale[$id_materia]['voto_pagellina'] > 0 && $voti_pagella_finale[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }
    }

    if ($parametri_stampa['stampa_solo_studenti_sospesi'] == 'SI'
            &&
        empty($mat_recupero_settembre) )
    {
        return;
    }


    // CURRICULUM
    $anno_scolastico_corrente = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $a = explode('/', $anno_scolastico_corrente);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];
    $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
    $anno_scolastico_corrente = $anno_inizio . "/" . $anno_fine;
    $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
    //estraggo i dati di provenienza e di quante volte è ripetente
    $provenienza = '';
    for($cont_curr=0; $cont_curr <count($curriculum_studente); $cont_curr++)
    {
//        if ($curriculum_studente[$cont_curr]['esito'] != 'Iscritto')
//        {
            if($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente)
            {
                if($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
                {
                        $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                }
                else
                {
//                        $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
//                    if ($studente['classe'] != 3) {
                        $provenienza = "Sc.Sec II G Rogazionisti";
//                    }
                }
            }
            else {
                $classe_attuale = $curriculum_studente[$cont_curr]['classe'];

                if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_corrente
                    && $classe_attuale == $curriculum_studente[$cont_curr]['classe']
                    && $studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola']
                    && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
                {
                    if (($curriculum_studente[$cont_curr]['esito'] == 'Iscritto') || ($cont_curr <count($curriculum_studente) -1)) {

                        // Nel caso nel curriculum non sia stata inserita la scuola di provenienza
                        if($curriculum_studente[$cont_curr]['nome_scuola'] != "") {
                            $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                        } else {
                            $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
                        }
                    }
                }
            }
//        }
    }

    // titolo di ammissione
    $esito_precedente = estrai_esito_e_volte_iscritto_da_curriculum_studente((int) $studente['id_studente'], $anno_scolastico_precedente);
    $titolo_ammissione = $esito_precedente['esito'];

    // estrazione nuemro volte iscritto
    $numero_volte_iscritto_studente_classe = $studente['mat_esito']['numero_volte_iscritto'];
    $numero_volte_iscritto_studente_classe = $numero_volte_iscritto_studente_classe > 0 ? $numero_volte_iscritto_studente_classe : 1;

    // Dizionario temporaneo
    $labels = [
        "p1_intestazione_1"             => "Ministero dell'Istruzione e del Merito",
        "p1_intestazione_2"             => "SCUOLE PARITARIE ROGAZIONISTI",
        "p1_intestazione_3"             => "Via:   Tiziano Minio, 15    PADOVA",
        "p1_intestazione_4"             => "email: <EMAIL> - web:www.scuolerogazionisti.it",
        "p1_intestazione_5"             => "Scuola secondaria II grado",
        "p1_intestazione_6"             => "Codice Istituto: PDPS035006",

        "p1_titolo_1"                   => "DOCUMENTO DI VALUTAZIONE",
        "p1_titolo_2"                   => "Anno scolastico ",

        "p1_dati_anagrafici"            => "Dati anagrafici dello studente",

        "p1_cognome"                    => "Cognome",
        "p1_nome"                       => "Nome",
        "p1_codice_fiscale"             => "Codice Fiscale",
        "p1_nascita_data"               => "Data di nascita",
        "p1_nascita_comune"             => "Comune di nascita",
        "p1_nascita_provincia"          => "Prov. o Stato estero",
        "p1_classe"                     => "iscritto alla classe",
        "p1_sezione"                    => "Sezione",



        "p2_dati_anagrafici"            => "Dati anagrafici dell'alunn||min_oa||",
        "p2_cognome"                    => "Cognome",
        "p2_nome"                       => "Nome",
        "p2_codice_fiscale"             => "Codice Fiscale",
        "p2_nascita_data"               => "Data di nascita",
        "p2_nascita_luogo"              => "Luogo di nascita",
        "p2_trimestre"                  => "Trimestre",
        "p2_scritto"                    => "Scritto",
        "p2_orale"                      => "Orale",
        "p2_assenze"                    => "Assenze",
        "p2_media_voti"                 => "Media Voti",
        "p2_tot_ore_assenza"            => "Tot. Ore Assenza",
        "p2_crediti_formativi"          => "Crediti formativi",
        "p2_3_liceo"                    => "3° liceo",
        "p2_4_liceo"                    => "4° liceo",
        "p2_5_liceo"                    => "5° liceo",
        "p2_crediti_scolastici"         => "Crediti Scolastici",
        "p2_totale"                     => "Totale",


        "p23_discipline"                => "DISCIPLINE",
        "p23_valutazioni"               => "VALUTAZIONI",


        "p3_posizione_scolastica"       => "Posizione scolastica dell'alunn||min_oa||",
        "p3_n_registro"                 => "N° Registro generale",
        "p3_classe"                     => "Classe",
        "p3_sezione"                    => "Sezione",
        "p3_indirizzo"                  => "Indirizzo",
        "p3_anno_scolastico"            => "Anno Scolastico",
        "p3_codice_istituto"            => "Codice Istituto: PDPS035006",
        "p3_provenienza"                => "Provenienza",
        "p3_titolo_ammissione"          => "Titolo di Ammissione",
        "p3_scuole"                     => "Sc.Sec II^G Rogazionisti",
        "p3_iscritto"                   => "Iscritt||min_oa|| per la",
        "p3_scrutinio_finale"           => "SCRUTINIO FINALE",
        "p3_pentamestre"                => "Pentamestre",
        "p3_settembre"                  => "Settembre",
        "p3_voto_finale"                => "Voto finale",
        "p3_assenze"                    => "Tot. Assenze",
        "p3_voto"                       => "Voto",
        "p3_annotazioni"                => "Annotazioni",
        "p3_risultato_finale"           => "RISULTATO FINALE",
        "p3_studente_stato"             => "Visti i risultati conseguiti si dichiara che l||min_oa|| student||min_eessa|| è stat||min_oa||",
        "p3_dirigente"                  => "Il Coordinatore<br>delle attività educative e didattiche",



        "p4_firma_trimestre"            => "FIRMA TRIMESTRE",
        "p4_firma_pentamestre"          => "FIRMA PENTAMESTRE",
        "p4_dirigente"                  => "IL COORDINATORE<br>DELLE ATTIVITÀ EDUCATIVE E DIDATTICHE",
        "p4_consegna_documento"         => "IL DOCUMENTO É STATO<br>CONSEGNATO ALLA FAMIGLIA<br>IN FORMATO ELETTRONICO",

        "p4_validita_titolo"            => "VALIDITA' DELL'ANNO SCOLASTICO",
        "p4_validita_titolo_2"          => "(Art. 14, comma 7 del D.P.R. n. 122/2009)",
        "p4_validita_intestazione"      => "Ai fini della validità dell'anno e dell'ammissione allo scrutinio finale, l'alunn||min_oa||:",
        "p4_frequentato"                => "ha frequentato per almeno tre quarti dell'orario annuale;",
        "p4_deroga"                     => "non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga;",
        "p4_non_frequentato"            => "non ha frequentato per almeno tre quarti dell'orario annuale;",

        "famiglia"                      => "Alla Famiglia dell'alunn||min_oa||",
        "aiuto_pretext"                 =>
        "<p>Gent.ma Famiglia,<br>"
        . "si comunica che il Consiglio di Classe in sede di scrutinio finale dell'anno scolastico ".$anno_scolastico_attuale." ha rilevato per l'alunn||min_oa|| "
        . "{$studente['cognome']} {$studente['nome']}"." che le seguenti materie risultano <b>non del tutto sufficienti</b> e pertanto necessitano di approfondito ripasso durante il periodo estivo:</p>",
        "aiuto_posttext"                 =>
        "<p align=\"center\"><i>\"ai sensi del D.P.R. n° 122 del 22-06-09, C.M. n° 3614 del 11-05-2010\".</i></p><br>"
        . "Nel periodo iniziale dell'attività scolastica i docenti accerteranno il recupero delle carenze formative e procederanno alla verifica dei "
                . "risultati conseguiti dall'alunn||min_oa|| per impostare positivamente il nuovo anno scolastico.",

        "sospensione_pretext"                 =>
        "<p>Gent.ma Famiglia,<br>"
        . "si comunica che il Consiglio di Classe in sede di scrutinio finale dell'anno scolastico ".$anno_scolastico_attuale.", ha deliberato la sospensione di giudizio per l'ammissione alla classe "
                . "successiva di {$studente['cognome']} {$studente['nome']}, che non ha conseguito la sufficienza nelle seguenti materie:</p>",
        "sospensione_posttext"                 =>
        "<p align=\"center\"><i>\"ai sensi dell'art. 2 comma 4 dell'O.M. n.128 del 14 maggio 1999, dell' art. 7 O.M. n. 92 del 05/11//2007D.P.R. n° 122 del 22-06-09, C.M n°3614 del 11-05-2010\".</i></p><br>"
        . "Il Consiglio di Classe ha deliberato che l'ammissione alla classe successiva avverrà con il completo raggiungimento della sufficienza in tutte le discipline in cui il giudizio è risultato sospeso. In caso di ammissione alla classe successiva, gli esiti delle prove delle discipline in cui il giudizio è risultato sospeso saranno verbalizzati nel registro elettronico come prima valutazione del trimestre.<br><br> Il corso di recupero per le materie segnalate non verrà attivato dalla scuola, pertanto l'alunno deve scegliere servizi di
recupero esterni e comunicare successivamente alla scuola il percorso di recupero compiuto"

    ];


    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
        $labels[$k] = str_replace("||cognome_nome_studente||", $studente['cognome'] . ' ' . $studente['nome'], $labels[$k]);
    }

    // Validazione anno
    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale_tmp / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
            $labels['p4_frequentato'] = "[ X ] " . $labels['p4_frequentato'];
            $labels['p4_deroga'] = "[   ] " . $labels['p4_deroga'];
            $labels['p4_non_frequentato'] = "[   ] " . $labels['p4_non_frequentato'];
        }
        elseif ($scrutinato)
        {
            $validazione_anno = "DEROGA";
            $labels['p4_frequentato'] = "[   ] " . $labels['p4_frequentato'];
            $labels['p4_deroga'] = "[ X ] " . $labels['p4_deroga'];
            $labels['p4_non_frequentato'] = "[   ] " . $labels['p4_non_frequentato'];
        }
        else
        {
            $validazione_anno = "NO";
            $labels['p4_frequentato'] = "[   ] " . $labels['p4_frequentato'];
            $labels['p4_deroga'] = "[   ] " . $labels['p4_deroga'];
            $labels['p4_non_frequentato'] = "[ X ] " . $labels['p4_non_frequentato'];
        }
    }
    else
    {
        $validazione_anno = "SI";
        $labels['p4_frequentato'] = "[ X ] " . $labels['p4_frequentato'];
        $labels['p4_deroga'] = "[   ] " . $labels['p4_deroga'];
        $labels['p4_non_frequentato'] = "[   ] " . $labels['p4_non_frequentato'];
    }
    //}}} </editor-fold>


    // Ordinamento pagine 2-3-4-1 chiesto dalla scuola per favorire la stampa in A3 in data 30/04/2019

    if ($parametri_stampa['stampa_lettere'] != 'SOLO_LETTERE') {
    // -- PAGINA 2
    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak(TRUE, 0);

    // convert TTF font to TCPDF format and store it on the fonts folder
    $fontname = TCPDF_FONTS::addTTFfont('fonts/KUNSTLER.TTF', 'TrueTypeUnicode', '', 96);

    //{{{ <editor-fold defaultstate="collapsed" desc="Dati anagrafici dello studente">
    $pdf->SetFont('helvetica', '', 10);

    //dati
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .= ' (' . $stato['descrizione'] . ')';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'] . ' (' . $studente['provincia_nascita_da_comune'] . ')';
    }

    $tbl = '
        <table cellspacing="0" cellpadding="1" border="0">
            <tr>
                <td colspan="3" style="border-top-width: 0.1px; border-left-width: 0.1px; border-right-width: 0.1px;"><b>'
                    . $labels['p2_dati_anagrafici'] .
                '</b><br></td>
            </tr>
            <tr>
                <td width="30%" style="border-left-width: 0.1px;">'
                    . $labels['p2_cognome'] .
                '</td>
                <td width="30%">'
                    . $labels['p2_nome'] .
                '</td>
                <td width="40%" style="border-right-width: 0.1px;">'
                    . $labels['p2_codice_fiscale'] .
                '</td>
            </tr>
            <tr>
                <td style="border-left-width: 0.1px;"><b>'
                    . $studente['cognome'] . '</b>' .
                '</td>
                <td><b>'
                    . $studente['nome'] . '</b>' .
                '</td>
                <td style="border-right-width: 0.1px;"><b>'
                    . $studente['codice_fiscale'] . '</b>' .
                '<br></td>
            </tr>
            <tr>
                <td style="border-left-width: 0.1px;">'
                    . $labels['p2_nascita_data'] .
                '</td>
                <td>'
                    . $labels['p2_nome'] .
                '</td>
                <td style="border-right-width: 0.1px;"></td>
            </tr>
            <tr>
                <td  style="border-bottom-width: 0.1px; border-left-width: 0.1px;"><b>'
                    . $studente[71] . '</b>' .
                '</td>
                <td style="border-bottom-width: 0.1px;"><b>'
                    . $luogo_nascita . '</b>' .
                '</td>
                <td style="border-bottom-width: 0.1px; border-right-width: 0.1px;"></td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Voti materie e assenze trimestre">
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Write('', $labels['p2_titolo'], '', false, 'C', true);

    $pdf->SetFont('helvetica', '', 10);
    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0.1">
            <tr>
                <td width="50%" rowspan="3" align="center" style="font-size: 160%;"><div style="line-height: 14pt;"> </div><i><b>'
                    . $labels['p23_discipline'] .
                '</b></i></td>
                <td colspan="3" width="50%" align="center" style="font-size: 130%;"><b>'
                    . $labels['p23_valutazioni'] .
                '</b></td>
            </tr>
            <tr>
                <td colspan="3" align="center"><b>'
                    . $labels['p2_trimestre'] .
                '</b></td>
            </tr>
            <tr>
                <td align="center">'
                    . $labels['p2_scritto'] .
                '</td>
                <td align="center">'
                    . $labels['p2_orale'] .
                '</td>
                <td align="center">'
                    . $labels['p2_assenze'] .
                '</td>
            </tr>
        ';

    $tot_voti = 0;
    $cont_voti = 0;
    $tot_assenze = 0;
    foreach ($arr_voti_finale['normali'] as $materia)
    {
        if ($materia['descrizione'])
        {
            $scritto = $orale = $assenze_pq = $finale = $assenze_fa = '';

            if ($materia['primo_quadrimestre']['voto_scritto_pagella'] != '')
            {
                $scritto = $materia['primo_quadrimestre']['voto_scritto_pagella'];
            }
            else
            {
                $scritto = $materia['primo_quadrimestre']['voto_pagellina'];
            }
            $orale = $materia['primo_quadrimestre']['voto_orale_pagella'];

            $finale = $materia['fine_anno']['voto_pagellina'];
            if ($finale != ''
                    &&
                $materia['in_media_pagelle'] != 'NO')
            {
                $tot_voti += $finale;
                $cont_voti++;
            }

            $assenze_pq = intval($materia['primo_quadrimestre']['ore_assenza'] / 60);
            $assenze_fa = intval($materia['fine_anno']['ore_assenza'] / 60);
            $tot_assenze += $assenze_fa;

            foreach ($materia['primo_quadrimestre']['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $scritto)
                {
                    $scritto = $significato['valore'];
                }

                if ($significato['voto'] == $orale)
                {
                    $orale = $significato['valore'];
                }
            }

            if (strpos(strtoupper($materia['descrizione']), 'COMPORTAMENTO') !== false)
            {
                $assenze_pq = '';
            }

            $tbl .= '
            <tr>
                <td><b>'
                    . $materia['descrizione'] .
                '</b></td>
                <td align="center">'
                    . $scritto .
                '</td>
                <td align="center">'
                    . $orale .
                '</td>
                <td align="center">'
                    . $assenze_pq .
                '</td>
            </tr>
            ';
        }
    }

    $tbl .= '</table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    $pdf->ln(8);

    //{{{ <editor-fold defaultstate="collapsed" desc="Riquadri sotto voti trimestre">
    // Primo riquadro: media voti e totale ore di assenza
    $tbl = '
        <table cellspacing="0" cellpadding="3" border="0">
            <tr>
                <td align="center" width="50%" style="border-top-width: 0.1px; border-left-width: 0.1px;">'
                    . $labels['p2_media_voti'] .
                '</td>
                <td align="center" width="50%" style="border-top-width: 0.1px; border-right-width: 0.1px;">'
                    . $labels['p2_tot_ore_assenza'] .
                '</td>
            </tr>
            <tr>
                <td align="center" width="50%" style="border-bottom-width: 0.1px; border-left-width: 0.1px;"><b>'
                    . round(($tot_voti / $cont_voti), 2) .
                '</b></td>
                <td align="center" width="50%" style="border-bottom-width: 0.1px; border-right-width: 0.1px;"><b>'
                    . $tot_assenze .
                '</b></td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');

    $pdf->ln(8);

    // Secondo riquadro: crediti formativi
    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td align="center" colspan="3" width="100%" style="border-top-width: 0.1px; border-left-width: 0.1px; border-right-width: 0.1px;">'
                    . $labels['p2_crediti_formativi'] .
                '</td>
            </tr>
            <tr>
                <td align="center" width="33%" style="border-bottom-width: 0.1px; border-left-width: 0.1px;">'
                    . $labels['p2_3_liceo'] .
                '</td>
                <td align="center" width="34%" style="border-bottom-width: 0.1px;">'
                    . $labels['p2_4_liceo'] .
                '</td>
                <td align="center" width="33%" style="border-bottom-width: 0.1px; border-right-width: 0.1px;">'
                    . $labels['p2_5_liceo'] .
                '</td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');

    $pdf->ln(8);

    // Terzo riquadro: crediti scolastici
    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td align="center" colspan="4" width="100%" style="border-top-width: 0.1px; border-left-width: 0.1px; border-right-width: 0.1px;">'
                    . $labels['p2_crediti_scolastici'] .
                '</td>
            </tr>
            <tr>
                <td align="center" width="25%" style="border-left-width: 0.1px;">'
                    . $labels['p2_3_liceo'] .
                '</td>
                <td align="center" width="25%">'
                    . $labels['p2_4_liceo'] .
                '</td>
                <td align="center" width="25%">'
                    . $labels['p2_5_liceo'] .
                '</td>
                <td align="center" width="25%" style="border-right-width: 0.1px;">'
                    . $labels['p2_totale'] .
                '</td>
            </tr>
            <tr>
                <td align="center" width="25%" style="border-bottom-width: 0.1px; border-left-width: 0.1px;"><b>'
                    . $studente['crediti_terza'] .
                '</b></td>
                <td align="center" width="25%" style="border-bottom-width: 0.1px;"><b>'
                    . $studente['crediti_quarta'] .
                '</b></td>
                <td align="center" width="25%" style="border-bottom-width: 0.1px;"><b>'
                    . $studente['crediti_quinta'] .
                '</b></td>
                <td align="center" width="25%" style="border-bottom-width: 0.1px; border-right-width: 0.1px;"><b>'
                    . ($studente['crediti_terza'] + $studente['crediti_quarta'] + $studente['crediti_quinta']) .
                '</b></td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>


    // -- PAGINA 3
    $pdf->AddPage('P');

    //{{{ <editor-fold defaultstate="collapsed" desc="Dati scolastici dello studente">
    $pdf->SetFont('helvetica', '', 10);

    $volte_iscritto = 1;

    $tbl = '
        <table cellspacing="0" cellpadding="1" border="0">
            <tr>
                <td colspan="5" style="border-top-width: 0.1px; border-left-width: 0.1px; border-right-width: 0.1px;"><b>'
                    . $labels['p3_posizione_scolastica'] .
                '</b><br></td>
            </tr>
            <tr>
                <td width="30%" style="border-left-width: 0.1px;">'
                    . $labels['p3_n_registro'] .
                '</td>
                <td width="10%">'
                    . $labels['p3_classe'] .
                '</td>
                <td width="10%">'
                    . $labels['p3_sezione'] .
                '</td>
                <td width="20%">'
                    . $labels['p3_indirizzo'] .
                '</td>
                <td width="30%" style="border-right-width: 0.1px;">'
                    . $labels['p3_anno_scolastico'] . ' ' . $anno_scolastico_attuale .
                '</td>
            </tr>
            <tr>
                <td width="30%" style="border-left-width: 0.1px;"><b>'
                    . $studente['matricola'] .
                '</b><br></td>
                <td width="10%"><b>'
                    . $studente['classe'] .
                '</b></td>
                <td width="10%"><b>U</b></td>
                <td width="20%"><b>'
                    . $studente['descrizione_indirizzi'] .
                '</b></td>
                <td width="30%" style="border-right-width: 0.1px;"><b>'
                    . $labels['p3_codice_istituto'] .
                '</b></td>
            </tr>
            <tr>
                <td width="30%" style="border-left-width: 0.1px;">'
                    . $labels['p3_provenienza'] .
                '</td>
                <td width="40%">'
                    . $labels['p3_titolo_ammissione'] .
                '</td>
                <td width="30%" style="border-right-width: 0.1px;"></td>
            </tr>
            <tr>
                <td width="30%" style="border-left-width: 0.1px; border-bottom-width: 0.1px;"><b>'
                    . $provenienza .
                '</b></td>
                <td width="40%" style="border-bottom-width: 0.1px;"><b>'
                    . $titolo_ammissione .
                '</b></td>
                <td width="30%" style="border-right-width: 0.1px; border-bottom-width: 0.1px;">'
                    . $labels['p3_iscritto'] . ' ' . $numero_volte_iscritto_studente_classe . 'ª volta' .
                '</td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Voti materie e assenze pentamestre">
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Write('', $labels['p2_titolo'], '', false, 'C', true);

    $pdf->SetFont('helvetica', '', 10);
    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0.1">
            <tr>
                <td width="33%" rowspan="3" align="center" style="font-size: 160%;"><div style="line-height: 14pt;"> </div><i><b>'
                    . $labels['p23_discipline'] .
                '</b></i></td>
                <td colspan="2" width="33%" align="center" style="font-size: 130%;"><i><b>'
                    . $labels['p23_valutazioni'] .
                '</b></i></td>
                <td width="34%" align="center" style="font-size: 130%;"><i><b>'
                    . $labels['p3_scrutinio_finale'] .
                '</b></i></td>
            </tr>
            <tr>
                <td colspan="2" align="center"><b>'
                    . $labels['p3_pentamestre'] .
                '</b></td>
                <td align="center"><b>'
                    . $labels['p3_settembre'] .
                '</b></td>
            </tr>
            <tr>
                <td align="center">'
                    . $labels['p3_voto_finale'] .
                '</td>
                <td align="center">'
                    . $labels['p3_assenze'] .
                '</td>
                <td align="center">'
                    . $labels['p3_voto'] .
                '</td>
            </tr>
        ';

    foreach ($arr_voti_finale['normali'] as $materia)
    {
        if ($materia['descrizione'])
        {
            $finale = $assenze_fa = $finale_trad = $finale_settembre ='';

            $finale = $materia['fine_anno']['voto_pagellina'];
            if ($finale != '')
            {
                $tot_voti += $finale;
                $cont_voti++;
            }

            $assenze_fa = intval($materia['fine_anno']['ore_assenza'] / 60);

            foreach ($materia['fine_anno']['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $finale)
                {
                    $finale_trad = $significato['valore'];
                }
            }

            if (strpos(strtoupper($materia['descrizione']), 'COMPORTAMENTO') !== false)
            {
                $assenze_fa = '';
            }


            if ($parametri_stampa['stampa_solo_studenti_sospesi'] == 'SI')
            {
                if (array_key_exists( $materia['fine_anno']['id_materia'], $mat_recupero_settembre))
                {

//                    if (strtolower($studente['esito']) == 'non ammesso alla classe successiva' || strtolower($studente['esito']) == 'non ammesso esame di stato')
//                    {
//                        // sospesi - non ammessi
//                        $finale_settembre = $finale_trad;
//                    }
//                    else
//                    {
                        // sospesi - ammessi
                        $finale_settembre = $finale_trad;
                        $finale_trad = 'cinque';
                        if (
                            ($studente['id_studente'] == '1001676' && strtolower($materia['descrizione'])=='scienze') // VIDRINI SASHA NICHOLAS
                                ||
                            ($studente['id_studente'] == '1001666' && strtolower($materia['descrizione'])=='scienze' ) // KOLOSOV ANTONIO
                                ||
                            ($studente['id_studente'] == '1001632' && (strtolower($materia['descrizione'])=='scienze' || strtolower($materia['descrizione'])=='filosofia') ) // FOGLIA MARCO
                            )
                        {
                            $finale_trad = 'quattro';
                        }
//                    }
                }
            }

            $tbl .= '
            <tr>
                <td><b>'
                    . $materia['descrizione'] .
                '</b></td>
                <td align="center">'
                    . $finale_trad .
                '</td>
                <td align="center">'
                    . $assenze_fa .
                '</td>
                <td align="center">'
                    . $finale_settembre .
                '</td>
            </tr>
            ';
        }
    }

    $tbl .= '</table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    $pdf->ln(8);

    //{{{ <editor-fold defaultstate="collapsed" desc="Riquadro annotazioni">
    $tbl = '
        <table cellspacing="0" cellpadding="3" border="0">
            <tr>
                <td align="center" width="100%" style="font-size: 130%; border-top-width: 1px; border-left-width: 1px; border-right-width: 1px;">'
                    . $labels['p3_annotazioni'] .
                '</td>
            </tr>
            <tr>
                <td style="border-bottom-width: 1px; border-left-width: 1px; border-right-width: 1px;">'
                    . $arr_voti_finale['giudizio_fine_anno']['giudizio'] .
                '</td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    $pdf->ln(8);

    //{{{ <editor-fold defaultstate="collapsed" desc="Attestato">
    $pdf->SetFont('helvetica', 'B', 10);
    $giorno = ucfirst(strtolower(traduci_giorno_in_lettere(date("N",strtotime($data_fine_anno_Year."-".$data_fine_anno_Month."-" . $data_fine_anno_Day)))));
    $stringa_data = ucfirst(strtolower($studente['descrizione_comuni'])) . ', ' . $giorno . ' ' . $data_fine_anno_Day . ' ' . traduci_mese_in_lettere($data_fine_anno_Month, 'esteso') . ' ' . $data_fine_anno_Year;

    $tbl = '
        <table cellspacing="0" cellpadding="10" border="0">
            <tr>
                <td colspan="3" align="center" style="font-size: 15%px; border-left-width: 1px; border-top-width: 1px; border-right-width: 1px; border-top-color: black; border-left-color: grey; border-right-color: grey;"><b>'
                    . $labels['p3_risultato_finale'] .
                '</b></td>
            </tr>
            <tr>
                <td colspan="3" align="center" style="font-style: italic; border-left-width: 1px; border-right-width: 1px;">'
                    . $labels['p3_studente_stato'] .
                '</td>
            </tr>
            <tr>
                <td colspan="3" align="center" style="font-style: italic; border-left-width: 1px; border-right-width: 1px;">'
                    . $studente['esito_corrente_calcolato'] .
                '</td>
            </tr>
            <tr>
                <td width="40%" style="font-style: italic; border-left-width: 1px; border-bottom-width: 1px;"><br><br><br>'
                    . $stringa_data .
                '</td>
                <td width="20%" align="center" style=" border-bottom-width: 1px;">
                    <img src="immagini_scuola/timbro_scientifico.png" height="70" width="70">
                </td>
                <td width="40%" align="center" style="font-style: italic; border-right-width: 1px; border-bottom-width: 1px;"><b>'
                    . $labels['p3_dirigente'] . '</b><br>' . $studente['nome_dirigente'] . '<br>' .
                    '<img src="immagini_scuola/firma_dirigente.png" height="30" width="130">' .
                '</td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>


    // -- Pagina 4
    $pdf->AddPage('P');

    //{{{ <editor-fold defaultstate="collapsed" desc="Firme">
    $pdf->SetFont('helvetica', '', 10);
    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td align="center" width="50%" style="font-size: 110%; color: black; font-weight: bold; background-color: #bad0e0; border-width: 1px; border-top-color: #003568; border-right-color: #36628c; border-bottom-color: #003568; border-left-color: #003568;">'
                    . $labels['p4_firma_trimestre'] .
                '</td>
                <td align="center" width="50%" style="font-size: 110%; color: black; font-weight: bold; background-color: #bad0e0; border-width: 1px; border-top-color: #003568; border-left-color: #36628c; border-bottom-color: #003568; border-right-color: #003568;">'
                    . $labels['p4_firma_pentamestre'] .
                '</td>
            </tr>
            <tr>
                <td align="center" width="50%" style="font-weight: bold; border-width: 1px; border-top-color: #003568; border-right-color: #36628c; border-bottom-color: #003568; border-left-color: #003568;">'
                    . '<span style="font-size: 90%;">' . $labels['p4_dirigente'] . '</span><br>'
                    . '(' . $studente['nome_dirigente'] . ')<br>'
                    . '<img src="immagini_scuola/firma_dirigente.png" height="30" width="130">' .
                '</td>
                <td align="center" width="50%" style="font-weight: bold; border-width: 1px; border-top-color: #003568; border-left-color: #36628c; border-bottom-color: #003568; border-right-color: #003568;">'
                    . '<span style="font-size: 90%;">' . $labels['p4_dirigente'] . '</span><br>'
                    . '(' . $studente['nome_dirigente'] . ')<br>'
                    . '<img src="immagini_scuola/firma_dirigente.png" height="30" width="130">' .
                '</td>
            </tr>
            <tr>
                <td align="center" width="50%" style="font-size: 90%; font-weight: bold; border-width: 1px; border-top-color: #003568; border-right-color: #36628c; border-bottom-color: #003568; border-left-color: #003568;">'
                    . '<br><br>' . $labels['p4_consegna_documento'] . '<br>' .
                '</td>
                <td align="center" width="50%" style="font-size: 90%; font-weight: bold; border-width: 1px; border-top-color: #003568; border-left-color: #36628c; border-bottom-color: #003568; border-right-color: #003568;">'
                    . '<br><br>' . $labels['p4_consegna_documento'] . '<br>' .
                '</td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    $pdf->ln(5);

    //{{{ <editor-fold defaultstate="collapsed" desc="Luogo e date">
    $pdf->SetFont('helvetica', '', 9);

    $giorno_fine_primo_q = traduci_giorno_in_lettere(date("N",strtotime($data_primo_q_Year."-".$data_primo_q_Month."-" . $data_primo_q_Day)));
    $stringa_data_primo_q = ucfirst(strtolower($studente['descrizione_comuni'])) . ', ' . strtolower($giorno_fine_primo_q) . ' ' . $data_primo_q_Day . ' ' . traduci_mese_in_lettere($data_primo_q_Month, 'esteso') . ' ' . $data_primo_q_Year;
    $giorno_fine_anno = traduci_giorno_in_lettere(date("N",strtotime($data_fine_anno_Year."-".$data_fine_anno_Month."-" . $data_fine_anno_Day)));
    $stringa_data_fine_anno = ucfirst(strtolower($studente['descrizione_comuni'])) . ', ' . strtolower($giorno_fine_anno) . ' ' . $data_fine_anno_Day . ' ' . traduci_mese_in_lettere($data_fine_anno_Month, 'esteso') . ' ' . $data_fine_anno_Year;

    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td align="center" width="50%" style="border-width: 1px; border-top-color: #003568; border-right-color: white; border-bottom-color: #003568; border-left-color: #36628c;">'
                    . $stringa_data_primo_q .
                '</td>
                <td align="center" width="50%" style="border-width: 1px; border-top-color: #003568; border-left-color: white; border-bottom-color: #003568; border-right-color: #36628c;">'
                    . $stringa_data_fine_anno .
                '</td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    $pdf->ln(20);

    //{{{ <editor-fold defaultstate="collapsed" desc="Validità anno">
    $pdf->SetFont('helvetica', 'B', 9);

    $tbl = '
        <div align="center">
            <span style="font-size: 110%; color: #003568; line-height: 18pt; font-style: italic;"> '
                . $labels['p4_validita_titolo'] .
                '<br>'
                . $labels['p4_validita_titolo_2'] .
            '</span>
        </div>
        <div align="center"> '
            . $labels['p4_validita_intestazione'] .
        '</div>
        <div style="line-height: 18pt;">'
            . $labels['p4_frequentato'] . '<br>'
            . $labels['p4_deroga'] . '<br>'
            . $labels['p4_non_frequentato'] . '<br>' .
        '</div>
        ';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>


    // -- PAGINA 1
    // dati anagrafici dello studente e della scuola
    $pdf->AddPage('P');

    //{{{ <editor-fold defaultstate="collapsed" desc="Inserimento logo e Intestazione">
    //logo
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', '', '', 0, 20, '', '', '', false, 300, 'C');

    $pdf->SetY(35);

    //intestazione 1
    $pdf->SetFont($fontname, 'B', 32, false);
    $pdf->Write('', $labels['p1_intestazione_1'], '', false, 'C', true);

    $pdf->ln(8);

    //intestazioni 2, 3, 4
    $pdf->SetFont('helvetica', '', 18);
    $pdf->Write('', $labels['p1_intestazione_2'], '', false, 'C', true);
    $pdf->ln(2);
    $pdf->SetFont('helvetica', '', 13);
    $pdf->Write('', $labels['p1_intestazione_3'], '', false, 'C', true);
    $pdf->ln(10);
    $pdf->Write('', $labels['p1_intestazione_4'], '', false, 'C', true);
    $pdf->ln(10);
    $pdf->SetFont('helvetica', '', 15);
    $pdf->Write('', $labels['p1_intestazione_5'], '', false, 'C', true);
    $pdf->ln(2);
    $pdf->SetFont('helvetica', '', 13);
    $pdf->Write('', $labels['p1_intestazione_6'], '', false, 'C', true);

    $pdf->ln(30);

    //titoli
    $pdf->SetFont('helvetica', 'B', 20);
    $pdf->Write('', $labels['p1_titolo_1'], '', false, 'C', true);
    $pdf->SetFont('helvetica', '', 15);
    $pdf->ln(6);
    $pdf->Write('', $labels['p1_titolo_2'] . $anno_scolastico_attuale, '', false, 'C', true);
    //}}} </editor-fold>

    $pdf->ln(50);

    //{{{ <editor-fold defaultstate="collapsed" desc="Dati anagrafici dello studente">
    //titolo
    $pdf->SetFont('helvetica', '', 11);
    $pdf->Write('', $labels['p1_dati_anagrafici'], '', false, 'C', true);

    $pdf->ln(5);

    //dati
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $comune_nascita = $studente['citta_nascita_straniera'];
        $provincia_nascita = '';
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $comune_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td width="30%" style="font-size: 120%;"><b>'
                    . $studente['cognome'] . '</b>' .
                '</td>
                <td width="30%" style="font-size: 120%;"><b>'
                    . $studente['nome'] . '</b>' .
                '</td>
                <td width="40%" style="font-size: 120%;"><b>'
                    . $studente['codice_fiscale'] . '</b>' .
                '</td>
            </tr>
            <tr>
                <td>'
                   . $labels['p1_cognome'] .
                '</td>
                <td>'
                   . $labels['p1_nome'] .
                '</td>
                <td>'
                   . $labels['p1_codice_fiscale'] .
                '</td>
            </tr>
        </table>
        <br>
        <br><table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td width="40%" style="font-size: 120%;"><b>'
                    . $studente[71] . '</b>' .
                '</td>
                <td width="40%" style="font-size: 120%;"><b>'
                    . $comune_nascita . '</b>' .
                '</td>
                <td width="20%" style="font-size: 120%;"><b>'
                    . $provincia_nascita . '</b>' .
                '</td>
            </tr>
            <tr>
                <td>'
                   . $labels['p1_nascita_data'] .
                '</td>
                <td>'
                   . $labels['p1_nascita_comune'] .
                '</td>
                <td>'
                    . $labels['p1_nascita_provincia'] .
                '</td>
            </tr>
        </table>
        <br>
        <br><table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td width="20%">'
                    . $labels['p1_classe'] .
                '</td>
                <td width="10%"><b>'
                    . $studente['classe'] . '</b>' .
                '</td>
                <td width="10%">'
                    . $labels['p1_sezione'] .
                '</td>
                <td width="10%"><b>Unica</b>' .
                '</td>
            </tr>
        </table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>
    }


    $pdf->SetCellHeightRatio(1.25);
    $pdf->SetFont('helvetica', 'BI', 8);
    $mese = traduci_mese_in_lettere($data_Month, 'esteso');
    $text = ucfirst(strtolower($studente['descrizione_comuni'])) . ", " . $giorno . ' ' . $data_Day . " " . $mese . " " . $data_Year;
    if ($periodo_finale == 'SI'
            &&
        ($parametri_stampa['stampa_lettere'] == 'SI' || $parametri_stampa['stampa_lettere'] == 'SOLO_LETTERE')
            &&
        $parametri_stampa['stampa_solo_studenti_sospesi'] == 'NO')
    {
        //{{{ <editor-fold defaultstate="collapsed" desc="materie aiuto">
        if ($materie_aiuto_setted == 'SI') {
            $pdf->AddPage('P');
            $pdf->SetFont('helvetica', '', 13);
            if (file_exists('immagini_scuola/logo_superiori.jpg')) {
                $pdf->Image('immagini_scuola/logo_superiori.jpg', '', '', 55, 25, 'JPG');
            }
            // header
            $mese_in_lettere = traduci_mese_in_lettere($data_Month, 'esteso');
            $tbl_header = '<table align="center">
                    <tr>
                        <td width="36%"></td>
                        <td width="32%">&nbsp;<br>SEGNALAZIONE MATERIE NON DEL TUTTO SUFFICIENTI</td>
                        <td width="32%">&nbsp;<br><font size="-2">Padova<br>' . "$giorno, $data_Day $mese_in_lettere $data_Year". '</font></td>
                    </tr>
                </table>';
            $pdf->writeHTML($tbl_header);
            //
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Ln(15);
            if ($num_prot != '') {
                $pdf->Cell(0, 0, 'Prot. N. '.$num_prot, 0, 1);
            }
            else {
                $pdf->Cell(0, 0, ' ', 0, 1);
            }
            $tbl = '<table>
              <tr>
                <td width="50%"></td>
                <td width="24%" align="right">' . $labels['famiglia'] . '</td>
                <td width="2%"></td>
                <td width="24%" align="left"><b>' . $studente['cognome'] .' '.$studente['nome'] . '</b></td>
              </tr>
              <tr>
                <td></td>
                <td align="right">Classe&nbsp;</td>
                <td></td>
                <td align="left"><b>' . $studente['classe'] .' '.$studente['sezione'] . '</b></td>
              </tr>
              </table>
            ';
            $pdf->writeHTML($tbl);
            $pdf->Ln(7);
            $pdf->SetCellHeightRatio(2.5);
            $pdf->writeHTML($labels['aiuto_pretext']); #
            $pdf->Ln(10);
            foreach ($materie_aiuto as $id_m=>$materia_aiuto) {
                $materie_aiuto_desc[] = ucfirst(strtolower($materia_aiuto['descrizione']));
            }
            $tbl_aiuto =
                '<table border="0.1" cellpadding="3">
                    <tr>
                        <td width="15%">MATERIE:</td>
                        <td width="85%">'.implode( ', ', $materie_aiuto_desc).'</td>
                    </tr>
                </table>';
            $pdf->writeHTML($tbl_aiuto);
            $pdf->Ln(4);
            $pdf->writeHTML($labels['aiuto_posttext']);
            $pdf->Ln(14);
            //{{{ <editor-fold defaultstate="collapsed" desc="Firme e timbro">
            $pdf->SetFont('helvetica', 'BI', 10);
            $pdf->SetCellHeightRatio(1.25);
            $tbl = '
                <table cellspacing="0" cellpadding="2" border="0">
                    <tr>
                        <td align="center" width="35%">
                        </td>
                        <td width="30%">
                        </td>
                        <td align="center" width="35%">
                            '.$labels['p3_dirigente'].'<br>'.$studente['nome_dirigente'].'
                        </td>
                    </tr>
                </table>
                ';
            $pdf->writeHTML($tbl, true, false, false, false, '');

            $y_corrente = $pdf->GetY();
            $pdf->Image('immagini_scuola/firma_dirigente.png', 138, $y_corrente - 10, 65, 15, '', '', '', false, 300, '', false, false, 0, true);

            if (file_exists('immagini_scuola/timbro_scientifico.png')) {
                $pdf->writeHTMLCell(0,0, '',$y_corrente - 18, '<img src="immagini_scuola/timbro_scientifico.png" height="70" width="70">',0,0,false,true,'C');
//                $pdf->Image('immagini_scuola/timbro_scientifico.png', '', $y_corrente - 18, '', 25, '', '', '', false, 300, 'C', false, false, 0, true);
            }
            //}}} </editor-fold>
        }
        $pdf->SetCellHeightRatio(1.25);
            //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="materie sospensione">
        if ($materie_sospensione_setted == 'SI') {
            $pdf->AddPage('P');
            $pdf->SetFont('helvetica', '', 13);
            if (file_exists('immagini_scuola/logo_superiori.jpg')) {
                $pdf->Image('immagini_scuola/logo_superiori.jpg', '', '', 55, 25, 'JPG');
            }
            // header
            $mese_in_lettere = traduci_mese_in_lettere($data_Month, 'esteso');
            $tbl_header = '<table align="center">
                    <tr>
                        <td width="36%"></td>
                        <td width="32%">&nbsp;<br>SOSPENSIONE<br>DEL GIUDIZIO</td>
                        <td width="32%">&nbsp;<br><font size="-2">Padova<br>' . "$giorno, $data_Day $mese_in_lettere $data_Year". '</font></td>
                    </tr>
                </table>';
            $pdf->writeHTML($tbl_header);
            //
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Ln(15);
            if ($num_prot != '') {
                $pdf->Cell(0, 0, 'Prot. N. '.$num_prot, 0, 1);
            }
            else {
                $pdf->Cell(0, 0, ' ', 0, 1);
            }
            $tbl = '<table>
              <tr>
                <td width="50%"></td>
                <td width="24%" align="right">' . $labels['famiglia'] . '</td>
                <td width="2%"></td>
                <td width="24%" align="left"><b>' . $studente['cognome'] .' '.$studente['nome'] . '</b></td>
              </tr>
              <tr>
                <td></td>
                <td align="right">Classe&nbsp;</td>
                <td></td>
                <td align="left"><b>' . $studente['classe'] .' '.$studente['sezione'] . '</b></td>
              </tr>
              </table>
            ';
            $pdf->writeHTML($tbl);
            $pdf->Ln(7);
            $pdf->SetCellHeightRatio(2);
            $pdf->writeHTML($labels['sospensione_pretext']); #
            $pdf->Ln(10);
            $materie_sosp_4 = [];
            $materie_sosp_5 = [];
            // table
            $tbl_sos = '<table border="0.1" cellpadding="3">
                <tr>
                    <td width="15%" align="center">Valutazione</td>
                    <td width="85%" align="center">Disciplina</td>
                </tr>
                ';
    //        echo '<pre>', print_r($materie_aiuto);
            foreach ($materie_sospensione as $id_m=>$materia_sosp)
            {
                if ($materia_sosp['voto_pagellina']==4) {
                    $materie_sosp_4[] = ucfirst(strtolower($materia_sosp['descrizione']));
                }
                elseif ($materia_sosp['voto_pagellina']==5) {
                    $materie_sosp_5[] = ucfirst(strtolower($materia_sosp['descrizione']));
                }
            }
            $tbl_sos .= '
                <tr>
                    <td>Con il 4</td>
                    <td>' . implode(', ', $materie_sosp_4) . '</td>
                </tr>
                <tr>
                    <td>Con il 5</td>
                    <td>' . implode(', ', $materie_sosp_5) . '</td>
                </tr>
            </table>';
            $pdf->writeHTML($tbl_sos);
            $pdf->Ln(4);
            $pdf->writeHTML($labels['sospensione_posttext']);
            $pdf->Ln(14);

            //{{{ <editor-fold defaultstate="collapsed" desc="Firme e timbro">
            $pdf->SetFont('helvetica', 'BI', 10);
            $pdf->SetCellHeightRatio(1.25);
            $tbl = '
                <table cellspacing="0" cellpadding="2" border="0">
                    <tr>
                        <td align="center" width="35%">
                        </td>
                        <td width="30%">
                        </td>
                        <td align="center" width="35%">
                            '.$labels['p3_dirigente'].'<br>'.$studente['nome_dirigente'].'
                        </td>
                    </tr>
                </table>
                ';
            $pdf->writeHTML($tbl, true, false, false, false, '');

            $y_corrente = $pdf->GetY();
            $pdf->Image('immagini_scuola/firma_dirigente.png', 138, $y_corrente - 10, 65, 15, '', '', '', false, 300, '', false, false, 0, true);
            if (file_exists('immagini_scuola/timbro_scientifico.png')) {
                $pdf->writeHTMLCell(0,0, '',$y_corrente - 18, '<img src="immagini_scuola/timbro_scientifico.png" height="70" width="70">',0,0,false,true,'C');
//                $pdf->Image('immagini_scuola/timbro_scientifico.png', '', $y_corrente - 18, '', 25, '', '', '', false, 300, 'C', false, false, 0, true);
            }
            $pdf->SetCellHeightRatio(1.25);
            //}}} </editor-fold>
        }

    //}}} </editor-fold>


        if (!empty($materie_da_recuperare))
        {
            foreach ($materie_da_recuperare as $id_m=>$mat_rec)
            {
                $pdf->AddPage('P');
                $pdf->SetFont('helvetica', '', 13);
                if (file_exists('immagini_scuola/logo_superiori.jpg')) {
                    $pdf->Image('immagini_scuola/logo_superiori.jpg', '', '', 55, 25, 'JPG');
                }
                // header
                $mese_in_lettere = traduci_mese_in_lettere($data_Month, 'esteso');
                $tbl_header = '
                    <table align="center">
                        <tr>
                            <td width="36%"></td>
                            <td width="32%">INDICAZIONI DOCENTI<br>SULLE MATERIE CON<br>SOSPENSIONE DI GIUDIZIO</td>
                            <td width="32%">&nbsp;<br><font size="-2">Padova<br>' . "$giorno, $data_Day $mese_in_lettere $data_Year". '</font></td>
                        </tr>
                    </table>
                    ';
                $pdf->writeHTML($tbl_header);
                //

                $pdf->SetFont('helvetica', '', 10);
                $pdf->Ln(15);
                if ($num_prot != '') {
                    $pdf->Cell(0, 0, 'Prot. N. '.$num_prot, 0, 1);
                }
                else {
                    $pdf->Cell(0, 0, ' ', 0, 1);
                }
                $tbl = '<table>
                  <tr>
                    <td width="50%"></td>
                    <td width="24%" align="right">' . $labels['famiglia'] . '</td>
                    <td width="2%"></td>
                    <td width="24%" align="left"><b>' . $studente['cognome'] .' '.$studente['nome'] . '</b></td>
                  </tr>
                  <tr>
                    <td></td>
                    <td align="right">Classe&nbsp;</td>
                    <td></td>
                    <td align="left"><b>' . $studente['classe'] .' '.$studente['sezione'] . '</b></td>
                  </tr>
                  <tr>
                    <td></td>
                    <td align="right">Materia&nbsp;</td>
                    <td></td>
                    <td align="left"><b>' . ucfirst(strtolower($mat_rec['descrizione'])) . '</b></td>
                  </tr>
                  </table>
                ';
                $pdf->writeHTML($tbl);
                $pdf->Ln(10);

                $pdf->SetCellHeightRatio(1.55);
                $tbl_nome_mat=''
                        . '<table>'
                        . '<tr>'
                        . '<td width="25%"></td>'
                        . '<td width="50%">'
                            . '<table border="0.1px" align="center">'
                            . '<tr>'
                            . '<td width="70%"><b>Materia</b></td>'
                            . '<td width="30%"><b>Voto</b></td>'
                            . '</tr>'
                            . '<tr>'
                            . '<td>'.ucfirst(strtolower($mat_rec['descrizione'])).'</td>'
                            . '<td>'.$mat_rec['voto_pagellina'].'</td>'
                            . '</tr>'
                            . '</table>'
                        . '</td>
                           <td></td>
                           </tr>
                           </table>';
                $pdf->writeHTML($tbl_nome_mat);
                $pdf->Ln(4);

                $tbl_campi_liberi = '
                    <table>
                        <tr>
                            <td width="5%"></td>
                            <td width="90%">
                                <table border="0.1px" align="center">
                                    <tr>
                                        <td><b>Carenze riscontrate nella preparazione</b></td>
                                        <td><b>Descrizione</b></td>
                                    </tr>';
                foreach ($mat_rec['campi_liberi'] as $id_campo=>$campo) {
                    if ( ( strtolower($campo['descrizione']) == 'impegno e partecipazione'
                        || strtolower($campo['descrizione']) == 'conoscenza dei contenuti'
                        || strtolower($campo['descrizione']) == 'conoscenze pregresse'
                        || (strpos(strtolower($campo['descrizione']), 'competenze espositive') !== false)
                        || strtolower($campo['descrizione']) == 'metodo di studio'
                        )
                        && $campo['visibile'] == 'SI')
                    {
                        $val_campo = '';
                        $val_campo = estrai_valore_campo_libero_selezionato($campo);
                        $tbl_campi_liberi.= '
                                <tr>
                                    <td>'.$campo['nome'].'</td>
                                    <td>'.$val_campo.'</td>
                                </tr>
                            ';
                    }
                }
                $tbl_campi_liberi .=
                    '           </table>
                            </td>
                            <td></td>
                        </tr>
                    </table>
                ';
                $pdf->writeHTML($tbl_campi_liberi);
                $pdf->Ln(4);

                $verifica_sett = '';
                foreach ($mat_rec['campi_liberi'] as $id_campo=>$campo) {
                    if ( (strpos(strtolower($campo['descrizione']), 'prova di verifica a settembre') !== false) && $campo['visibile'] == 'SI') {
                        $verifica_sett = estrai_valore_campo_libero_selezionato($campo);
                    }
                }
                $tbl_mod_rec = '
                    <table>
                    <tr>
                        <td width="20%"></td>
                        <td width="60%">
                            <table border="0.1px" align="center">
                                <tr>
                                    <td><b>Modalità di recupero</b></td>
                                    <td><b>Prova di verifica ad settembre</b></td>
                                </tr>
                                 <tr>
                                    <td>'.$mat_rec['tipo_recupero_tradotto'].'</td>
                                    <td>'.$verifica_sett.'</td>
                                </tr>
                            </table>
                        </td>
                        <td></td>
                    </tr>
                    </table>
                    ';
                $pdf->writeHTML($tbl_mod_rec);
                $pdf->Ln(11);

                $pdf->SetCellHeightRatio(1.3);
                $txt_indicazioni_docente='';
                foreach ($mat_rec['campi_liberi'] as $id_campo=>$campo) {
                    if ( (strpos(strtolower($campo['descrizione']), 'indicazioni da parte del docente') !== false) && $campo['visibile'] == 'SI') {
                        $txt_indicazioni_docente = estrai_valore_campo_libero_selezionato($campo);
                    }
                }
                $pdf->SetFont('helvetica', 'B', 12);
                $pdf->Cell(0,0,'ALTRE INDICAZIONI DA PARTE DEL DOCENTE:' ,0,1);
                $pdf->Ln(3);
                $pdf->SetFont('helvetica', '', 10);
                $pdf->MultiCell(0,0,$txt_indicazioni_docente,0,'L');
                $pdf->Ln(10);

                //{{{ <editor-fold defaultstate="collapsed" desc="Firme e timbro">
                $pdf->SetFont('helvetica', 'BI', 10);
                $pdf->SetCellHeightRatio(1.25);
                $tbl = '
                    <table cellspacing="0" cellpadding="2" border="0">
                        <tr>
                            <td align="center" width="35%">
                            </td>
                            <td width="30%">
                            </td>
                            <td align="center" width="35%">
                                '.$labels['p3_dirigente'].'<br>'.$studente['nome_dirigente'].'
                            </td>
                        </tr>
                    </table>
                    ';
                $pdf->writeHTML($tbl, true, false, false, false, '');

                $y_corrente = $pdf->GetY();
                $pdf->Image('immagini_scuola/firma_dirigente.png', 138, $y_corrente - 10, 65, 15, '', '', '', false, 300, '', false, false, 0, true);
                if (file_exists('immagini_scuola/timbro_scientifico.png')) {
                    $pdf->writeHTMLCell(0,0, '',$y_corrente - 18, '<img src="immagini_scuola/timbro_scientifico.png" height="70" width="70">',0,0,false,true,'C');
//                    $pdf->Image('immagini_scuola/timbro_scientifico.png', '', $y_corrente - 18, '', 25, '', '', '', false, 300, 'C', false, false, 0, true);
                }
                $pdf->SetCellHeightRatio(1.25);
                //}}} </editor-fold>

            }
        }
    }
    $pdf->SetCellHeightRatio(1.25);

}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
