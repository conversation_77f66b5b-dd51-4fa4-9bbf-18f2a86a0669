<?php
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'scheda_candidato_mm_salesianinovara', 'Scheda candidato esami scuola secondaria I grado', 1, 'salesianinovara', 9);
 */

$orientamento = 'L';
$formato = 'A3';
$periodo = 29;
$periodo_pagella = "finale" ;

$elenco_studenti = estrai_studenti_classe($id_classe, true);
$parametri_stampa = [
    'id_classe'       => $id_classe,
    'data_day'        => $data_Day,
    'data_month'      => $data_Month,
    'data_year'       => $data_Year,
    'periodo'         => $periodo,
    'orientamento'    => $orientamento,
    'current_key' => $current_key,
];

function genera_stampa(&$pdf, $studente, $parametri_stampa)
{
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Sezione dedicata ai parametri, al dizionario e alle impostazioni specifiche dei campi liberi">
    $id_classe = $parametri_stampa['id_classe'];
    $data_Day = $parametri_stampa['data_day'];
    $data_Month = $parametri_stampa['data_month'];
    $data_Year = $parametri_stampa['data_year'];
    $current_key = $parametri_stampa['current_key'];
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $anno_scolastico_corrente = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $a = explode('/', $anno_scolastico_corrente);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }
    $id_studente = $studente['id_studente'];

    $lingue_straniere = [];
    $materie = estrai_materie_studente($id_studente);
    foreach ($materie as $id => $dati_materia) {
        if ($dati_materia['tipo_materia'] == 'LINGUA STRANIERA') {
            $lingue_straniere[$id] = $dati_materia['descrizione'];
        }
    }
    $voti_pagella = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);

    $dati_commissione = estrai_dati_singola_commissione_da_classe((int) $id_classe);
    if($dati_commissione["id_commissione"] > 0)
    {
        $dati_commissari = estrai_abbinamenti_commissari((int) $dati_commissione["id_commissione"]);
    }

    $data_stampa = $data_Day . "/" . $data_Month . "/" . $data_Year;
    $data_stampa = "__________________";
    $classe_attuale = $studente['classe'];
    $mat_commissioni = array();
    $mat_commissioni = estrai_classi_sottocommissioni_commissione_medie($studente['id_classe']);

    $abbinamenti_commissari = array();
    foreach($mat_commissioni as $commissione)
    {
        if($commissione['id_classe'] == $commissione['id_classe_commissione_medie'])
        {
            $abbinamenti_commissari = estrai_abbinamenti_commissari($commissione['id_commissione']);
            foreach($abbinamenti_commissari['esterni'] as $singolo_abbinamento)
            {

                if($singolo_abbinamento['dati_commissario']['ruolo_commissario_est'] == 'P')
                {
                    $nome_presidente = $singolo_abbinamento['nome']
                                       . ' ' .
                                       $singolo_abbinamento['cognome'];
                }
            }
        }
    }
    
    $consiglio_txt = "";
    $param_consigli = [
        "anno_scolastico" => "{$anno_inizio}_{$anno_fine}"
    ];
    $elenco_consigli = nextapi_call('/school/structure/consiglio_orientativo', 'GET', $param_consigli, $current_key);
    $consigli_tmp = [];
    foreach ($elenco_consigli as $consiglio){
        if ($consiglio['id_consiglio_orientativo_template'] == $studente['id_consiglio_orientativo']) {
            $consigli_tmp[1] = $consiglio['descrizione'];
        }
        if ($consiglio['id_consiglio_orientativo_template'] == $studente['id_consiglio_orientativo2']) {
            $consigli_tmp[2] = $consiglio['descrizione'];
        }
        if ($consiglio['id_consiglio_orientativo_template'] == $studente['id_consiglio_orientativo3']) {
            $consigli_tmp[3] = $consiglio['descrizione'];
        }
    }
    if (!empty($consigli_tmp)) {
        ksort($consigli_tmp);
        $consiglio_txt =  (implode(', ', $consigli_tmp));
    }

    $desc_lingua2 = estrai_dati_materia($studente['id_lingua_2'])['descrizione'];
    if (empty($desc_lingua2)) {
        $desc_lingua2 = '________________________';
        $desc_lingua2 = 'TEDESCO';
    }

    $anno_scolastico_corrente = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $a = explode('/', $anno_scolastico_corrente);
    $anno_inizio = (int) $a[0];
    $anno_fine = (int) $a[1];
    $curriculum_studente = estrai_curriculum_studente((int) $id_studente);
    $anno_scolastico_corrente = $anno_inizio . "/" . $anno_fine;
    $anno_scolastico_precedente = ($anno_inizio - 1) . "/" . ($anno_fine - 1);
    //estraggo i dati di provenienza e di quante volte è ripetente
    $provenienza = '';
    for($cont_curr=0; $cont_curr <count($curriculum_studente); $cont_curr++)
    {
        if($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_precedente)
        {
            if($studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola'] && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_desc'].' '.$curriculum_studente[$cont_curr]['nome_scuola'];
            }
            else
            {
                    $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
            }
        }
        else {
            $classe_attuale = $curriculum_studente[$cont_curr]['classe'];

            if ($curriculum_studente[$cont_curr]['anno_scolastico'] == $anno_scolastico_corrente
                && $classe_attuale == $curriculum_studente[$cont_curr]['classe']
                && $studente['codice_meccanografico'] != $curriculum_studente[$cont_curr]['id_scuola']
                && $studente['codice_meccanografico_secondario'] != $curriculum_studente[$cont_curr]['id_scuola'])
            {
                if (($curriculum_studente[$cont_curr]['esito'] == 'Iscritto') || ($cont_curr <count($curriculum_studente) -1)) {

                    // Nel caso nel curriculum non sia stata inserita la scuola di provenienza
                    if($curriculum_studente[$cont_curr]['nome_scuola'] != "") {
                        $provenienza = $curriculum_studente[$cont_curr]['nome_scuola'];
                    } else {
                        $provenienza = $curriculum_studente[$cont_curr]['classe_tradotta'] . ' ' . $curriculum_studente[$cont_curr]['descrizione'];
                    }
                }
            }
        }
    }

    $numero_volte_iscritto_studente_classe = $studente['mat_esito']['numero_volte_iscritto'];
    $numero_volte_iscritto_studente_classe_trad = '';
    switch ($numero_volte_iscritto_studente_classe) {
        case 1: $numero_volte_iscritto_studente_classe_trad = 'PRIMA';break;
        case 2: $numero_volte_iscritto_studente_classe_trad = 'SECONDA';break;
        case 3: $numero_volte_iscritto_studente_classe_trad = 'TERZA';break;
    }

    $curriculum_studente = estrai_curriculum_studente($studente['id_studente']);
    $boxcurriculum = [];
    $boxcurriculum[0]=$boxcurriculum[1]=$boxcurriculum[2]=$boxcurriculum[3] = $boxcurriculum[4] = "\nClasse ";
    $c = 0;
    // echo "@@@\n$studente[id_studente] - $studente[cognome] $studente[nome] - $numero_volte_iscritto_studente_classe_trad";
    $boxdone_as = [];
    foreach ($curriculum_studente as $curriculum) {
        if ($curriculum['esito_tradotto'] != '' && in_array($curriculum['classe'], [6,7,8]) && (
                $curriculum['esito_tradotto'] == 'Iscritto'
                // ||
                // $anno_scolastico_attuale==$curriculum['anno_scolastico']
                )
            ) {
            // ppre($curriculum);
            $classe = '';
            if($curriculum['descrizione'] == '')
            {
                $classe = $curriculum['classe_desc'] . ' '. $curriculum['nome_scuola'];
            }
            else
            {
                $classe = $curriculum['classe_tradotta'].' '.$curriculum['descrizione'];
            }
            if (!in_array($curriculum['anno_scolastico'], array_keys($boxdone_as))) {
                $boxcurriculum[$c] = "A.S.{$curriculum['anno_scolastico']}\nClasse {$curriculum['classe_tradotta']} ^";
                $c++;
                $boxdone_as[$curriculum['anno_scolastico']] = $curriculum['anno_scolastico'];
            }
        }
    }

//    $prova_italiano = "Prova scelta n. " . $studente['tipo_voto_esame_medie_italiano'];
    $prova_italiano = "Prova scelta n.  . . . . . . .";
//    switch ($studente['tipo_voto_esame_medie_italiano'])
//    {
//        case '1':
//            $prova_italiano = "traccia 1";
//            break;
//        case '2':
//            $prova_italiano = "traccia 2";
//            break;
//        case '3':
//            $prova_italiano = "traccia 3";
//            break;
//        case '4':
//            $prova_italiano = "testo argomentativo";
//            break;
//        case '5':
//            $prova_italiano = "testo espositivo (relazione)";
//            break;
//        case '6':
//            $prova_italiano = "testo narrativo o descrittivo (lettera,diario)";
//            break;
//        case '7':
//            $prova_italiano = "traccia di comprensione e sintesi di un testo letterario, divulgativo, scientifico";
//            break;
//        default :
//            break;
//    }
    $prova_inglese = [];
    $tipi_esame_ing[0] = $studente['tipo_voto_esame_medie_inglese'];
    if(intval($anno_inizio >= 2022)) {
        $tipi_esame_ing = explode(',', $studente['tipo_voto_esame_medie_inglese']);
    }
    foreach ($tipi_esame_ing as $tipo) {
        switch ($tipo)
        {
            case '1':
                $prova_inglese[] = "lettera";
                break;
            case '2':
                $prova_inglese[] = "comprensione testo";
                break;
            case '3':
                $prova_inglese[] = "dialogo";
                break;
            case '4':
                $prova_inglese[] = "questionario a risposta chiusa o aperta";
                break;
            case '5':
                $prova_inglese[] = "completamento di un testo";
                break;
            case '6':
                $prova_inglese[] = "riordino, riscrittura o trasformazione di un testo";
                break;
            case '7':
                $prova_inglese[] = "elaborazione di un dialogo";
                break;
            case '8':
                $prova_inglese[] = "elaborazione di una lettera o mail personale";
                break;
            case '9':
                $prova_inglese[] = "sintesi di un testo";
                break;
            case '10':
                $prova_inglese[] = "prova combinata";
                break;
            default :
                break;
        }
    }
    if (empty($prova_inglese)) {
        $prova_inglese = 'prova n ...';
    } else {
        $prova_inglese = implode(', ', $prova_inglese);
    }
    $prova_lingua_straniera = [];
    $tipi_esame_sec_lin[0] = $studente['tipo_voto_esame_medie_seconda_lingua'];
    if(intval($anno_inizio >= 2022)) {
        $tipi_esame_sec_lin = explode(', ', $studente['tipo_voto_esame_medie_seconda_lingua']);
    }
    foreach ($tipi_esame_sec_lin as $tipo) {
        switch ($tipo)
        {
            case '1':
                $prova_lingua_straniera[] = "lettera";
                break;
            case '2':
                $prova_lingua_straniera[] = "comprensione testo";
                break;
            case '3':
                $prova_lingua_straniera[] = "dialogo";
                break;
            case '4':
                $prova_lingua_straniera[] = "questionario a risposta chiusa o aperta";
                break;
            case '5':
                $prova_lingua_straniera[] = "completamento di un testo";
                break;
            case '6':
                $prova_lingua_straniera[] = "riordino, riscrittura o trasformazione di un testo";
                break;
            case '7':
                $prova_lingua_straniera[] = "elaborazione di un dialogo";
                break;
            case '8':
                $prova_lingua_straniera[] = "elaborazione di una lettera o mail personale";
                break;
            case '9':
                $prova_lingua_straniera[] = "sintesi di un testo";
                break;
            case '10':
                $prova_lingua_straniera[] = "prova combinata";
                break;
            default :
                break;
        }
    }
    if (empty($prova_lingua_straniera)) {
        $prova_lingua_straniera = 'prova n ...';
    } else {
        $prova_lingua_straniera = implode(', ', $prova_lingua_straniera);
    }
    $consiglio = '';
//    if ($studente['consiglio_orientativo_trentino'] != '')
//    {
//        // Estrazione consiglio orientativo
//        $valori_consiglio_orientativo = estrai_valori_consiglio_orientativo_trentino($studente['consiglio_orientativo_trentino']);
//        foreach ($valori_consiglio_orientativo as $valore)
//        {
//            $consiglio .= '-'.$valore['descrizione'].'<br>';
//        }
//    }

    if ($studente['giudizio_sintetico_esame_terza_media']>6) {
//        $giud_1 = "[X] avendo superato l’esame di stato venga dichiarato licenziato con la valutazione di {$studente['giudizio_sintetico_esame_terza_media']}";
        $giud_1 = "[X] avendo superato l’esame di stato venga dichiarato licenziato con la valutazione di ";
    } else {
        $giud_1 = "[  ] avendo superato l’esame di stato venga dichiarato licenziato con la valutazione di ";
    }
    if ($studente['giudizio_sintetico_esame_terza_media'] != '') {
        if ( strpos(strtoupper($studente['giudizio_sintetico_esame_terza_media']), 'LODE') !== false ) {
            $giud_2 = '[X] sulla base degli esiti dell’ Esame e del percorso scolastico triennale, gli venga attribuita la lode';
        }
    } else {
        $giud_2 = '[  ] sulla base degli esiti dell’ Esame e del percorso scolastico triennale, gli venga attribuita la lode';
    }
    if ($studente['esito'] == "Non Licenziato" || $studente['esito'] == "Non Licenziata" ||
        $studente['esito'] == "Non ammesso esame di stato" || $studente['esito'] == "Non ammessa esame di stato" ) {
        $giud_3 = '[X] non avendo superato l’esame di stato, venga dichiarato: non licenziato';
    } else {
        $giud_3 = '[  ] non avendo superato l’esame di stato, venga dichiarato: non licenziato';
    }

    // Dizionario
    $labels = [
        "p4_delibera"   => "<b>La Commissione plenaria</b>, visto il curriculum scolastico e le risultanze dell’esame, delibera che ||IlLa|| candidat||min_oa||",
        "candidato" => 'CANDIDAT||max_oa|| ',
        "nato"  => "nat||min_oa|| a $luogo_nascita (prov. $provincia_nascita) il {$studente['data_nascita_ext']}",
        "iscrizione_n"  => "<b>Iscritt||min_oa||</b> per la $numero_volte_iscritto_studente_classe^ volta alla classe {$studente['classe']}^",
        "lingue_straniere"  => "2^ LINGUA COMUNITARIA: ".$desc_lingua2,
        "p1_note"   => "Per la normativa che regolamenta l'Esame di Stato, rif. N. Min 4155 del 07/02/2023",
        "ammissione" => "Il candidat||min_oa|| è stat||min_oa|| ammess||min_oa|| all'esame con il seguente <b>voto di ammissione: {$studente['voto_ammissione_medie']}</b>",
        "firma_candidato" => "Firma candidat||min_oa||",
        "note"  => "(1) - Valutare in decimi (Dieci e lode, Dieci, Nove, Otto, Sette, Sei).<br>
(2) - Dieci e lode, Dieci, Nove, Otto, Sette, Sei"
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $max_oa = "A";
        $min_eessa = "essa";
        $IlLa = 'la';
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $IlLa = 'il';
        $min_oa = "o";
        $max_oa = "O";
        $min_eessa = "e";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||IlLa||", $IlLa, $labels[$k]);
    }
    //}}} </editor-fold>

    $f = 'Times';
    $fd = 12;
    $page_width = $pdf->getPageWidth();
    $page_dims = $pdf->getPageDimensions();
    $m_sinistro = $page_dims['lm'];
    $m_top = $page_dims['tm'];
    $m_destro = $page_dims['rm'];
    $m_destro = $m_sinistro = 10;
    $half_page = $page_width / 2;
    $wp = $half_page - $m_destro - $m_sinistro;
    $h = 8;
    $x2 = $half_page + $m_sinistro;
    $y_base = 20;
    $pdf->AddPage();
    $pdf->SetAutoPageBreak("off", 1);

    //{{{ <editor-fold defaultstate="collapsed" desc="p4">
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "{$studente['cognome']} {$studente['nome']} ______________________________", 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->SetFont($f, 'B', $fd+4);
    $pdf->CellFitScale($wp, $h, "VALUTAZIONE FINALE", 0, 1, 'C');
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "Motivato giudizio complessivo della Sottocommissione ed eventuale modifica del consiglio orientativo:", 0, 1, false, true, 'L');
    $pdf->CellFitScale($wp, $h, '', 'B', 1);
    $pdf->CellFitScale($wp, $h, '', 'B', 1);
    $pdf->CellFitScale($wp, $h, '', 'B', 1);
    $pdf->CellFitScale($wp, $h, '', 'B', 1);
    $pdf->CellFitScale($wp, $h, '', 'B', 1);
    $pdf->CellFitScale($wp, $h, '', 'B', 1);
    $pdf->ln(5);
    $pdf->writeHTMLCell($wp, 0, '', '', "Valutazione finale della Sottocommissione: ________________ /10", 0, 1, false, true, 'L');
    $pdf->ln(8);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "LA _______ SOTTOCOMMISSIONE", 0, 1, false, true, 'C');
    $pdf->Ln(2);
    $pdf->SetFont($f, '', $fd);
//    foreach($abbinamenti_commissari['totali'] as $commissario)
//    {
//        if($commissario['selezionato'] == 'SI'
//                &&
//            $commissario['dati_commissario']['ruolo_commissario_est'] != 'P')
//        {
//            $pdf->CellFitScale($wp, $h, $commissario['cognome'].' '.$commissario['nome'], 0, 1, 'L');
//        }
//    }
    $pdf->CellFitScale($wp/2, $h, '1 ____________________________________', 0, 0, 'L');
    $pdf->CellFitScale($wp/2, $h, '7 ____________________________________', 0, 1, 'R');
    $pdf->CellFitScale($wp/2, $h, '2 ____________________________________', 0, 0, 'L');
    $pdf->CellFitScale($wp/2, $h, '8 ____________________________________', 0, 1, 'R');
    $pdf->CellFitScale($wp/2, $h, '3 ____________________________________', 0, 0, 'L');
    $pdf->CellFitScale($wp/2, $h, '9 ____________________________________', 0, 1, 'R');
    $pdf->CellFitScale($wp/2, $h, '4 ____________________________________', 0, 0, 'L');
    $pdf->CellFitScale($wp/2, $h, '10 ____________________________________', 0, 1, 'R');
    $pdf->CellFitScale($wp/2, $h, '5 ____________________________________', 0, 0, 'L');
    $pdf->CellFitScale($wp/2, $h, '11 ____________________________________', 0, 1, 'R');
    $pdf->CellFitScale($wp/2, $h, '6 ____________________________________', 0, 0, 'L');
    $pdf->CellFitScale($wp/2, $h, '12 ____________________________________', 0, 1, 'R');
    $pdf->ln(8);
    $pdf->writeHTMLCell($wp, 0, '', '', '', 'B', 1, false, true, 'L');
    $pdf->ln(8);
    $pdf->writeHTMLCell($wp, 0, '', '', $labels['p4_delibera'], 0, 1, false, true, 'L');
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', $studente['cognome'].' '.$studente['nome'], 0, 1, false, true, 'C');
    $pdf->Ln(2);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "    ".'<input type="checkbox" name="box" value="0" readonly="true" />'." – avendo superato l’esame di <i>Stato</i> venga dichiarato <b>licenziato</b> con il giudizio di: (2) __________ / 10 _________", 0, 1, false, true, 'L');
    $pdf->Ln(2);
    $pdf->writeHTMLCell($wp, 0, '', '', "    ".'<input type="checkbox" name="box" value="0" readonly="true" />'." – non avendo superato l’esame di <i>Stato</i>, venga dichiarato <b>non licenziato</b>.", 0, 1, false, true, 'L');
    $pdf->ln(7);
    $pdf->writeHTMLCell($wp, 0, '', '', "{$studente['descrizione_comuni']}, $data_stampa", 0, 1, false, true, 'L');
    $pdf->Ln(2);
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "Il Presidente", 0, 1, false, true, 'R');
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="p1">
    $y_rel = $y_base + 5;
    $pdf->SetXY($x2, $y_rel);
    $pdf->SetFont($f, '', 12);
//    $logo = estrai_parametri_singoli('LOGO_NOME', $id_classe, 'classe');
//    if(file_exists("/var/www-source/mastercom/immagini_scuola/" . $logo))
//    {
//        $pdf->Image('/var/www-source/mastercom/immagini_scuola/' . $logo, $x2, '', $wp, 50, 'JPG', false);
//        $pdf->SetFont($f, 'B', 12);
//        $pdf->SetX($x2);
//        $pdf->CellFitScale(200, 35, "", 0, 1, 'C');
//        $pdf->Ln(25);
//    }
//    else
//    {
//        if(intval($dati_classe[0]["id_sede"]) > 0)
//        {
//            $dati_sede = estrai_sede_singola($dati_classe[0]["id_sede"]);
//            $intestazione_stampa = $dati_sede["descrizione_tipo_scuola"] . " " . $dati_sede["descrizione_scuola"];
//            $sotto_intestazione_stampa = $dati_sede["indirizzo"] . " -- Tel." . $dati_sede["telefono"] . " , ".$dati_sede["descrizione_comune"];
//            $pdf->SetFont($f, 'B', 12);
//            $pdf->CellFitScale(0, 10, $intestazione_stampa, 0, 1, "C");
//            $pdf->SetX($x2);
//            $pdf->SetFont($f, '', 10);
//            $pdf->CellFitScale(0, 10, $sotto_intestazione_stampa, 0, 1, "C");
//        }
//    }
    $pdf->writeHTMLCell($wp, 0, $x2, '', "SCUOLA SECONDARIA DI I° GRADO<br>
<b>SAN LORENZO</b><br>
PARITARIA D.R. 2789 del 15-01-2002<br>
B.do Lamarmora 14 - 28100 NOVARA<br>
Tel. 0321.668611 - Fax 0321.36848", 0, 1, false, true, 'C');
    $pdf->ln(12);
    $pdf->SetFont($f, '', 12);
    $pdf->SetX($x2);
    $pdf->CellFitScale(25, $h, "Anno Scolastico ", 0, 0, 'L');
    $pdf->CellFitScale(25, $h, $anno_scolastico_attuale, 0, 0, 'R');
    $pdf->CellFitScale(0, $h, "Classe TERZA - Sez. ".$studente['sezione'], 0, 1, 'R');
    $pdf->SetX($x2);
    $pdf->CellFitScale(55, $h, "SESSIONE UNICA " , 0, 0, 'L');
    $pdf->CellFitScale(0, $h, "___________ Sottocommissione", 0, 1, 'R');
    $pdf->Ln(14);
    $pdf->SetFont($f, 'B', 18);
    $pdf->SetX($x2);
    $pdf->CellFitScale($wp, $h, "ESAME DI STATO", 0, 1, 'C');
    $pdf->SetX($x2);
    $pdf->SetFont($f, 'B', 14);
    $pdf->CellFitScale($wp, $h, "CONCLUSIVO DEL PRIMO CICLO DI ISTRUZIONE", 0, 1, 'C');
    $pdf->Ln(9);
    $pdf->SetX($x2);
    $pdf->SetFont($f, 'B', 11);
    $pdf->CellFitScale($wp, $h, "SCHEDA PERSONALE DEL CANDIDATO", 0, 1, 'C');
    $pdf->Ln(2);
    $pdf->writeHTMLCell($wp, 0, $x2, '', "con verbale delle valutazioni sulle prove scritte<br>
e sul colloquio pluridisciplinare e risultato finale</b>", 0, 1, false, true, 'C');
    $pdf->Ln(12);
    $pdf->SetX($x2);
    $pdf->SetFont($f, '', 12);
    $pdf->CellFitScale(0, $h, $labels['candidato'], 0, 1, 'C');
    $pdf->SetFont($f, 'B', 12);
    $pdf->SetX($x2);
    $pdf->CellFitScale(0, $h,$studente['cognome'] . " " . $studente['nome'], 0, 1, 'C');
    $pdf->SetX($x2);
    $pdf->SetFont($f, '', 12);
    $pdf->writeHTMLCell(0, $h, $x2, '', $labels['nato'], 0, 0, false, true, 'C');
    $pdf->Ln(8);
    $pdf->SetX($x2);
    $pdf->CellFitScale(0, $h,"CLASSI FREQUENTATE NELLA SCUOLA SECONDARIA DI 1° GRADO", 0, 1, 'C');
    $pdf->SetX($x2);
    $pdf->MultiCell($wp*.2, 30, $boxcurriculum[0], $border=1, $align='L', $fill=0, $ln=0, $x='', $y='', $reseth=true, $stretch=0, $ishtml=false, $autopadding=true, $maxh=0, 'B', true);
    $pdf->MultiCell($wp*.2, 30, $boxcurriculum[1], $border=1, $align='L', $fill=0, $ln=0, $x='', $y='', $reseth=true, $stretch=0, $ishtml=false, $autopadding=true, $maxh=0, 'B', true);
    $pdf->MultiCell($wp*.2, 30, $boxcurriculum[2], $border=1, $align='L', $fill=0, $ln=0, $x='', $y='', $reseth=true, $stretch=0, $ishtml=false, $autopadding=true, $maxh=0, 'B', true);
    $pdf->MultiCell($wp*.2, 30, $boxcurriculum[3], $border=1, $align='L', $fill=0, $ln=0, $x='', $y='', $reseth=true, $stretch=0, $ishtml=false, $autopadding=true, $maxh=0, 'B', true);
    $pdf->MultiCell($wp*.2, 30, $boxcurriculum[4], $border=1, $align='L', $fill=0, $ln=1, $x='', $y='', $reseth=true, $stretch=0, $ishtml=false, $autopadding=true, $maxh=0, 'B', true);
    $pdf->Ln(5);
    $pdf->writeHTMLCell($wp, $h, $x2, '', "{$labels['lingue_straniere']} ", 0, 1, false, true, 'C');
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="p2">
    $pdf->AddPage();
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "{$studente['cognome']} {$studente['nome']} ______________________________", 0, 0, false, true, 'C');
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, $x2, '', "{$studente['cognome']} {$studente['nome']} ______________________________", 0, 1, false, true, 'C');
    $pdf->ln(2);
    $pdf->SetFont($f, 'I', 10);
    $pdf->writeHTMLCell($wp, $h, '', '', "Il Candidato è stato ammesso all’esame con il seguente <b>voto di ammissione </b>: ", 0, 1);
    $pdf->writeHTMLCell($wp, $h, '', '', "", 'B', 1);
    $pdf->ln(3);
    $pdf->writeHTMLCell($wp, $h, '', '', "Il Consiglio di classe ha formulato inoltre il seguente <b>consiglio orientativo</b> sulle scelte successive:", 0, 1);
    $pdf->writeHTMLCell($wp, $h, '', '', "$consiglio_txt", 'B', 1);
    $pdf->ln(3);
    $pdf->SetFont($f, 'B', 14);
    $pdf->CellFitScale($wp, $h, "PROVE D'ESAME", 0, 1, 'C');
    $pdf->SetFont($f, 'B', 12);
    $pdf->CellFitScale($wp, $h, "PROVE SCRITTE", 0, 1, 'L');
    $pdf->Ln(2);
    $pdf->SetFont($f, '', 9);
    $tbl =
        '<table border="0.1px" cellpadding="3">'
            . '<tr>'
                . '<td colspan="5"><b>Italiano (Traccia svolta n° . . . . . . .)</b></td>'
            . '</tr>'
            . '<tr align="center">'
                . '<td width="10%"></td>'
                . '<td width="13%"></td>'
                . '<td width="40%"></td>'
                . '<td width="10%"><b>SI</b></td>'
                . '<td width="10%"><b>NO</b></td>'
                . '<td width="17%"><b>PARZIALMENTE</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td rowspan="2">Contenuto</td>'
                . '<td>Produzione</td>'
                . '<td>Aderenza alla traccia; ricchezza,
varietà; originalità; coerenza rispetto
alla tipologia testuale.</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Sintesi e comprensione<br><small>(traccia 3)</small></td>'
                . '<td>Comprensione dei contenuti; capacità di sintesi.</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td colspan="2">Capacità espositiva</td>'
                . '<td>Adeguata rispetto alla tipologia
testuale.<br>Rielaborazione contenuti e lessico (traccia 3).</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td colspan="2">Forma e ortografia</td>'
                . '<td>Chiara, corretta, lessicalmente
appropriata e ricca.</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td align="right" colspan="6" style="line-height:20px;">Valutazione: _____________________</td>'
            . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell($wp, $h, '', '', $tbl, 0, 1);
    $pdf->Ln(5);
    $pdf->SetFont($f, '', 9);
    $tbl =
        '<table border="0.1px" cellpadding="3">'
            . '<tr>'
                . '<td colspan="6"><b>Lingua comunitaria INGLESE (Prova di Comprensione)</b></td>'
            . '</tr>'
            . '<tr align="center">'
                . '<td width="20%"><b>COMPETENZA</b></td>'
                . '<td width="16%"><b>LIVELLO
AVANZATO
(OTTIMO)</b></td>'
                . '<td width="16%"><b>LIVELLO
INTERMEDIO
(BUONO)</b></td>'
                . '<td width="16%"><b>LIVELLO BASE
(SUFFICIENTE)</b></td>'
                . '<td width="16%"><b>NON DEL TUTTO
RAGGIUNTO
(INSUFFICIENTE)</b></td>'
                . '<td width="16%"><b>NON RAGGIUNTA
(GRAVEMENTE
INSUFFICIENTE)</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Lingua</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Comprensione</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            // . '<tr>'
            //     . '<td align="right" colspan="6" style="line-height:20px;">Valutazione: _____________________</td>'
            // . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell($wp, $h, '', '', $tbl, 0, 1);
    $pdf->Ln(5);
    $pdf->SetFont($f, '', 9);
    $tbl =
        '<table border="0.1px" cellpadding="3">'
            . '<tr>'
                . '<td colspan="6"><b>Lingua comunitaria INGLESE (Prova di Produzione Scritta) </b></td>'
            . '</tr>'
            . '<tr align="center">'
                . '<td width="20%"><b>COMPETENZA</b></td>'
                . '<td width="16%"><b>LIVELLO
AVANZATO
(OTTIMO)</b></td>'
                . '<td width="16%"><b>LIVELLO
INTERMEDIO
(BUONO)</b></td>'
                . '<td width="16%"><b>LIVELLO BASE
(SUFFICIENTE)</b></td>'
                . '<td width="16%"><b>NON DEL TUTTO
RAGGIUNTA
(INSUFFICIENTE)</b></td>'
                . '<td width="16%"><b>NON RAGGIUNTA
(GRAVEMENTE
INSUFFICIENTE)</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Lingua</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Contenuto</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            // . '<tr>'
            //     . '<td align="right" colspan="6" style="line-height:20px;">Valutazione: _____________________</td>'
            // . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell($wp, $h, '', '', $tbl, 0, 1);
    $pdf->Ln(5);
    $pdf->SetFont($f, '', 9);
    $tbl =
        '<table border="0.1px" cellpadding="2">'
            . '<tr>'
                . '<td colspan="6"><b>Seconda lingua comunitaria '.$desc_lingua2.'  (Prova di Comprensione)</b></td>'
            . '</tr>'
            . '<tr align="center">'
                . '<td width="20%"><b>COMPETENZA</b></td>'
                . '<td width="16%"><b>LIVELLO
AVANZATO
(OTTIMO)</b></td>'
                . '<td width="16%"><b>LIVELLO
INTERMEDIO
(BUONO)</b></td>'
                . '<td width="16%"><b>LIVELLO BASE
(SUFFICIENTE)</b></td>'
                . '<td width="16%"><b>NON DEL TUTTO
RAGGIUNTA
(INSUFFICIENTE)</b></td>'
                . '<td width="16%"><b>NON RAGGIUNTA
(GRAVEMENTE
INSUFFICIENTE)</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Lingua</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Comprensione</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            // . '<tr>'
            //     . '<td align="right" colspan="6" style="line-height:20px;">Valutazione: _____________________</td>'
            // . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell($wp, $h, '', '', $tbl, 0, 1);
    $pdf->ln(4);
    $pdf->writeHTMLCell($wp, $h, '', '', "<b>Valutazione:</b> ______________________", 0, 0, false, true, 'R');
    //}}} </editor-fold>
    //{{{ <editor-fold defaultstate="collapsed" desc="p3">
    $pdf->SetXY($x2, $y_base+5);
    $pdf->ln(4);
    $pdf->SetFont($f, '', 9);
    $tbl = '<table border="0.1px" cellpadding="2">'
            . '<tr>'
                . '<td align="left" colspan="6"><b>Competenze logico-matematiche (Quesiti svolti N. _________________)</b></td>'
            . '</tr>'
            . '<tr align="center">'
                . '<td width="25%"><b>COMPETENZA</b></td>'
                . '<td width="15%"><b>LIVELLO
AVANZATO
(OTTIMO)</b></td>'
                . '<td width="15%"><b>LIVELLO
INTERMEDIO
(BUONO)</b></td>'
                . '<td width="15%"><b>LIVELLO BASE
(SUFFICIENTE)</b></td>'
                . '<td width="15%"><b>NON DEL TUTTO
RAGGIUNTA
(INSUFFICIENTE)</b></td>'
                . '<td width="15%"><b>NON
RAGGIUNTA
(GRAVEMENTE
INSUFFICIENTE)</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Conoscenze</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Applicazione di procedimenti</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Risoluzione di problemi</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Uso del linguaggio</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td align="right" colspan="6" style="line-height:20px;">Valutazione: _____________________</td>'
            . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell($wp, 0, $x2, '', $tbl, 0, 1, false, true, 'C');
    $pdf->ln(4);
    $pdf->SetX($x2);
    $pdf->CellFitScale($wp, 0, "", 'B', 1);
    $pdf->ln(1);
    $pdf->SetFont($f, 'B', 12);
    $pdf->SetX($x2);
    $pdf->CellFitScale($wp, 0, "COLLOQUIO PLURIDISCIPLINARE", 0, 1);
    $pdf->SetFont($f, '', 10);
    $pdf->ln(5);
    $pdf->SetX($x2);
    $pdf->CellFitScale($wp, 0, "Titolo dell’elaborato: ___________________________________________", 0, 1, 'C');
    $pdf->SetFont($f, '', 9);
    $pdf->ln(3);
    $tbl = '<table align="center" border="0.1px" cellpadding="3">'
            . '<tr align="center">'
                . '<td width="25%"><b>CRITERI</b></td>'
                . '<td width="15%">Per nulla
adeguata</td>'
                . '<td width="15%">Poco
adeguata</td>'
                . '<td width="15%">Appena
adeguata</td>'
                . '<td width="15%">Adeguata</td>'
                . '<td width="15%">Pienamente
adeguata</td>'
            . '</tr>'
            . '<tr>'
                . '<td><b>ORIGINALITÀ</b>
dei contenuti
dell’elaborato</td>
                    <td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'


            . '<tr>'
                . '<td><b>COERENZA</b>
dell’elaborato
con l’argomento scelto</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td><b>QUALITÀ</b>
della realizzazione fisica
dell’elaborato</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>COMPETENZE <b>COMUNICATIVE</b></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'

            . '<tr>'
                . '<td>CAPACITÀ di
<b>ARGOMENTAZIONE,
DI PENSIERO CRITICO E
RIFLESSIVO</b></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td><b>COMPETENZA LOGICA</b>
nell’organizzazione
dei concetti e nei collegamenti
pluridisciplinari</td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
            . '<tr>'
                . '<td>Competenze di
<b>EDUCAZIONE CIVICA</b></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
                . '<td></td>'
            . '</tr>'
        . '</table>';
    $pdf->writeHTMLCell($wp, 0, $x2, '', $tbl, 0, 1, false, true, 'C');
    $pdf->Ln(4);
    $pdf->writeHTMLCell($wp, $h, $x2, '', '(Eventuale giudizio sul colloquio):', 1, 1, false, true, 'L');
    $pdf->writeHTMLCell($wp, $h*2, $x2, '', '', 1, 1, false, true, 'L');
    $pdf->writeHTMLCell($wp, 10, $x2, '', '<br><br>Valutazione: _____________________', 1, 1, false, true, 'R');
    $pdf->Ln(4);
    $pdf->writeHTMLCell($wp, $h, $x2, '', '', 'B', 1, false, true, 'R');
    $pdf->Ln(7);
    $pdf->writeHTMLCell($wp/2, $h, $x2, '', "{$studente['descrizione_comuni']}, $data_stampa", 0, 0, false, true, 'L');
    $pdf->writeHTMLCell($wp/2, $h, '', '', 'Firma del candidato: ____________________________________', 0, 1, false, true, 'R');
    //}}} </editor-fold>

    //}}} </editor-fold>
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Scheda candidato ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
