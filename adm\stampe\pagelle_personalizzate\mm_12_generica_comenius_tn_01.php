<?php

/*
 * Tipo: Pagella primaria classi prime e seconde
 * Richie<PERSON> da: IC Comenius
 * Data: 2017/01/20
 *
 * Materie particolari:
 * - Giudizio Globale (necessita campo libero omonimo)
 * - Materia con tipo Opzionale (necessita campo libero omonimo)
 */

$stampa_personalizzata = 'SI';

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
// -- PARAMETRI
// Campo Giudizio Globale
$campo_giudizio = "Giudizio Globale";
// Campo Materia Opzionale
$campo_materia_opzionale = "Opzionale";
// Campo Attività Opzionale
$campo_attivita_opzionale = "Attivit&agrave; opzionali";
// Campo Attività Alternative
$campo_attivita_alternativa = "Attivit&agrave; alternativa all&#039;IRC";
// Campo Consiglio Orientativo
$campo_consiglio_orientativo = "Consiglio Orientativo";

$campo_materia_annotazioni = "Annotazion";

$id_classe = $parametri_stampa['id_classe'];
$data_Day = $parametri_stampa['data_day'];
$data_Month = $parametri_stampa['data_month'];
$data_Year = $parametri_stampa['data_year'];
$periodo_pagella = $parametri_stampa['periodo_pagella'];
$firma_digitale = $parametri_stampa['firma_digitale'];

$anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $arr_voti_finale['giudizio_primo_quadrimestre'] = '';
    $arr_voti_finale['giudizio_finale'] = '';
    $arr_voti_finale['opzionale_primo_quadrimestre'] = '';
    $arr_voti_finale['attivita_opzionali_primo_quadrimestre'] = '';
    $arr_voti_finale['attivita_alternativa_primo_quadrimestre'] = '';
    $id_studente = $studente['id_studente'];
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);

    // Estrazione voti
    $voti_pagella_primo_quadrimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 27, $studente['id_studente']);
    $voti_pagella_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);

    foreach ($materie_studente as $materia) {
        $id_materia = $materia['id_materia'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV' && (!in_array($materia['tipo_materia'], ['RELIGIONE','ALTERNATIVA']))) {
            $arr_voti_finale['primo_quadrimestre'][$id_materia]['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['primo_quadrimestre'][$id_materia] = $voti_pagella_primo_quadrimestre[$id_materia];
            $arr_voti_finale['finale'][$id_materia]['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['finale'][$id_materia] = $voti_pagella_finale[$id_materia];
        }
        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $arr_voti_finale['primo_quadrimestre'][$id_materia]['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['primo_quadrimestre'][$id_materia] = $voti_pagella_primo_quadrimestre[$id_materia];
            $arr_voti_finale['finale'][$id_materia]['descrizione'] = $materia['descrizione'];
            $arr_voti_finale['finale'][$id_materia] = $voti_pagella_finale[$id_materia];
        }

        // Giudizio Globale (materia condotta)
        else if ($materia['tipo_materia'] == 'CONDOTTA' && strpos($materia['descrizione'], 'GLOBALE') === false) {
            foreach ($voti_pagella_primo_quadrimestre[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_giudizio) {
                    $arr_voti_finale['giudizio_primo_quadrimestre'] = estrai_valore_campo_libero_selezionato($campo_libero);
                }
                }

            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_giudizio) {
                    $arr_voti_finale['giudizio_finale'] = estrai_valore_campo_libero_selezionato($campo_libero);
                }
            }
        }
        // Materia opzionale
        else if ($materia['tipo_materia'] == 'OPZIONALE') {
            foreach ($voti_pagella_primo_quadrimestre[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_materia_opzionale) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
//                    $arr_voti_finale['opzionale_primo_quadrimestre'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['opzionale_primo_quadrimestre'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
            }

            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_materia_opzionale) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                   $arr_voti_finale['opzionale_finale'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['opzionale_finale'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
            }
        }
        // Attività opzionali
        else if (strpos($materia['descrizione'], 'GLOBALE') !== false) {
            foreach ($voti_pagella_primo_quadrimestre[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_attivita_opzionale) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                   $arr_voti_finale['attivita_opzionali_primo_quadrimestre'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['attivita_opzionali_primo_quadrimestre'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
                elseif ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] === 0) && $campo_libero['nome'] == $campo_attivita_alternativa) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                   $arr_voti_finale['attivita_alternativa_primo_quadrimestre'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['attivita_alternativa_primo_quadrimestre'] .= estrai_valore_campo_libero_selezionato($campo_libero);

                    }                }
                elseif ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_consiglio_orientativo) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                   $arr_voti_finale['consiglio_orientativo_primo_quadrimestre'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['consiglio_orientativo_primo_quadrimestre'] .= estrai_valore_campo_libero_selezionato($campo_libero);

                    }                }
                elseif ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_giudizio) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                    // $arr_voti_finale['giudizio_primo_quadrimestre'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['giudizio_primo_quadrimestre'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
            }

            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_attivita_opzionale) {                        $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                   $arr_voti_finale['attivita_opzionali_finale'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['attivita_opzionali_finale'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
                elseif ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] === 0) && $campo_libero['nome'] == $campo_attivita_alternativa) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                   $arr_voti_finale['attivita_alternativa_finale'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['attivita_alternativa_finale'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
                elseif ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_consiglio_orientativo) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                   $arr_voti_finale['consiglio_orientativo_finale'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['consiglio_orientativo_finale'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
                elseif ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && $campo_libero['nome'] == $campo_giudizio) {
                    $val =  estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($val != '') {
                    // $arr_voti_finale['giudizio_finale'] .= decode($campo_libero['nome']) .": ";
                    $arr_voti_finale['giudizio_finale'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
            }
        }

        // Campo libero BES
        if ($studente['bes'] == 'SI')
        {
            foreach ($voti_pagella_primo_quadrimestre[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && strpos($campo_libero['nome'], 'BES') != false) {
                    $arr_bes['primo_quadrimestre'] =  estrai_valore_campo_libero_selezionato($campo_libero);
                }
            }

            foreach ($voti_pagella_finale[$id_materia]['campi_liberi'] as $campo_libero) {
                if ((in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) && strpos($campo_libero['nome'], 'BES') != false) {
                    $arr_bes['finale'] = estrai_valore_campo_libero_selezionato($campo_libero);
                }
            }
        }
    }

    if ($arr_voti_finale['opzionale_primo_quadrimestre'] != '') {
        // $arr_voti_finale['opzionale_primo_quadrimestre'] = decode($campo_materia_opzionale) . ": ".$arr_voti_finale['opzionale_primo_quadrimestre'];
    }
    if ($arr_voti_finale['attivita_alternativa_primo_quadrimestre'] != '') {
        // $arr_voti_finale['attivita_alternativa_primo_quadrimestre'] = decode($campo_attivita_alternativa) . ": ".$arr_voti_finale['attivita_alternativa_primo_quadrimestre'];
    }
    if ($arr_voti_finale['attivita_opzionali_primo_quadrimestre'] != '') {
        // $arr_voti_finale['attivita_opzionali_primo_quadrimestre'] = decode($campo_materia_opzionale) . ": ".$arr_voti_finale['attivita_opzionali_primo_quadrimestre'];
    }
    if ($arr_voti_finale['opzionale_finale'] != '') {
        // $arr_voti_finale['opzionale_finale'] = decode($campo_materia_opzionale) . ": ".$arr_voti_finale['opzionale_finale'];
    }
    if ($arr_voti_finale['attivita_alternativa_finale'] != '') {
        // $arr_voti_finale['attivita_alternativa_finale'] = decode($campo_attivita_alternativa) . ": ".$arr_voti_finale['attivita_alternativa_finale'];
    }
    if ($arr_voti_finale['attivita_opzionali_finale'] != '') {
        // $arr_voti_finale['attivita_opzionali_finale'] = decode($campo_materia_opzionale) . ": ".$arr_voti_finale['attivita_opzionali_finale'];
    }

// Dizionario temporaneo
    $labels = [
        "p1_intestazione"     => "ISTITUTO COMPRENSIVO << Johannes Amos Comenius >>",
        "p1_intestazione_1"   => "via Ponte Alto, 2/1 - COGNOLA-TRENTO",
        "p1_titolo"           => "DOCUMENTO DI VALUTAZIONE",
        "p1_desc_alunno"      => "Student||min_eessa||",
        "p1_nascita_data"     => "Nat||min_oa|| il",
        "p1_nascita_luogo"    => "a",
        "p1_nascita_prov"     => "prov.",
        "p1_iscriz_classe"    => "Iscritt||min_oa|| alla classe",
        "p1_iscriz_sezione"   => "sezione",
        "p1_anno_scolastico"  => "anno scolastico",
        "p1_titolo_materia"   => "DISCIPLINE",
        "p1_titolo_voto"      => "VALUTAZIONE",
        "p1_titolo_voto_1"    => "I^ quadrimestre",
        "p1_titolo_voto_2"    => "II^ quadrimestre",
        "p1_titolo_note"      => "Annotazioni",
        "p1_titolo_opzionali" => "ATTIVITA' OPZIONALI ED EVENTUALE ATTIVITA' ALTERNATIVA ALL'IRC",
        "p1_titolo_giudizio"  => "GIUDIZIO GLOBALE",
        "titolo_attestato"    => "ATTESTATO",
        "testo_attestato_0"   => "Visti gli atti d'ufficio si attesta che lo studente ha frequentato almeno i tre quarti dell'orario annuale.",
        "testo_attestato"     => "Vista la valutazione del Consiglio di Classe si attesta che:",
        "testo_attestato_2"   => " alla classe successiva",
        "tipo_dirigente"      => "Il Dirigente Scolastico",
        "firma_autografa_1"   => "Questa nota, se trasmessa in forma cartacea, costituisce copia dell’originale informatico firmato digitalmente, predisposto e conservato presso questa Amministrazione in conformità alle regole tecniche (Artt. 3 bis e 71 D. Lgs. 82/2005).",
        "firma_autografa_2"   => "La firma autografa è sostituita dall’indicazione a stampa del nominativo del responsabile (art. 3 D. Lgs39/1993).",
        "firma_digitale"      => "(Documento firmato digitalmente)"
    ];

//    $labels = estrai_dizionaro_stampe_personalizzate('pagella_12_mm_02');
    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
    }

// -- PAGINA 1
    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak("off", 1);

    // Inserimento logo
     $x_rel = 30;
    $y_rel = 7;

    $pdf->Image('immagini_scuola/logo_repubblica.jpg', $x_rel - 15, $y_rel, 10);
    $pdf->SetXY($x_rel - 25, $y_rel + 12);

    $pdf->SetFont('helvetica', 'B', 6);
    $pdf->CellFitScale(0, 0, 'REPUBBLICA ITALIANA', 0, 0, "L");

    // Inserimento logo
    if (file_exists("/var/www-source/mastercom/immagini_scuola/logo_per_pagelle.png")) {
        $val_y = 0;
		$logo = "/var/www-source/mastercom/immagini_scuola/logo_per_pagelle.png";
		$pdf->Image($logo, 0, $y_rel - 2, 25, 25, 'png','', 'N', false, 300, 'C', false, false, 0, 'CM');
    } else {
        inserisci_intestazione_pdf($pdf, (int) $id_classe);
    }

    $pdf->Image('immagini_scuola/logo_trento.jpg', $x_rel + 143, $y_rel, 10);
    $pdf->SetXY($x_rel + 125, $y_rel + 15);

    $pdf->SetFont('helvetica', 'B', 6);
    $pdf->CellFitScale(0, 0, 'PROVINCIA AUTONOMA DI TRENTO', 0, 1, "L");

    $pdf->ln(7);

    //Intestazione
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->CellFitScale(0, 8, $labels['p1_intestazione'], 0, 1, "C");
    $pdf->CellFitScale(0, 0, $labels['p1_intestazione_1'], 0, 1, "C");
    $pdf->SetFont('helvetica', 'B', 18);

    $pdf->ln(1);

    //Intestazione
    $pdf->SetFont('helvetica', 'B', 18);
    $pdf->CellFitScale(0, 8, $labels['p1_titolo'], 0, 1, "C");
    $pdf->SetFont('helvetica', 'B', 11);
    $pdf->CellFitScale(0, 0, $labels['p1_anno_scolastico'] . ' ' . $anno_scolastico_attuale, 0, 1, 'C');

    $pdf->ln(5);

    // Riga nome alunno
    $pdf->SetFont('helvetica', 0, 10);
    $pdf->CellFitScale(30, 0, $labels['p1_desc_alunno'], 0, 0, 'L');
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->CellFitScale(110, 0, $studente['cognome'] . " " . $studente['nome'], 0, 0, 'C');
    // Riga data e luogo di nascita
    $pdf->SetFont('helvetica', 0, 10);
    //data
    $pdf->CellFitScale(15, 0, $labels['p1_nascita_data'], 0, 0, 'L');
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->CellFitScale(25, 0, $studente[71], 0, 1, 'C');
    $pdf->ln(2);
    //luogo
    $pdf->SetFont('helvetica', 0, 10);
    $pdf->CellFitScale(15, 0, $labels['p1_nascita_luogo'], 0, 0, 'L');
    $pdf->SetFont('helvetica', 'B', 10);

    $luogo_nascita_finale = ($studente['descrizione_nascita'] != "" && $studente['descrizione_nascita'] != 'COMUNE ESTERO') ? $studente['descrizione_nascita'] : $studente['citta_nascita_straniera'] ;
    $luogo_nascita_finale = $luogo_nascita_finale != "" ? $luogo_nascita_finale : $studente['descrizione_stato_nascita'] ;

    $pdf->CellFitScale(95, 0, $luogo_nascita_finale, 0, 0, 'C');

    //provincia
    $pdf->SetFont('helvetica', 0, 10);
    $pdf->CellFitScale(15, 0, "(" . $labels['p1_nascita_prov'], 0, 0, 'C');
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->CellFitScale(7, 0, $studente['provincia_nascita_da_comune'], 0, 0, 'C');
    $pdf->SetFont('helvetica', '', 10);
    $pdf->CellFitScale(5, 0, ")", 0, 1, 'L');

    $pdf->ln(2);

    // Riga iscrizione
    $pdf->SetFont('helvetica', 0, 10);
    // Testo e classe
    $pdf->CellFitScale(35, 0, $labels['p1_iscriz_classe'], 0, 0, 'L');
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->CellFitScale(10, 0, $studente['classe'], 0, 0, 'C');
    // Sezione
    $pdf->SetFont('helvetica', 0, 10);
    $pdf->CellFitScale(25, 0, $labels['p1_iscriz_sezione'], 0, 0, 'C');
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->CellFitScale(110, 0, $studente['sezione'], 0, 1, 'L');

    $pdf->ln(2);

    // Dati scuola
    $pdf->SetFont('helvetica', 0, 10);
    $pdf->CellFitScale(20, 0, 'della ', 0, 0, 'L');
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->CellFitScale(160, 0, $studente['descrizione_indirizzi'], 0, 1, 'L');

    $pdf->ln(5);

    // Se non è BES
//    if ($studente['bes'] != 'SI')
    if (true)
    {
        // --- VOTI PRIMO QUADRIMESTRE
        // Intestazione materie
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->CellFitScale(80, 0, $labels['p1_titolo_materia'], 0, 0, 'C');
        $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
        $pdf->CellFitScale(55, 0, $labels['p1_titolo_voto'], 0, 0, 'C');
        $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
        $pdf->CellFitScale(35, 0, $labels['p1_titolo_note'], 0, 1, 'C');
        $pdf->CellFitScale(80, 0, '', "B", 0, 'C');
        $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
        $pdf->CellFitScale(55, 0, $labels['p1_titolo_voto_1'], "B", 0, 'C');
        $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
        $pdf->CellFitScale(35, 0, '', "B", 1, 'C');

        $pdf->ln(1);

        foreach ($arr_voti_finale['primo_quadrimestre'] as $voto) {
            $voto_tradotto = "";

            foreach ($voto['significati_voto'] as $significato) {
                if ($significato['voto'] == $voto['voto_pagellina']) {
                    $voto_tradotto = $significato['valore'];
                }
            }

            $riga = $pdf->MultiCellNbLines(70, $voto['descrizione']) * 5;

            $voto['descrizione'] = str_replace('(MM).', '', $voto['descrizione']);
            $annotazione = '';

            $pdf->SetFont('helvetica', 'I', 9);
            $pdf->MultiCell(80, $riga, $voto['descrizione'], "B", 'L', false, 0, "", "", true, 0, false, true, $riga, "M");
            $pdf->MultiCell(5, $riga, "", 0, 'L', false, 0);
            $pdf->MultiCell(55, $riga, $voto_tradotto, "B", 'C', false, 0, "", "", true, 0, false, true, $riga, "M");
            $pdf->MultiCell(5, $riga, "", 0, 'L', false, 0);

            foreach ($voto['campi_liberi'] as $campo_libero) {
                if (strpos($campo_libero['nome'], $campo_materia_annotazioni) !== false) {
                    $annotazione = estrai_valore_campo_libero_selezionato($campo_libero);
                }
            }

            $pdf->MultiCell(35, $riga, $annotazione, "B", 'C', false, 1, "", "", true, 0, false, true, $riga, "M");
            $pdf->ln(1);
        }

        $pdf->ln(4);
        // Giudizio materia opzionale
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->MultiCell(180, 0, $labels['p1_titolo_opzionali'], "TLR",'C', false, 1, "", "", true, 0, false, true, 20, "T");
        $pdf->SetFont('helvetica', 0, 10);
        $pdf->MultiCell(180, 0, $arr_voti_finale['opzionale_primo_quadrimestre']."\n".$arr_voti_finale['attivita_opzionali_primo_quadrimestre']."\n".$arr_voti_finale['attivita_alternativa_primo_quadrimestre'], "BRL",'L', false, 1, "", "", true, 0 );
        $y_rel = $pdf->GetY();

    } else {

        $x_base = 10;
        $y_rel = $pdf->GetY();
        $pdf->SetFillColor(255, 255, 255);
        $pdf->SetXY($x_base, $y_rel);
        $pdf->CellFitScale(180, 105, '', 1, 0, 'L', 1);

        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->MultiCell(180, 0, $labels['p1_titolo_voto_1'], "TLR",'C', false, 1, "", "", true, 0, false, true, 20, "T");
        $pdf->SetFont('helvetica', 0, 10);
        $pdf->MultiCell(180, 0, $arr_bes['primo_quadrimestre'], "BRL",'L', false, 1, "", "", true, 0 );

        $y_rel = $pdf->GetY() + 80;

    }

    // Giudizio Globale
    $x_base = 10;
    $y_rel = $y_rel + 5;
    $pdf->SetFillColor(255, 255, 255);
    $pdf->SetXY($x_base, $y_rel);
    $pdf->CellFitScale(180, 50, '', 1, 0, 'L', 1);
    $pdf->SetXY($x_base, $y_rel);
    $pdf->SetFont('helvetica', 'B', 11);
    $pdf->MultiCell(180, 0, $labels['p1_titolo_giudizio'], "TLR",'C', false, 1, "", "", true, 0, false, true, 20, "T");
    $pdf->SetFont('helvetica', 0, 10);
    $pdf->MultiCell(180, 0, $arr_voti_finale['giudizio_primo_quadrimestre'], "BRL",'L', false, 1, "", "", true, 0 );
    $y_rel = $y_rel + 40;
    $pdf->SetY($y_rel);
    $pdf->SetFont('helvetica', 0, 9);

    if ($periodo_pagella == 'intermedia') {
        $y_rel = $y_rel + 10;
        $pdf->SetY($y_rel);
        $pdf->SetFont('helvetica', 0, 10);

        // Data, luogo e firma dirigente
        $luogo_data = $studente['descrizione_comuni'] . ", " . $data_Day . "/" . $data_Month . "/" . $data_Year;
        $pdf->CellFitScale(5, 0, "", 0, 0, 'L');
        $pdf->CellFitScale(125, 0, $luogo_data, 0, 0, 'L');
        $pdf->MultiCell(50, 0, $labels['tipo_dirigente'], 0, 'C', false, 1, "", "", true, 0);

        $pdf->SetFont('helvetica', 0, 8);
        $pdf->CellFitScale(5, 0, "", 0, 0, 'L');
        $pdf->CellFitScale(125, 0, "", 0, 0, 'C');
        $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, 'C');
        if ($firma_digitale == 'SI') {
            $pdf->SetFont('helvetica', 0, 8);
            $pdf->CellFitScale(5, 0, "", 0, 0, 'L');
            $pdf->CellFitScale(125, 0, "", 0, 0, 'C');
            $pdf->CellFitScale(50, 0, $labels['firma_digitale'], 0, 1, 'C');
        }

//        $pdf->SetFont('helvetica', 0, 7);
//        $pdf->MultiCell(180, 0, $labels['firma_autografa_1'] . "\n" . $labels['firma_autografa_2'], 0, 'L', false, 1, "", "", true, 0);
    }

// -- PAGINA 2

    if ($periodo_pagella == 'finale') {
        $pdf->AddPage('P');

        $pdf->ln(2);

        // Se non è BES
//        if ($studente['bes'] != 'SI')
        if (true)
        {
            // --- VOTI SECONDO QUADRIMESTRE
            // Intestazione materie
            $pdf->SetFont('helvetica', 'B', 11);
            $pdf->CellFitScale(80, 0, $labels['p1_titolo_materia'], 0, 0, 'C');
            $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
            $pdf->CellFitScale(55, 0, $labels['p1_titolo_voto'], 0, 0, 'C');
            $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
            $pdf->CellFitScale(35, 0, $labels['p1_titolo_note'], 0, 1, 'C');
            $pdf->CellFitScale(80, 0, '', "B", 0, 'C');
            $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
            $pdf->CellFitScale(55, 0, $labels['p1_titolo_voto_2'], "B", 0, 'C');
            $pdf->CellFitScale(5, 0, "", 0, 0, 'C');
            $pdf->CellFitScale(35, 0, '', "B", 1, 'C');

            $pdf->ln(1);

            foreach ($arr_voti_finale['finale'] as $voto) {
                $voto_tradotto = "";
                foreach ($voto['significati_voto'] as $significato) {
                    if ($significato['voto'] == $voto['voto_pagellina']) {
                        $voto_tradotto = $significato['valore'];
                    }
                }
                $riga = $pdf->MultiCellNbLines(70, $voto['descrizione']) * 5;

                //$riga = $riga < 10 ? 10 : $riga;

                $pdf->SetFont('helvetica', 'I', 9);

                $voto['descrizione'] = str_replace('(MM).', '', $voto['descrizione']);

                $pdf->SetFont('helvetica', 'I', 9);
                $pdf->MultiCell(80, $riga, $voto['descrizione'], "B", 'L', false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(5, $riga, "", 0, 'L', false, 0);
                $pdf->MultiCell(55, $riga, $voto_tradotto, "B", 'C', false, 0, "", "", true, 0, false, true, $riga, "M");
                $pdf->MultiCell(5, $riga, "", 0, 'L', false, 0);

                $annotazione = "";
                foreach ($voto['campi_liberi'] as $campo_libero) {
                    if (strpos($campo_libero['nome'], $campo_materia_annotazioni) !== false) {
                        $annotazione .= estrai_valore_campo_libero_selezionato($campo_libero);
                    }
                }
                $pdf->MultiCell(35, $riga, $annotazione, "B", 'C', false, 1, "", "", true, 0, false, true, $riga, "M");

                $pdf->ln(1);
            }

            $pdf->ln(4);

            $pdf->SetFont('helvetica', 'B', 11);
            $pdf->MultiCell(180, 0, $labels['p1_titolo_opzionali'], "TLR",'C', false, 1, "", "", true, 0, false, true, 20, "T");
            $pdf->SetFont('helvetica', 0, 10);
            $pdf->MultiCell(180, 0, $arr_voti_finale['opzionale_finale']."\n".$arr_voti_finale['attivita_opzionali_finale']."\n" . $arr_voti_finale['attivita_alternativa_finale'], "BRL",'L', false, 1, "", "", true, 0,true );

            $y_rel = $pdf->GetY() + 5;

        }
        else
        {

            $x_base = 10;
            $y_rel = $pdf->GetY();
            $pdf->SetFillColor(255, 255, 255);
            $pdf->SetXY($x_base, $y_rel);
            $pdf->CellFitScale(180, 105, '', 1, 0, 'L', 1);

            $pdf->SetXY($x_base, $y_rel);
            $pdf->SetFont('helvetica', 'B', 11);
            $pdf->MultiCell(180, 0, $labels['p1_titolo_voto_2'], "TLR",'C', false, 1, "", "", true, 0, false, true, 20, "T");
            $pdf->SetFont('helvetica', 0, 10);
            $pdf->MultiCell(180, 0, $arr_bes['finale'], "BRL",'L', false, 1, "", "", true, 0 );

            $y_rel = $pdf->GetY() + 80;
        }

        $pdf->ln(5);

        // Giudizio Globale
        $x_base = 10;

        $pdf->SetFillColor(255, 255, 255);
        $pdf->SetXY($x_base, $y_rel);
        $pdf->CellFitScale(180, 55, '', 1, 0, 'L', 1);
        $pdf->SetXY($x_base, $y_rel);
        $pdf->SetFont('helvetica', 'B', 11);
        $pdf->MultiCell(180, 0, $labels['p1_titolo_giudizio'], "TLR",'C', false, 1, "", "", true, 0, false, true, 20, "T");
        $pdf->SetFont('helvetica', 0, 10);
//        $pdf->MultiCell(180, 0, $arr_voti_finale['giudizio_finale'], "BRL",'L', false, 1, "", "", true, 0 );
        $pdf->writeHTMLCell( 180, 0, '', '', $arr_voti_finale['giudizio_finale'], "BRL", 1);
        $y_rel = $y_rel + 45;
        $pdf->SetY($y_rel);
        $pdf->SetFont('helvetica', 0, 9);



        // Attestato
        $x_base = 10;
        $y_rel = $pdf->GetY();
        $pdf->SetFillColor(255, 255, 255);
        $pdf->SetFont('helvetica', '', 12);
        $pdf->SetXY($x_base, $y_rel + 12);
        $pdf->CellFitScale(180, 55, '', 1, 0, 'L', 1);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->SetXY($x_base, $y_rel + 12);
        $pdf->CellFitScale(180, 0, $labels['titolo_attestato'], 0, 1, 'C');
        $pdf->SetFont('helvetica', 0, 10);
        $pdf->ln(4);

        $pdf->CellFitScale(180, 0, $labels['testo_attestato_0'], 0, 1, 'L');

        $pdf->ln(4);

        $pdf->CellFitScale(180, 0, $labels['testo_attestato'], 0, 1, 'C');

        $no_ammissione = (strpos(strtolower($studente['esito']),"non") !== false) ? "non " : "";
        $testo_risultato_finale = "L{$min_oa} student{$min_eessa} " . $no_ammissione . "è stat{$min_oa} "  . str_replace('non', '', strtolower($studente['esito']));

        $pdf->CellFitScale(180, 0, $testo_risultato_finale, 0, 1, 'C');


        $y_rel = $y_rel + 48;
        $pdf->SetY($y_rel);
        $pdf->SetFont('helvetica', 0, 12);

        // Data, luogo e firma dirigente
        $luogo_data = $studente['descrizione_comuni'] . ", " . $data_Day . "/" . $data_Month . "/" . $data_Year;
        $pdf->CellFitScale(5, 0, "", 0, 0, 'L');
        $pdf->CellFitScale(125, 0, $luogo_data, 0, 0, 'L');
        $pdf->MultiCell(50, 0, $labels['tipo_dirigente'], 1, 'C', false, 1, "", "", true, 0);

        $pdf->SetFont('helvetica', 0, 8);
        $pdf->CellFitScale(5, 0, "", 0, 0, 'L');
        $pdf->CellFitScale(125, 0, "", 0, 0, 'C');
        $pdf->CellFitScale(50, 0, $studente['nome_dirigente'], 0, 1, 'C');
        if ($firma_digitale == 'SI') {
            $pdf->SetFont('helvetica', 0, 8);
            $pdf->CellFitScale(5, 0, "", 0, 0, 'L');
            $pdf->CellFitScale(125, 0, "", 0, 0, 'C');
            $pdf->CellFitScale(50, 0, $labels['firma_digitale'], 0, 1, 'C');
        }

//        $pdf->SetFont('helvetica', 0, 7);
//        $pdf->MultiCell(180, 0, $labels['firma_autografa_1'] . "\n" . $labels['firma_autografa_2'], 0, 'L', false, 1, "", "", true, 0);

        $x_corr = $pdf->GetX();
        $y_corr = $pdf->GetY();
        if (file_exists("/var/www-source/mastercom/immagini_scuola/timbro.jpeg")) {
            $img = "/var/www-source/mastercom/immagini_scuola/timbro.jpeg";
            $pdf->Image($img, 120, $y_corr - 10, 15, 15, 'jpeg', '', 'N', false, 300, '', false, false, 0, 'CM');
        }
    }
//}}} </editor-fold>
}


$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'       => $id_classe,
    'data_day'        => $data_Day,
    'data_month'      => $data_Month,
    'data_year'       => $data_Year,
    'periodo_pagella' => $periodo_pagella,
    'firma_digitale'  => $firma_digitale
];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF('P', 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . '_' . $anno_fine. ".pdf";

            $pdf = new NEXUS_PDF($orientamento, 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . '_' . $anno_fine. ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF('P', 'mm', 'A4');
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF('P', 'mm', 'A4');
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
