<?php
/*
INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'certificato_competenze_ss_4_salesianitreviglio_bg', 'Certificato delle competenze per le IV', 1, 'salesianitreviglio-bg', 12);
 */

$orientamento = 'P';
$formato = 'A4';


$elenco_studenti = estrai_studenti_classe($id_classe, true);
$dati_classe = estrai_classe($id_classe);
$anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
$anno = explode("/", $anno_scolastico);

$tipo_stampa = "certificazione_competenze_superiori_" . $dati_classe['classe'] . $dati_classe['sezione'];
$periodo_pagella = "finale";
$periodo = 9;

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
];


function genera_stampa($pdf, $studente, $parametri_stampa) {
    $id_classe = $parametri_stampa['id_classe'];
    $data_attestato = "{$parametri_stampa['data_day']}/{$parametri_stampa['data_month']}/{$parametri_stampa['data_year']}";
    $anno_scolastico = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $anno = explode("/", $anno_scolastico);

    $data_nascita_txt = date('d/m/Y', $studente['data_nascita']);
    $provincia_nascita = $luogo_nascita = '';
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    // mappo competenze superiori generali agli indicatori delle classi 4° che hanno creato sulla materia condotta per ricavare i valori
    $ordine = 'SS';
    $competenze = estrai_competenze_scolastiche_studente($studente['id_studente'], $ordine, $anno[0]);
    $voti_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);
    $materie_studente = estrai_materie_multi_classe_studente((int) $studente['id_studente']);
    $arr_indicatori_valori = [];
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
            foreach ($voti_finale[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($voti_finale[$id_materia], $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ($value !== '') {
                        $arr_indicatori_valori[$campo_libero['nome']] .= $value;
                    }
                    if (stripos($campo_libero['nome'], 'allievo/a ha inoltre mostrato significative competenze') !== false ) {
                        $arr_indicatori_valori['testo'] .= $value;
                    }
                }
            }
        }
    }
    /*
    $mappatura_nomi_indicatori_competenze = [
        "Comunicazione nella madre-lingua o lingua di istruzione" => "Comunicazione nella madrelingua o lingua di istruzione",
        "Spirito di iniziativa e imprenditorialit&agrave;" => "Spirito di iniziativa *",
        "Riconosce ed apprezza le diverse identit&agrave;, le tradizioni culturali e religiose, in un&rsquo;ottica di dialogo e di rispetto reciproco." => "Riconosce ed apprezza le diverse identit&agrave;, le tradizioni culturali e religiose, in un&#39;ottica di dialogo e di rispetto reciproco.",
        "In relazione alle proprie potenzialit&agrave; e al proprio talento si esprime e dimostra interesse per gli ambiti motori, artistici e musicali." => "In relazione alle proprie potenzialit&agrave; e al proprio talento si esprime negli ambiti che gli sono pi&ugrave; congeniali: motori, artistici e musicali.",
        "L’allievo/a ha inoltre mostrato significative competenze nello svolgimento di attività scolastiche e/o extrascolastiche, relativamente a" => "testo",
        "Competenza in materia di cittadinanza."    => "Competenza in materia di cittadinanza",
        "Competenza in materia di consapevolezza ed espressione culturali." => "Competenza in materia di consapevolezza ed espressione culturali"
Competenza alfabetica funzionale
        Competenza
multilinguistica
        Competenza matematica e
        competenza in scienze,
        tecnologie e ingegneria
        Competenza digitale
        Competenza personale,
sociale e capacità di
imparare a imparare
        Competenza in materia di
cittadinanza
        Competenza
imprenditoriale
        
        Competenza in materia di
        consapevolezza ed
        espressione culturali
    ];
    foreach ($arr_indicatori_valori as $nome_cc => $value) {
        $arr_indicatori_valori[$mappatura_nomi_indicatori_competenze[$nome_cc]] = $value;
    }
*/
    //###
    // Identifico e raggruppo le competenze che hanno la stessa chiave e le divido da quelle dove invece va mostrato solo il testo
    $competenze_livello = [];
    $competenze_testo = [];
    foreach ($competenze as $competenza) {
        if ($competenza['competenze_chiave'] == "") {
            $competenze_testo[] = $competenza;
        }
        else {
            $competenze_livello[$competenza['competenze_chiave']][$competenza['id_competenza_scolastica']] = $competenza;
        }
    }


    //{{{ <editor-fold defaultstate="collapsed" desc="dizionario e testi">
    $labels = [
        "nasce"     => "nat||min_oa|| a ",
        "c_alunno"              => "che l’alliev||min_oa|| <b>{$studente['cognome']} {$studente['nome']}</b> iscritt||min_oa|| alla classe <b>{$studente['classe']} {$studente['sezione']}</b>",
        "note"                  => '<sup>1</sup> Dalla Raccomandazione 2006/962/CE del 18 dicembre 2006 del Parlamento europeo e del Consiglio.<br>
<sup>2</sup> Dalle “Indicazioni Nazionali per il curricolo del Primo Ciclo di istruzione 2012". D.M. n. 254 del 16 novembre 2012.',
        "legenda"   => '<table>'
            . '<tr>'
                . '<td width="15%" style=style="border-bottom-width: 0.1px;"><b>(*) Livello</b></td>'
                . '<td width="85%" style=style="border-bottom-width: 0.1px;"><b>Indicatori esplicativi</b></td>'
            . '</tr>'
            . '<tr>'
                . '<td><b><i>A – Avanzato</i></b></td>'
                . '<td>L’alunno/a svolge compiti e risolve problemi complessi, mostrando padronanza nell’uso delle conoscenze e delle abilità; propone e sostiene le proprie opinioni e assume in modo responsabile decisioni consapevoli.</td>'
            . '</tr>'
            . '<tr>'
                . '<td><b><i>B - Intermedio</i></b></td>'
                . '<td>L’alunno/a svolge compiti e risolve problemi in situazioni nuove, compie scelte consapevoli, mostrando di saper utilizzare le conoscenze e le abilità acquisite.</td>'
            . '</tr>'
            . '<tr>'
                . '<td><b><i>C - Base</i></b></td>'
                . '<td>L’alunno/a svolge compiti semplici anche in situazioni nuove, mostrando di possedere conoscenze e abilità fondamentali e di saper applicare basilari regole e procedure apprese.</td>'
            . '</tr>'
            . '<tr>'
                . '<td><b><i>D - Iniziale</i></b></td>'
                . '<td>L’alunno/a, se opportunamente guidato/a, svolge compiti semplici in situazioni note.</td>'
            . '</tr>'
        . '</table>'
    ];

    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
    } else {
        $min_oa = "o";
        $min_eessa = "e";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Pagina 1">
    $fnt = 'times';
    $fd = 11;

    $pdf->AddPage('P');
    $pdf->SetAutoPageBreak("off", 1);
    $pdf->SetFont($fnt, 'B', $fd+2);
	if(file_exists('/var/www-source/mastercom/immagini_scuola/logo_ss.jpg')) {
		$pdf->Image('/var/www-source/mastercom/immagini_scuola/logo_ss.jpg', "", 10, '', 35, 'JPG', false, 'C', false, 300, 'L', false, false, 0, false, false, false);
	}
    $pdf->setY(120);
    $pdf->SetFont($fnt, 'B', $fd+9);
    $pdf->writeHTMLCell(0, 0, '', '', "CERTIFICAZIONE DELLE COMPETENZE<br>
A CONCLUSIONE DEL SECONDO BIENNIO", 0, 1, false, true, 'C');
    $pdf->ln(30);
    $pdf->SetFont($fnt, '', $fd+4);
    $pdf->writeHTMLCell(0, 0, '', '', "Si certifica che <b>{$studente['cognome']} {$studente['nome']}</b>", 0, 1, false, true, 'L');
    $pdf->ln(7);
    $pdf->CellFitScale(20, 0, $labels['nasce'], 0, 0, "L");
    $pdf->SetFont($fnt, 'B', $fd+4);
    $pdf->CellFitScale(60, 0, $luogo_nascita, 0, 0, "L");
    $pdf->SetFont($fnt, '', $fd+4);
    $pdf->writeHTMLCell(45, 0, '', '', "(prov. <b>$provincia_nascita</b>)", 0, 0, false, true, 'L');
    $pdf->writeHTMLCell(0, 0, '', '', "il   <b>{$data_nascita_txt}</b>", 0, 1, false, true, 'C');
    $pdf->ln(7);
    $pdf->writeHTMLCell(0, 0, '', '', "ha raggiunto, a conclusione del secondo biennio, i livelli di competenza di seguito illustrati.", 0, 1, false, true, 'L');

    $pdf->AddPage('P');
    $pdf->ln(2);
    $pdf->SetFont($fnt, 'B', 9);
    $pdf->MultiCell(45, 12, "COMPETENZA CHIAVE", "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
    $pdf->MultiCell(120, 12, "COMPETENZE IN ASSOLVIMENTO DELL’OBBLIGO DI ISTRUZIONE", "LTB", "C", false, 0, "", "", true, 0, false, true, 12, "M");
    $pdf->MultiCell(25, 12, "LIVELLO*", "LTRB", "C", false, 1, "", "", true, 0, false, true, 12, "M");
    $pdf->SetFont($fnt, '', 9);
    $x_base = $pdf->getX();
    /* @@@ */
    foreach ($competenze_livello as $chiave => $raggruppamento_chiave)
    {
        // scansione lingue
        foreach ($raggruppamento_chiave as $key => $competenza_singola)
        {
            $desc_tmp = $competenza_singola['descrizione'];
            if ($chiave == "Competenza multilinguistica") {
                if (stripos($desc_tmp, '###id_lingua_') !== false ) {
                    // $raggruppamento_chiave[$key]['valore'] = $arr_indicatori_valori['Competenza multilinguistica - Spagnolo'];
                } else {
                    // $raggruppamento_chiave[$key]['valore'] = $arr_indicatori_valori['Competenza multilinguistica - Inglese'];
                }
            }

            if (stripos($desc_tmp, '###id_lingua_') !== false ) {
                $lingua_varsrc = explode('###', $desc_tmp)[1];
                $lingua_src = estrai_dati_materia($studente[$lingua_varsrc]);
                if (count($lingua_src) > 0) {
                    $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", $lingua_src['descrizione'], $desc_tmp);
                } else {
                    $raggruppamento_chiave[$key]['descrizione'] = str_replace("###$lingua_varsrc", '______________________________', $desc_tmp);
                }
            }
        }

        $h_competenze = 0;
        foreach ($raggruppamento_chiave as $k => $competenza_singola)
        {
            if ($chiave != "Competenza multilinguistica") {
                // $competenza_singola['valore'] = $arr_indicatori_valori[$chiave];
                $raggruppamento_chiave[$k]['valore'] = $competenza_singola['valore'];
            }

            $raggruppamento_chiave[$k]['h_riga'] = max($pdf->getStringHeight(120, decode($competenza_singola['descrizione']),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                $pdf->getStringHeight(25, $competenza_singola['valore'],  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1 ),
                5);
            $h_competenze +=  $raggruppamento_chiave[$k]['h_riga'];
        }
        $h_chiave = max($h_competenze, $pdf->getStringHeight(45, decode($chiave),  $reseth = false,  $autopadding = true,  $cellpadding = '',  $border=1));

        if ($pdf->GetY()+$h_chiave>$pdf->GetPageHeight()) {
            $pdf->AddPage('P');
        }
        $pdf->SetFont($fnt, 'B', 9);
        $pdf->MultiCell(45, $h_chiave, decode($chiave), 1, "L", false, 0, "", "", true, 0, false, true, $h_chiave, "M");
        $pdf->SetFont($fnt, '', 9);
        $x_rel = $pdf->getX();
        foreach ($raggruppamento_chiave as $competenza_singola)
        {
            if ($competenza_singola['valore'] == '') { $competenza_singola['valore'] = ' ';}

            if (stripos($chiave, 'Competenza multilinguistica') !== false &&
                !isset($competenza_singola['lingua_valutazione'])) {
                if (trim($competenza_singola['valore']) != '') {
                    $pdf->MultiCell(120, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                    $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
                } else {    
                    // ppre($competenza_singola);
                    $pdf->MultiCell(145, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 1, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                }
            } else {
                $pdf->MultiCell(120, $competenza_singola['h_riga'], decode($competenza_singola['descrizione']), 1, "L", false, 0, "", "", true, 1, false, true, $competenza_singola['h_riga'], "M");
                $pdf->MultiCell(25, $competenza_singola['h_riga'], $competenza_singola['valore'], 1, "C", false, 1, "", "", true, 0, false, true, $competenza_singola['h_riga'], "M");
            }
            $pdf->setX($x_rel);
        }

        $pdf->setX($x_base);
    }
    foreach ($competenze_testo as $competenza_singola)
    {
        $riga_controllo_altezza = decode(str_replace("alunno/a", "alunn$min_oa",$competenza_singola['descrizione'])) . "\n " . decode($competenza_singola['testo']);
        $riga = $pdf->MultiCellNbLines(180, $riga_controllo_altezza) * 4.5;
        $pdf->MultiCell(190, $riga, $riga_controllo_altezza, "LBR", "L", false, 1, "", "", true, 0, false, true, $riga, "M");
    }    
    $pdf->ln(3);
    $pdf->SetFont($fnt, '', $fd);
    $pdf->writeHTMLCell(40, 0, '', '', "Treviglio, $data_attestato", 0, 0, false, true, 'L');
    $pdf->writeHTMLCell(60, 0, '', '', "", 0, 0, false, true, 'C');
    $pdf->writeHTMLCell(0, 0, '', '', "Il coordinatore delle attività educative e didattiche (1)<br>
<b>{$studente['nome_dirigente']}</b>", 0, 1, false, true, 'C');
    $pdf->SetFont($fnt, '', $fd-1);
    $pdf->writeHTMLCell(0, 0, '', 240, $labels['legenda'], 0, 1, false, true, 'L');
    $pdf->SetFont($fnt, 'I', $fd-2);
    $pdf->writeHTMLCell(0, 0, '', '', '<sup>1</sup> Per le istituzioni scolastiche paritarie, la certificazione è rilasciata dal Coordinatore delle attività educative e didattiche. Nel caso di percorsi di IeFP
    realizzati da Strutture formative accreditate dalle Regioni occorre sostituire ‘Il Dirigente scolastico” con ‘Il Direttore/Legale Rappresentante della
    Struttura formativa accreditata”.', 'T', 1, false, true, 'L');
    //}}} </editor-fold>
}

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Certificato competenze superiori ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

           if (file_exists($pagella)) {
               $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
               unlink($pagella);
           }
        }

       if (empty($pagelle_generate)) {
           echo "Errore generazione pagelle (contattare l'Assistenza)";
       } else {
           echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
       }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Certificato competenze superiori ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new MASTERCOM_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}
