
;-------------------------------------------------------------------------------
; Package configuration
;-------------------------------------------------------------------------------

[application]
;
; Application name, will be master-<name>
;
;name = myApp
;
name = gestionale
;
; Application version
;
;version = 1.0.0
;
; NOTA BENE: cambiare anche LOGGER_APP_VERSION nel file configs/mastercom.php
version = 25.3.02

;
; Debian package configuration for Control file
;
;replaces =
;breaks =
;conflicts =
;section = main
;priority = optional
;architecture = all
;depends[] = ""
;maintainer = "<PERSON> <a.bass<PERSON>@mastertraining.it>"
;description = ""
;
section = base
priority = optional
architecture = all
;replaces = "gestionale-mastercom (<=22.2.0)"
;conflicts = "gestionale-mastercom (<=22.2.0)"
depends[] = "php5 (>=5.5.35) | php (>=1:7.0)"
depends[] = "php5-cli (>=5.5.35) | php-cli (>=1:7.0)"
depends[] = "php5-curl (>=5.5.35) | php-curl (>=1:7.0)"
depends[] = "php5-gd (>=5.5.35) | php-gd (>=1:7.0)"
depends[] = "php5-pgsql (>=5.5.35) | php-pgsql (>=1:7.0)"
depends[] = "php-soap"
depends[] = "php5 | php-mbstring (>=1:7.0)"
; TODO: verificare se ancora necessaria dipendenza
depends[] = "php5-imagick (>=3.1.2) | php-imagick"
depends[] = "apache2 (>=2.4.7)"
depends[] = "libapache2-mod-php5 (>=5.5.35) | libapache2-mod-php (>=1:7.0)"
depends[] = "********ql (>=9.1)"
depends[] = "********ql-contrib (>=9.1)"
depends[] = "curl (>=7.22.0)"
depends[] = "cups (>=1.5.3)"
depends[] = "zip (>=3.0-4)"
depends[] = "unzip (>=6.0-4)"
depends[] = "sane-utils (>=1.0.22)"
depends[] = "wget (>=1.13.4)"
; Generazione icone dinamiche
depends[] = "libmagickcore4-extra | libmagickcore-6.q16-2-extra"
depends[] = "master-apache (>=3.3.4)"
depends[] = "master-api (>=3.4.4)"
depends[] = "master-mt (>=1.2.51)"
depends[] = "master-messenger (>=1.6.22)"
depends[] = "master-messenger-ui (>=1.1.27)"
depends[] = "master-logger (>=1.1.33)"
depends[] = "master-manuali (>=1.0.1)"
depends[] = "master-flussi (>=1.1.0)"
maintainer = "Francesco Zamboni <<EMAIL>>"
description = "Gestionale Mastercom"

;
; Source path of the application in development environment
;
;src_path = /home/<USER>
;src_path = "$HOME/NetBeansProjects/mastercom"
;src_path = /var/lib/mercurial-server/repos/<app name>
src_path = /var/lib/mercurial-server/repos/mastercom

;
; Destination path of the application
;
;dest_path = /var/www/myApp
;
dest_path = /var/www-source/mastercom

;
; Configuration files (Debian conffiles)
;
; If your application uses configuration files but also rewrites them on its
; own, it's best not to make them conffiles because dpkg will then prompt users
; to verify the changes all the time.
;
; conffiles[] = /var/www/myApp/configuration.ext
; conffiles[] = /var/www/myApp/configuration_2.ext
;
conffiles[] = /etc/mastercom/mastercom.conf
conffiles[] = /etc/mastercom/database_main.conf

;
; If specified the package manager will backups all files/directories in
; /var/backups/mastertraining/<APPLICATION NAME> before install or upgrade the
; package.
; NOTE: you must set the absolute path of the files/directories. The directory
;       tree will be replicated into the backup directory.
;       Symbolic links are not supported yet.
;
;backup[] = /etc/myconf
;
backup[] = /var/www-source/mastercom/foto_studenti
backup[] = /var/www-source/mastercom/immagini_scuola

;
; Lock script executed before updating application.
; The script must to be into RELEASE path, and the extension must be .sh or .php
;
; IMPORTANT: also unlock_script must to be specified
;
;lock_script = lock.sh
;
lock_script = lock.sh

;
; Unlock script executed after updating application
; The script must to be into RELEASE path, and the extension must be .sh or .php
;
; IMPORTANT: also lock_script must to be specified
;
;unlock_script = unlock.sh
;
unlock_script = unlock.sh

;
; Define the list of directories/files to ignore for inclusion.
; NOTE: You must define the absolute path
;
;exclude[] = /var/www/myApp/PathToIgnore
;exclude[] = /var/www/myApp/FileToIgnore.ext
;
exclude[] = /var/lib/mercurial-server/repos/mastercom/DOCS
exclude[] = /var/lib/mercurial-server/repos/mastercom/mastercom.ant

;
; Check PHP syntax errors
;
;check_php_errors = on
;
check_php_errors = on

;-------------------------------------------------------------------------------
; Database parameters
;-------------------------------------------------------------------------------
[database]
;
; Specify the configuration file (using the absolute path) where database
; connection parameters are defined for the application.
;
; IMPORTANT:
; - See also label_<ALL> parameters
; - The content format must be like:
;
;   define('MY_DB_HOST_CONSTANT', 'myhost');
;   define('MY_DB_PORT_CONSTANT', 5432);
;   ...
;
;configuration = /var/www/myApp/configs/database.php
;
configuration = /etc/mastercom/mastercom.conf

;
; If off force to skip database creation. To use for eg. with a new schema in an
; existing database
;
create_database = on

;
; The labels regexp where package builder must find relative configuration
; parameters.
;
;label_host = MY_DB_HOST_LABEL
;label_port = MY_DB_PORT_LABEL
;label_name = MY_DB_NAME_LABEL
;label_user = MY_DB_USER_LABEL
;label_password = MY_DB_PASSWORD_LABEL
;
;label_host =
label_port = 5432
;label_name =
;label_user =
;label_password =

;
; Default database configuration
;
; NOTE: Port number will be updated if update_port is "on"
;
;name = myAppDatabaseName
;schema = public
;port = 5432
;host = localhost
;user = ********
;password = ********
;
name = MASTERCOM
schema = public
port = 5432
host = localhost
user = ********
password = ********

;
; If "on" the package manager will check the database port if differs from
; current running port, if so the configuration file will be updated.
;
;update_port = on
;
update_port = on

;
; Make a database backup before updating application.
; The backup will be saved as gzip file into:
; /var/lib/********ql/mastertraining/<APPLICATION NAME>/backup.sql.gz
;
;backup = on
;
backup = on

;
; Database optimization after install
;
;reindex = on
;vacuum = on
;analyze = on
;
reindex = off
vacuum = off
analyze = on

;
; Table parameters
;
;parameters_table = parameters
;parameter_name_column = name
;parameter_value_column = value
;version_label = VERSION
;
parameters_table = parametri
parameter_name_column = nome
parameter_value_column = valore
version_label = VERSIONE

;
; Restart / Reload services after install
;
[services]

apache_reload = on
apache_restart = off
********ql_reload = off
********ql_restart = off

;
; Code Optimizer
;
[optimizer]

;
; Minify/Precompile application code for the final package.
; Files in <APPLICATION NAME>/configs/ path will be ignored
;
;php = on
;js = on
;css = on
;html = on
php = on
js = off
css = on
html = off

;
; Define the list of the files to ignore for optimization.
; NOTE: Exclude directories are not implemented yet.
;
;exclude[] = /var/www/myApp/fileToIgnore.php
;
exclude[] = /var/lib/mercurial-server/repos/mastercom/javascript/jquery-1.2.1.pack.js

;-------------------------------------------------------------------------------
; On each update executes specified files into the defined order
;-------------------------------------------------------------------------------
[execute]
;
;file[] = update.sql
;
file[] = update.sql