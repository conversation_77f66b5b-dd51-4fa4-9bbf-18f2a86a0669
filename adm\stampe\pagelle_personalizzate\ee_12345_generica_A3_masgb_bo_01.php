<?php

/*
 * Tipo: Pagella generica elementari
 * Nome: ee_12345_generica_A3_masgb_bo_01
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'ee_12345_generica_A3_masgb_bo_01', 'Pagella Scuola Primaria', 1, 'masgb_bo', 1);

UPDATE parametri set valore = 'SI' where nome ='ABILITA_STAMPE_PERSONALIZZATE';
 */
$orientamento = 'L';
$formato = 'A4';

if ($periodo_pagella=='finale') {
    $periodo = 9;
} elseif ($periodo_pagella=='intermedia')  {
    $periodo = 7;
}

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $id_classe = $parametri_stampa['id_classe'];
    $periodo = $parametri_stampa['periodo'];
    $current_key = $parametri_stampa['current_key'];
    $periodo_pagella = $parametri_stampa['periodo_pagella'];
    $data_attestato = "{$parametri_stampa['data_attestato_day']}/{$parametri_stampa['data_attestato_month']}/{$parametri_stampa['data_attestato_year']}";
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
            $desc_nascita = $luogo_nascita;
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
        $desc_nascita = "$luogo_nascita ($provincia_nascita)";
    }
    $id_studente = $studente['id_studente'];

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagella_p1 = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $studente['id_studente']);
    $voti_pagella_p2 = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);

    if ($periodo < 9) {
        $voti_pagella_p2 = [];
    }


    $arr_voti = $religione = $condotta = [];
    $giudizio = '';
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        if ($materia['in_media_pagelle'] != 'NV'
            && (
                    !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO'])
                    ||
                    (
                    $studente['esonero_religione'] == 0 && $materia['tipo_materia'] == 'RELIGIONE'
                    ||
                    $studente['esonero_religione'] == 1 && $materia['tipo_materia'] == 'ALTERNATIVA'
                     )
                )
            )
        {
            $arr_voti[$id_materia]['p1'] = $voti_pagella_p1[$id_materia];
            $arr_voti[$id_materia]['p2'] = $voti_pagella_p2[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $religione[$id_materia]['p1'] = $voti_pagella_p1[$id_materia];
            $religione[$id_materia]['p2'] = $voti_pagella_p2[$id_materia];
        }


        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
            $condotta[$id_materia]['p1'] = $voti_pagella_p1[$id_materia];
            $condotta[$id_materia]['p2'] = $voti_pagella_p2[$id_materia];

            foreach ($voti_pagella_p1[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ( stripos($campo_libero['nome'], 'GIUDIZIO GLOBALE') !== false) {
                        if ($value !== '') {
                            $giudizio['p1'] .= ($value);
                        }
                    }
                }
            }

            foreach ($voti_pagella_p2[$id_materia]['campi_liberi'] as $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if ( stripos($campo_libero['nome'], 'GIUDIZIO GLOBALE') !== false) {
                        if ($value !== '') {
                            $giudizio['p2'] .= ($value);
                        }
                    }
                }
            }
        }
    }
    $arr_voti = $arr_voti + $religione + $condotta;
//    $giudizio['p1'] = str_replace(["\n", "\r"], "<br>", $giudizio['p1']);


    $parametri = [
        "anno_scolastico" =>    $anno_scolastico_attuale,
        "estrai_valutazioni_figli"  =>  'SI'
    ];
    $periodo_api = 7;
    $competenze_scrutinio_stud_p1 = nextapi_call('competenze_studente/dati_scrutinio/' . $periodo_api . '/' . $id_studente, 'GET', $parametri, $current_key);
//    echo '@@@', $studente['cognome'];
//    echo_debug($competenze_scrutinio_stud_p1);
//    echo '@@@';
    foreach ($competenze_scrutinio_stud_p1['elenco'] as $key_comp => $competenza)
    {
        if ( !array_key_exists($periodo_api, $competenza['periodi_scrutini_abilitati']) ) {
            unset($competenze_scrutinio_stud_p1['elenco'][$key_comp]);
        }
        foreach ($competenza['schema_valutazioni'] as $key_schema => $val) {
            if ($val['tipo'] != 'scrutini') {
                unset($competenza['schema_valutazioni'][$key_schema]);
            }
        }
        foreach ($competenza['vincoli']['materie'] as $key_materia => $dati_materia) {
            if (isset($arr_voti[$key_materia])) {
                $arr_voti[$key_materia]['p1']['competenze'][$key_comp] = $competenza;
            }
        }
    }
    if ($periodo == 9) {
        $periodo_api = 9;
        $competenze_scrutinio_stud_p2 = nextapi_call('competenze_studente/dati_scrutinio/' . $periodo_api . '/' . $id_studente, 'GET', $parametri, $current_key);
    //    echo '@@@', $studente['cognome'];
    //    echo_debug($competenze_scrutinio_stud_p2);
    //    echo '@@@';
        foreach ($competenze_scrutinio_stud_p2['elenco'] as $key_comp => $competenza)
        {
            if ( !array_key_exists($periodo_api, $competenza['periodi_scrutini_abilitati']) ) {
                unset($competenze_scrutinio_stud_p2['elenco'][$key_comp]);
            }
            foreach ($competenza['schema_valutazioni'] as $key_schema => $val) {
                if ($val['tipo'] != 'scrutini') {
                    unset($competenza['schema_valutazioni'][$key_schema]);
                }
            }
            foreach ($competenza['vincoli']['materie'] as $key_materia => $dati_materia) {
                if (isset($arr_voti[$key_materia])) {
                    $arr_voti[$key_materia]['p2']['competenze'][$key_comp] = $competenza;
                }
            }
        }
    } else {
        $arr_voti[$key_materia]['p2']['competenze'] = null;
    }



    // Dizionario temporaneo
    $labels = [
        "classe"                    => "iscritt||min_oa|| alla classe:",
        "alunno"                    => "dell’alunn||min_oa||: ",
        "nascita"                   => "nat||min_oa|| a: ",
        "legenda"                   =>
        '<table border="0.1px" cellpadding="5" align="center">'
        . '<tr><td width="20%"><b>GIUDIZIO SINTETICO</b></td><td width="80%"><b>DESCRIZIONE</b></td></tr>'
        . '<tr><td>OTTIMO</td><td align="left">L’alunno svolge e porta a termine le attività con autonomia e consapevolezza, riuscendo ad affrontare anche situazioni complesse e non proposte in precedenza.<br>
È in grado di utilizzare conoscenze, abilità e competenze per svolgere con continuità compiti e risolvere problemi, anche difficili, in modo originale e personale.<br>
Si esprime correttamente, con particolare proprietà di linguaggio, capacità critica e di argomentazione, in modalità adeguate al contesto.</td></tr>'
        . '<tr><td>DISTINTO</td><td align="left">L’alunno svolge e porta a termine le attività con autonomia e consapevolezza, riuscendo ad affrontare anche situazioni complesse.<br>
È in grado di utilizzare conoscenze, abilità e competenze per svolgere con continuità compiti e risolvere problemi anche difficili.<br>
Si esprime correttamente, con proprietà di linguaggio e capacità di argomentazione, in modalità adeguate al contesto.</td></tr>'
        . '<tr><td>BUONO</td><td align="left">L’alunno svolge e porta a termine le attività con autonomia e consapevolezza.<br>
È in grado di utilizzare conoscenze, abilità e competenze per svolgere con continuità compiti e risolvere problemi.<br>
Si esprime correttamente, collegando le principali informazioni e usando un linguaggio adeguato al contesto.</td></tr>'
        . '<tr><td>DISCRETO</td><td align="left">L’alunno svolge e porta a termine le attività con parziale autonomia e consapevolezza.<br>
È in grado di utilizzare alcune conoscenze, abilità e competenze per svolgere compiti e risolvere problemi non particolarmente complessi.<br>
Si esprime correttamente, con un lessico semplice e adeguato al contesto.</td></tr>'
        . '<tr><td>SUFFICIENTE</td><td align="left">L’alunno svolge le attività principalmente sotto la guida e con il supporto del docente.<br>
È in grado di applicare alcune conoscenze e abilità per svolgere semplici compiti e problemi, solo se già affrontati in precedenza.<br>
Si esprime con un lessico limitato e con qualche incertezza.</td></tr>'
        . '<tr><td>NON SUFFICIENTE</td><td align="left">L’alunno non riesce abitualmente a svolgere le attività proposte, anche se guidato dal docente.<br>
Applica solo saltuariamente conoscenze e abilità per svolgere alcuni semplici compiti.<br>
Si esprime con incertezza e in maniera non adeguata al contesto.</td></tr>'
      . '</table>',
        "note"                      => "(1)    La firma è omessa ai sensi dell’art. 3, D.to Lgs. 12.02.1993, n. 39.<br>
(2)    Giudizio formulato secondo le modalità deliberate dal Collegio dei docenti, ai sensi dell’Art. 2, comma 8, del D.P.R. n. 122/2009.<br><br>
Il presente certificato non può essere prodotto agli organi della pubblica amministrazione o ai privati gestori di pubblici servizi (art. 15, comma 1, L. 183/11)",
        "esito" => "Visti gli atti d’ufficio e la valutazione dei docenti della classe, si attesta che l’alunn||min_oa|| {$studente['cognome']} {$studente['nome']}, nat||min_oa|| a $desc_nascita il {$studente['data_nascita_ext']}, è stat||min_oa|| ",
    ];
    $mat_risultato = calcola_esito_finale_studente($id_studente, $current_user, false);

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $mat_risultato['esito'] = str_replace('Ammesso', 'Ammessa', $mat_risultato['esito']);
        $mat_risultato['esito'] = str_replace('ammesso', 'ammessa', $mat_risultato['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    $tbl_firme = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td width="70%" align="left">'.$studente['descrizione_comuni'] . ', ' . $data_attestato.'</td>
                <td width="30%" align="center">
                    La Direttrice Didattica<br>
                    '.$studente['nome_dirigente'].'<sup>(1)</sup>
                </td>
            </tr>
        </table>
        ';
    switch ($studente['classe']) {
        case 1:
            $cls_trad = 'I';
            break;
        case 2:
            $cls_trad = 'II';
            break;
        case 3:
            $cls_trad = 'III';
            break;
        case 4:
            $cls_trad = 'IV';
            break;
        case 5:
            $cls_trad = 'V';
            break;
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf">
    $f = 'helvetica';
    $fd = 10;

    $pdf->AddPage();
    $wp = $pdf->GetPageWidth() - 20;
    $pdf->SetAutoPageBreak('off', 1);
    inserisci_intestazione_pdf($pdf, $id_classe, $target_height=50);
    // $pdf->Image('immagini_scuola/logo_ee.jpg', 0, 0, 180, 50, 'JPG','', 'N', false, 300, 'C', false, false, 0, 'CM');

    $pdf->ln(10);
    $pdf->SetFont($f, 'B', $fd+7);
    $pdf->writeHTMLCell(0, 0, '', '', "DOCUMENTO DI VALUTAZIONE<br>di {$studente['cognome']} {$studente['nome']}<br>CLASSE $cls_trad<br>Anno Scolastico $anno_scolastico_attuale", 0, 1, false, true, 'C');
    $pdf->ln(10);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', $labels['legenda'], 0, 1, false, true, 'C');
    $pdf->ln(15);
//if ($studente['id_studente']==3174) {file_put_contents('/tmp/debug_voti_3174.txt', print_r($voti_pagella_p2,true));}
    if ($periodo == 9) { // finale
        foreach ($arr_voti as $id_materia => $voto)
        {
            $voti_p1 = $voto['p1'];
            $voti_p2 = $voto['p2'];

            $mat_st = [];
            $alt_mat = 0;
            $desc_materia = decode($voti_p1['descrizione']);

            foreach ($voti_p1['campi_liberi'] as $id_cmp => $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if (stripos($campo_libero['nome'], 'GIUDIZIO GLOBALE') === false && $value !== '' && $campo_libero['visibile']=='SI') {
                        $mat_st[$id_cmp]['descrizione'] = str_replace(["\r"], "", $campo_libero['descrizione']);
                        $mat_st[$id_cmp]['descrizione'] = str_replace(["\r","\n"], "\n", $mat_st[$id_cmp]['descrizione']);
                        $mat_st[$id_cmp]['giud_p1'] = $value;
                    }
                }
            }
            foreach ($voti_p2['campi_liberi'] as $id_cmp => $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if (stripos($campo_libero['nome'], 'GIUDIZIO GLOBALE') === false && $value !== '' && $campo_libero['visibile']=='SI') {
                        $mat_st[$id_cmp]['descrizione'] = str_replace(["\r"], "", $campo_libero['descrizione']);
                        $mat_st[$id_cmp]['descrizione'] = str_replace(["\r","\n"], "\n", $mat_st[$id_cmp]['descrizione']);
                        $mat_st[$id_cmp]['giud_p2'] = $value;
                    }
                }
            }

// file_put_contents("/tmp/debug_mat_st.txt", print_r($mat_st, true), FILE_APPEND);
            //echo_debug($mat_st);
            $pdf->SetFont($f, '', $fd-2);
            foreach ($mat_st as $id => $mt_data) {
                $mat_st[$id]['alt'] = max(
                    $pdf->getStringHeight( $wp*0.70, $mt_data['descrizione'], false, true, '', 1 ),
                    $pdf->getStringHeight( $wp*0.15, $mt_data['giud_p1'], false, true, '', 1 )
                );
                $alt_mat += $mat_st[$id]['alt'];
            }

            if ($pdf->GetY() + $alt_mat + 15 > $pdf->GetPageHeight()-3) {
                $pdf->AddPage();
            }

            $pdf->SetFont($f, 'B', $fd+1);
            $pdf->SetTextColor(25, 75, 255);
            $pdf->MultiCell( 0, 6, $desc_materia, 1, 'C', 0, 1, '', '', true, 1, false, true, 6, 'M', true);
            $pdf->SetTextColor(0, 0, 0);
            $pdf->SetFont($f, 'B', $fd-2);
            $pdf->MultiCell($wp*0.70, 9, "OBIETTIVI OGGETTO DI VALUTAZIONE DEL PERIODO DIDATTICO", 1, 'C', 0, 0, '', '', true, 1, false, true, 9, 'M', true);
            $pdf->MultiCell($wp*0.15, 9, "GIUDIZIO I°Q", 1, 'C', 0, 0, '', '', true, 1, false, true, 9, 'M', true);
            $pdf->MultiCell($wp*0.15, 9, "GIUDIZIO II°Q", 1, 'C', 0, 1, '', '', true, 1, false, true, 9, 'M', true);
            $pdf->SetFont($f, '', $fd-2);
            foreach ($mat_st as $st) {
                $dsc_obiettivo = $liv_p1 = $liv_p2 = $giud_p1 = $giud_p2 ='';

                $dsc_obiettivo = str_replace(["\r","\n"], "<br>", $st['descrizione']);
                $giud_p1 = $st['giud_p1']!=''? $st['giud_p1']:'';
                $giud_p2 = $st['giud_p2']!=''? $st['giud_p2']:'';
                $h_row= $st['alt'];

                $pdf->MultiCell($wp*0.70, $h_row, $dsc_obiettivo, 1, 'L', 0, 0, '', '', true, 1, true, true, $h_row, 'M', true);
                $pdf->MultiCell($wp*0.15, $h_row, $giud_p1, 1, 'C', 0, 0, '', '', true, 1, false, true, $h_row, 'M', true);
                $pdf->MultiCell($wp*0.15, $h_row, $giud_p2, 1, 'C', 0, 1, '', '', true, 1, false, true, $h_row, 'M', true);
            }
            // $pdf->ln(3);
        }

        $pdf->ln(6);
        $pdf->SetFont($f, '', $fd);
        $alt_giud = max(
            $pdf->getStringHeight( $wp*0.50, $giudizio['p1'], false, true, '', 1 ),
            $pdf->getStringHeight( $wp*0.50, $giudizio['p2'], false, true, '', 1 )
        )+8;
        if ($pdf->GetY() + $alt_giud + 10 > $pdf->GetPageHeight()) {
            $pdf->AddPage();
        }
        $pdf->SetFont($f, 'B', $fd);
        $pdf->SetTextColor(25, 75, 255);
        $pdf->MultiCell($wp, 6, "GIUDIZIO GLOBALE", 1, 'C', 0, 1, '', '', true, 1, false, true, 6, 'M', true);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->MultiCell($wp*0.50, 6, "I QUADRIMESTRE", 1, 'C', 0, 0, '', '', true, 1, false, true, 6, 'M', true);
        $pdf->MultiCell($wp*0.50, 6, "II QUADRIMESTRE", 1, 'C', 0, 1, '', '', true, 1, false, true, 6, 'M', true);
        $pdf->SetFont($f, '', $fd);
        $pdf->MultiCell($wp*0.50, $alt_giud, $giudizio['p1'], 1, 'L', 0, 0, '', '', true, 1, false, true, $alt_giud, 'M', true);
        $pdf->MultiCell($wp*0.50, $alt_giud, $giudizio['p2'], 1, 'L', 0, 1, '', '', true, 1, false, true, $alt_giud, 'M', true);
        $pdf->ln(10);
        if ($pdf->GetY() + 20 > $pdf->GetPageHeight()) {
            $pdf->AddPage();
        }
        // if ($periodo == 9) {
            $pdf->SetFont($f, 'B', $fd);
            $pdf->writeHTMLCell(0, 0, '', '', "ATTESTAZIONE", 0, 1, false, true, 'C');
            $pdf->ln(2);
            $pdf->writeHTMLCell(0, 0, '', '', $labels['esito'].$mat_risultato['esito'], 0, 1, false, true, 'C');
            $pdf->ln(6);
        // }
    } else { // quadrimestre
        // echo_debug($arr_voti);
        foreach ($arr_voti as $id_materia => $voto)
        {
            $voti_p1 = $voto['p1'];
// file_put_contents("/tmp/debug_voti_p1.txt", print_r($voti_p1, true), FILE_APPEND);
            $mat_st = [];
            $alt_mat = 0;
            $desc_materia = decode($voti_p1['descrizione']);

            foreach ($voti_p1['campi_liberi'] as $id_cmp => $campo_libero) {
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);
                    if (stripos($campo_libero['nome'], 'GIUDIZIO GLOBALE') === false && $value !== '' && $campo_libero['visibile']=='SI') {
                        $mat_st[$id_cmp]['descrizione'] = str_replace(["\r"], "", $campo_libero['descrizione']);
                        $mat_st[$id_cmp]['descrizione'] = str_replace(["\r","\n"], "\n", $mat_st[$id_cmp]['descrizione']);
                        $mat_st[$id_cmp]['giud_p1'] = $value;
                    }
                }
            }

// file_put_contents("/tmp/debug_mat_st.txt", print_r($mat_st, true), FILE_APPEND);
            //echo_debug($mat_st);
            $pdf->SetFont($f, '', $fd-2);
            foreach ($mat_st as $id => $mt_data) {
                $mat_st[$id]['alt'] = max(
                    $pdf->getStringHeight( $wp*0.80, $mt_data['descrizione'], false, true, '', 1 ),
                    $pdf->getStringHeight( $wp*0.20, $mt_data['giud_p1'], false, true, '', 1 )
                );
                $alt_mat += $mat_st[$id]['alt'];
            }

            if ($pdf->GetY() + $alt_mat + 15 > $pdf->GetPageHeight()-3) {
                $pdf->AddPage();
            }

            $pdf->SetFont($f, 'B', $fd+1);
            $pdf->SetTextColor(25, 75, 255);
            $pdf->MultiCell( 0, 6, $desc_materia, 1, 'C', 0, 1, '', '', true, 1, false, true, 6, 'M', true);
            $pdf->SetTextColor(0, 0, 0);
            $pdf->SetFont($f, 'B', $fd-2);
            $pdf->MultiCell($wp*0.80, 9, "OBIETTIVI OGGETTO DI VALUTAZIONE DEL PERIODO DIDATTICO", 1, 'C', 0, 0, '', '', true, 1, false, true, 9, 'M', true);
            $pdf->MultiCell($wp*0.20, 9, "GIUDIZIO", 1, 'C', 0, 1, '', '', true, 1, false, true, 9, 'M', true);
            $pdf->SetFont($f, '', $fd-2);
            foreach ($mat_st as $st) {
                $dsc_obiettivo = $liv_p1 = $liv_p2 = $giud_p1 = $giud_p2 ='';

                $dsc_obiettivo = str_replace(["\r","\n"], "<br>", $st['descrizione']);
                $giud_p1 = $st['giud_p1']!=''? $st['giud_p1']:'';
                $h_row= $st['alt'];

                $pdf->MultiCell($wp*0.80, $h_row, $dsc_obiettivo, 1, 'L', 0, 0, '', '', true, 1, true, true, $h_row, 'M', true);
                $pdf->MultiCell($wp*0.20, $h_row, $giud_p1, 1, 'C', 0, 1, '', '', true, 1, false, true, $h_row, 'M', true);
            }
            // $pdf->ln(3);
        }
        $pdf->ln(6);
        if ($pdf->GetY() + $alt_giud + 10 > $pdf->GetPageHeight()) {
            $pdf->AddPage();
        }
        $pdf->SetFont($f, 'B', $fd);
        $pdf->SetTextColor(25, 75, 255);
        $pdf->MultiCell($wp, 6, "GIUDIZIO GLOBALE", 1, 'C', 0, 1, '', '', true, 1, false, true, 6, 'M', true);
        $pdf->SetTextColor(0, 0, 0);
        $pdf->MultiCell($wp*1, 6, "I QUADRIMESTRE", 1, 'C', 0, 1, '', '', true, 1, false, true, 6, 'M', true);
        $pdf->SetFont($f, '', $fd);
        $pdf->MultiCell($wp*1, $alt_giud, $giudizio['p1'], 1, 'L', 0, 1, '', '', true, 1, false, true, $alt_giud, 'M', true);
        $pdf->ln(8);
    }
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', $tbl_firme, 0, 1, false, true, 'C');
    $pdf->ln(3);
    $pdf->SetFont($f, '', $fd-2);
    $pdf->writeHTMLCell(0, 0, '', '', $labels['note'], 0, 1,false,true, 'L'); # y 185
    //}}} </editor-fold>
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
//    'data_IQ_day'          => $data_IQ_Day,
//    'data_IQ_month'        => $data_IQ_Month,
//    'data_IQ_year'         => $data_IQ_Year,
    'data_attestato_day'            => $data_Day,
    'data_attestato_month'          => $data_Month,
    'data_attestato_year'           => $data_Year,
    'periodo'   => $periodo,
    'periodo_pagella'   => $periodo_pagella,
    'orientamento'      => $orientamento,
    'current_key'   => $current_key

];


switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
