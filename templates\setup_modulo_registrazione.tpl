<!DOCTYPE html>
<html lang="it">
    <head>
        <title>{$titolo} - {$nome}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="google" value="notranslate">
        <link rel="stylesheet" href="css/style.css">
        <link rel="shortcut icon" type="image/x-icon" href="icone/mastercom.ico">
        <link rel="stylesheet" type="text/css" href="css/jquery-ui-1.8.16.custom.css" />
        <link rel="stylesheet" href="css/style.css">
        {* <script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script> *}
        <script type="text/javascript" src="libs/jquery/jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
        <script type="text/javascript" src="javascript/messenger.js"></script>
        <script type="text/javascript" src="/mastertek-api/ckeditor-mastercom/ckeditor.js"></script>
        <script type="text/javascript" src="javascript/anylink.js"></script>
        <script type="text/javascript" src="javascript/professore/include_funzioni_registro.js"></script>

		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.base.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.darkblue.css" type="text/css" />
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">

		<script type="text/javascript" src="libs/jqwidgets/jqx-all.js"></script>
		<script type="text/javascript" src="libs/jqwidgets/globalization/globalize.culture.it-IT.js"></script>
        {literal}
        <script type="text/javascript">
            var url_api = "../next-api/v1/";
            var url_preiscrizioni = "../preiscrizioni/";
            var modifica = 'SI';

            var idModulo = "";
            var datiModulo = {};
            var comuni = [];
            var stati = [];
            var professioni = [];
            var titoli_studio = [];
            var prenotazioni = [];
            var prenotazioniDisponibili = [];
 
            function scriviDato(dato, chiave, modifica = 'NO'){
                var html = "";

                if (dato.label){
                    html += "<div class='margin2'>";
                    html += dato.label;
                    if (dato.tipo){
                        html += ": ";
                    }
                    if (typeof dato.valore != 'undefined'){
                        if (modifica == 'SI'){
                            switch (dato.tipo){
                                case 'text':
                                    if (dato.valore == null){
                                        dato.valore = '';
                                    }
                                    html += '<input type="text" name="'+chiave+'" value="'+dato.valore.replace('"', "'")+'" style="width: 400px;">';
                                    break;
                                case 'area':
                                    if (dato.valore == null){
                                        dato.valore = '';
                                    }
                                    html += "<textarea name='"+chiave+"' cols='70' style='vertical-align: middle;'>"+dato.valore+"</textarea>";
                                    break;
                                case 'check':
                                    html += "<input type='hidden' name='"+chiave+"' value='0'>";
                                    html += "<input type='checkbox' name='"+chiave+"' value='1' ";
                                    if (dato.valore == 1){
                                        html += " checked ";
                                    }
                                    html += ">";
                                    break;
                                case 'select':
                                    html += "<select name='"+chiave+"'>";
                                    html += "<option value=''";
                                    if (dato.valore == null){
                                        html += " selected ";
                                    }
                                    html += "></option>";
                                    Object.keys(dato.opzioni).forEach(function(index){
                                        html += "<option value='"+index+"' ";
                                        if (dato.valore == index){
                                            html += " selected ";
                                        }
                                        html += ">"+dato.opzioni[index]+"</option>";
                                    });
                                    html += "</select>";
                                    break;
                                case 'date':
                                    var id = chiave;
                                    id = id.replaceAll('[', '');
                                    id = id.replaceAll(']', '');

                                    if (dato.valore == null){
                                        dato.valore = '';
                                    } else {
                                        var date = dato.valore.split('/')[2]+'-'+dato.valore.split('/')[1]+'-'+dato.valore.split('/')[0];
                                    }
                                    html += "<input type='date' value='"+date+"' onchange='settaData(this.value, \""+id+"\");'>";
                                    html += "<input type='hidden' id='"+id+"' name='"+chiave+"' value='"+dato.valore+"'>";
                                    break;
                                case 'datetime':
                                    if (dato.valore == null){
                                        dato.valore = '';
                                    }
                                    html += "<input type='time' name='"+chiave+"' value='"+dato.valore+"'>";
                                    break;
                                case 'comune':
                                    // prestazioni ottimizzabili 
                                    html += "<select name='"+chiave+"'>";
                                    html += "<option value='XXXX'";
                                    if (dato.valore == null){
                                        html += " selected ";
                                    }
                                    html += "></option>";
                                    Object.keys(comuni).forEach(function(index){
                                        var c = comuni[index];
                                        html += "<option value='"+c.id_comune+"' ";
                                        if (dato.valore == c.id_comune){
                                            html += " selected ";
                                        }
                                        html += ">"+c.descrizione+" ("+c.provincia+")</option>";
                                    });
                                    html += "</select>";
                                    break;
                                case 'stato':
                                    html += "<select name='"+chiave+"'>";
                                    html += "<option value='------'";
                                    if (dato.valore == null){
                                        html += " selected ";
                                    }
                                    html += "></option>";
                                    Object.keys(stati).forEach(function(index){
                                        var s = stati[index];
                                        html += "<option value='"+s.id_stato+"' ";
                                        if (dato.valore == s.id_stato){
                                            html += " selected ";
                                        }
                                        html += ">"+s.descrizione+"</option>";
                                    });
                                    html += "</select>";
                                    break;
                                case 'professione':
                                    html += "<select name='"+chiave+"'>";
                                    html += "<option value='------'";
                                    if (dato.valore == null){
                                        html += " selected ";
                                    }
                                    html += "></option>";
                                    Object.keys(professioni).forEach(function(index){
                                        var p = professioni[index];
                                        html += "<option value='"+p.id_professione+"' ";
                                        if (dato.valore == p.id_professione){
                                            html += " selected ";
                                        }
                                        html += ">"+p.descrizione+"</option>";
                                    });
                                    html += "</select>";
                                    break;
                                case 'titolo_studio':
                                    html += "<select name='"+chiave+"'>";
                                    html += "<option value='------'";
                                    if (dato.valore == null){
                                        html += " selected ";
                                    }
                                    html += "></option>";
                                    Object.keys(titoli_studio).forEach(function(index){
                                        var p = titoli_studio[index];
                                        html += "<option value='"+p.id_titolo_studio+"' ";
                                        if (dato.valore == p.id_titolo_studio){
                                            html += " selected ";
                                        }
                                        html += ">"+p.descrizione+"</option>";
                                    });
                                    html += "</select>";
                                    break;
                            }
                        } else {
                            switch (dato.tipo){
                                case 'check':
                                    if (dato.valore == 1){
                                        html += "<b>SI</b>";
                                    } else {
                                        html += "<b>NO</b>";
                                    }
                                    break;
                                case 'select':
                                    if (dato.valore == null){
                                        dato.valore = '';
                                    }
                                    html += "<b>";
                                    var trovato = false;
                                    Object.keys(dato.opzioni).forEach(function(index){
                                        if (dato.valore == index){
                                            html += dato.opzioni[index];
                                            trovato = true;
                                        }
                                    });
                                    if (!trovato){
                                        html += dato.valore;
                                    }
                                    html += "</b>";
                                    break;
                                case 'comune':
                                    var trovato = false;
                                    Object.keys(comuni).forEach(function(index){
                                        var c = comuni[index];
                                        if (dato.valore == c.id_comune){
                                            html += "<b>" + c.descrizione + "</b>";
                                            trovato = true;
                                        }
                                    });

                                    if (!trovato){
                                        html += "<b>" + dato.valore + "</b>";
                                    }
                                    break;
                                case 'stato':
                                    var trovato = false;
                                    Object.keys(stati).forEach(function(index){
                                        var s = stati[index];
                                        if (dato.valore == s.id_stato){
                                            html += "<b>" + s.descrizione + "</b>";
                                            trovato = true;
                                        }
                                    });

                                    if (!trovato){
                                        html += "<b>" + dato.valore + "</b>";
                                    }
                                    break;
                                case 'professione':
                                    var trovato = false;
                                    Object.keys(professioni).forEach(function(index){
                                        var p = professioni[index];
                                        if (dato.valore == p.id_professione){
                                            html += "<b>" + p.descrizione + "</b>";
                                            trovato = true;
                                        }
                                    });

                                    if (!trovato){
                                        html += "<b>" + dato.valore + "</b>";
                                    }
                                    break;
                                default:
                                    if (dato.valore == null){
                                        dato.valore = '';
                                    }
                                    html += "<b>" + dato.valore + "</b>";
                                    break;
                            }
                        }
                    }
                    html += "</div>";
                }

                return html;
            }

            function settaData(data, id){
                if (data != ""){
                    var valoreTmp = data.split('-')[2]+"/"+data.split('-')[1]+"/"+data.split('-')[0];
                } else {
                    var valoreTmp = "";
                }

                $('#'+id).val(valoreTmp);
            }

            function scriviModulo(datoModulo){
                var html = "";

                html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                html += "<h3>Dati modulo</h3>";
                html += "<div>Nome: <b>"+datoModulo.nome+"</b></div>";
                //html += "<div>Fase: <b>"+datoModulo.fasi[datiModulo.fase]+"</b></div>";

                html += "<div>Fase: ";
                html +=     "<select class='select' name='fase_modulo'>";
                Object.keys(datoModulo.fasi).forEach(function (index){
                    var fase = (datoModulo.fasi[index] != "") ? datoModulo.fasi[index] : index;

                    html += "<option value='"+index+"' ";
                    if (index == datiModulo.fase){
                        html += " selected ";
                    }
                    html += "><b>"+fase+"</b></option>";
                });
                html +=     "</select>";
                html +=     "<button type='button'";
                html +=         "style='font-size: 90%;'";
                html +=         "class='btn_flat btn_padding_ridotto sfondo_verde testo_bianco ombra_testo ripples margin6'";
                html +=         "onclick='aggiornaFase();'";
                html +=     ">Salva fase</button>";

                html += "</div>";

                html += "<div>Data inserimento: <b>"+datoModulo.insert_date+"</b></div>";
                html += "<div>Inserito da: <b>"+datoModulo.insert_name+"</b></div>";
                html += "<div>Data ultima modifica: <b>"+datoModulo.modify_date+"</b></div>";
                html += "<div>Utente ultima modifica: <b>"+datoModulo.modify_name+"</b></div>";
                if ($("#nx").val() == "SI"){
                    html += "<div>ID modulo (nexus): <b>"+datoModulo._id+"</b></div>";
                }
                html += "</div>";

                if (datoModulo.opzioni){
                    html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                    html += "<h3>Opzioni</h3>";
                    Object.keys(datoModulo.opzioni).forEach(function(index){
                        var dato = datoModulo.opzioni[index];
                        html += scriviDato(dato, 'datiModulo[opzioni]['+datoModulo.opzioni[index].funzione+']['+datoModulo.opzioni[index].campo+']', modifica);
                    });
                    html += "</div>";
                }

                if (datoModulo.studente){
                    html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                    html += "<h3>Studente</h3>";
                    Object.keys(datoModulo.studente).forEach(function(index){
                        var dato = datoModulo.studente[index];
                        html += scriviDato(dato, 'datiModulo[studente]['+datoModulo.studente[index].funzione+']['+datoModulo.studente[index].campo+']', modifica);
                    });
                    html += "</div>";
                }

                //if (datoModulo.dati_medici){
                //    html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                //    html += "<h3>Dati medici</h3>";
                //    Object.keys(datoModulo.dati_medici).forEach(function(index){
                //        var dato = datoModulo.dati_medici[index];
                //        //html += scriviDato(dato, 'datiModulo[dati_medici]['+datoModulo.dati_medici[index].funzione+']['+datoModulo.dati_medici[index].campo+']', modifica);
                //        html += "<div class='margin2'>" + index + ": ";
                //        html += "<input type='text' name='datiModulo[dati_medici][dati_medici]["+index+"]' value='"+dato+"' style='width: 400px;'>";
                //        html += "</b></div>";
                //    });
                //    html += "</div>";
                //}

                if (datoModulo.parenti){
                    html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                    html +=     "<h3>Parenti</h3>";
                    html +=     "<div style='display: flex; flex-wrap: wrap;'>";
                    Object.keys(datoModulo.parenti).forEach(function(index){
                        var parente = datoModulo.parenti[index];
                        html += "<div class='margin6'>";
                        Object.keys(parente).forEach(function(index2){
                            var dato = parente[index2];
                            html += scriviDato(dato, 'datiModulo[parenti]['+datoModulo.parenti[index][index2].funzione+']['+index+']['+datoModulo.parenti[index][index2].campo+']', modifica);
                        });
                        html += "</div>";
                    });
                    html +=     "</div>";
                    html += "</div>";
                }

                if (datoModulo.pagante){
                    html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                    html += "<h3>Pagante</h3>";
                    Object.keys(datoModulo.pagante).forEach(function(index){
                        var dato = datoModulo.pagante[index];
                        html += scriviDato(dato, 'datiModulo[pagante]['+datoModulo.pagante[index].funzione+']['+datoModulo.pagante[index].campo+']', modifica);
                    });
                    html += "</div>";
                }

                if (datoModulo.consensi){
                    html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                    html += "<h3>Consensi</h3>";
                    Object.keys(datoModulo.consensi).forEach(function(index){
                        var dato = datoModulo.consensi[index];
                        html += scriviDato(dato, 'datiModulo[consensi]['+datoModulo.consensi[index].funzione+']['+datoModulo.consensi[index].campo+']', 'NO');
                    });
                    html += "</div>";
                }

                if (datoModulo.servizi){
                    html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                    html += "<h3>Servizi</h3>";
                    Object.keys(datoModulo.servizi).forEach(function(index){
                        var dato = datoModulo.servizi[index];
                        html += scriviDato(dato, 'datiModulo[servizi]['+datoModulo.servizi[index].funzione+']['+datoModulo.servizi[index].campo+']', 'NO');
                    });
                    html += "</div>";
                }

                var indexNuovoColloquio = 0;
                html += "<div class='padding6 bordo_azzurro bordrad5 margin6' align='center'>";
                html += "<h3>Colloqui e Punteggi</h3>";
                if (datoModulo.colloqui){
                    html += "<table>";
                    html +=     "<tr>";
                    html +=         "<td class='padding3 bold' align='center'>Data</td>";
                    html +=         "<td class='padding3 bold' align='center'>Effettuato</td>";
                    html +=         "<td class='padding3 bold' align='center'>Punteggio</td>";
                    html +=         "<td class='padding3 bold' align='center'></td>";
                    html +=     "</tr>";
                    Object.keys(datoModulo.colloqui).forEach(function(index){
                        var colloquio = datoModulo.colloqui[index];
                        indexNuovoColloquio = parseInt(index) + 1;

                        var data = "";
                        if (colloquio.data > 0){
                            var date = new Date(colloquio.data*1000);
                            data = date.getFullYear()+"-"+fillZero((date.getMonth()+1))+"-"+fillZero(date.getDate());
                        }

                        html +=     "<tr class='bordo_alto_generico'>";
                        html +=         "<td class='padding3' align='left'>";
                        html +=             "<input type='date' value='"+data+"' id='dataModificaColloquio"+index+"' onchange='aggiornaDataHidden(this.value, \"dataModificaColloquioHidden"+index+"\", \"modificaColloquioEffettuatoHidden"+index+"\");'>";
                        if (!colloquio.data || colloquio.data == ""){
                            html +=         "<button type='button' onclick='settaOggi(\"dataModificaColloquio"+index+"\");'>Oggi</button>";
                        }
                        html +=             "<input type='hidden' name='datiModificaColloquio[colloqui]["+index+"][data]' value='"+colloquio.data+"' id='dataModificaColloquioHidden"+index+"'>";
                        html +=             "<input type='hidden' name='datiModificaColloquio[colloqui]["+index+"][effettuato]' value='"+((colloquio.effettuato === true)?"1":"0")+"' id='modificaColloquioEffettuatoHidden"+index+"'>";
                        html +=         "</td>";
                        html +=         "<td class='padding3' align='center'>"+((colloquio.effettuato === true)?"SI":"NO")+"</td>";
                        html +=         "<td class='padding3' align='center'>";
                        html +=             "<input type='number' name='datiModificaColloquio[colloqui]["+index+"][punteggio]' value='"+colloquio.punteggio+"' style='width: 70px;'>";
                        html +=             "<input type='hidden' name='datiModificaColloquio[colloqui]["+index+"][fase]' value='"+colloquio.fase+"'>";
                        html +=         "</td>";
                        html +=         "<td class='padding3' align='center'>";
                        //html +=             "<button type='button'";
                        //html +=                "class='btn_flat btn_padding_ridotto sfondo_rosso testo_bianco ombra_testo margin-left8'";
                        //html +=                "onclick='eliminaColloquio("+index+");'";
                        //html +=             ">&times;</button>";
                        html +=         "</td>";
                        html +=     "</tr>";
                    });
                    html += "</table>";
                    //html += "<button type='button'";
                    //html +=    "class='btn_flat btn_padding_ridotto bordo_verde testo_verde margin6'";
                    //html +=    "onclick='modificaColloqui();'";
                    //html += ">Salva</button>";
                } else {
                    html += "<p class='annotazione_leggera'>Nessun colloquio presente</p>";
                }
                html += "<h3>Nuovo</h3>";
                html += "<table>";
                html +=     "<tr>";
                html +=         "<td class='padding3'>Data colloquio</td>";
                html +=         "<td class='padding3'>";
                html +=             "<input type='date' value='' id='dataNuovoColloquio' onchange='aggiornaDataHidden(this.value, \"dataNuovoColloquioHidden\", \"nuovoColloquioEffettuatoHidden\");'>";
                html +=             "<button type='button' onclick='settaOggi(\"dataNuovoColloquio\");'>Oggi</button>";
                html +=             "<input type='hidden' name='datiNuovoColloquio[colloqui]["+indexNuovoColloquio+"][data]' value='' id='dataNuovoColloquioHidden'>";
                html +=             "<input type='hidden' name='datiNuovoColloquio[colloqui]["+indexNuovoColloquio+"][effettuato]' value='' id='nuovoColloquioEffettuatoHidden'>";
                html +=         "</td>";
                html +=     "</tr>";
                html +=     "<tr>";
                html +=         "<td class='padding3'>Punteggio</td>";
                html +=         "<td class='padding3'>";
                html +=             "<input type='number' name='datiNuovoColloquio[colloqui]["+indexNuovoColloquio+"][punteggio]' value='0'>";
                html +=             "<input type='hidden' name='datiNuovoColloquio[colloqui]["+indexNuovoColloquio+"][fase]' value='"+datiModulo.fase+"'>";
                html +=         "</td>";
                html +=     "</tr>";
                html += "</table>";
                html += "<button type='button'";
                html +=    "class='btn_flat btn_padding_ridotto bordo_verde testo_verde margin6'";
                html +=    "onclick='inserisciColloquio();'";
                html += ">Inserisci Colloquio</button>";
                html += "</div>";

                html += "<div class='padding6 bordo_azzurro bordrad5 margin6'>";
                html += "<h3>Prenotazioni effettuate</h3>";
                html += "<div id='div_prenotazioni_effettuate' align='center'></div>";
                html += "<h3>Prenotazioni disponibili</h3>";
                html += "<div id='div_prenotazioni_disponibili' align='center'></div>";
                html += "</div>";

                return html;
            }

            function settaOggi(idData){
                var date = new Date ();
                var oggi = date.getFullYear()+"-"+fillZero((date.getMonth()+1))+"-"+fillZero(date.getDate());
                $('#'+idData).val(oggi).trigger('change');
            }

            function aggiornaDataHidden(data, idDataHidden, idEffettuatoHidden){
                var hidData = "";
                var hidEffettuato = 0;

                if (data != ''){
                    var nuovaData = new Date (data);
                    hidData = nuovaData.getTime()/1000;
                    hidEffettuato = 1;
                }

                $("#"+idDataHidden).val(hidData);
                $("#"+idEffettuatoHidden).val(hidEffettuato);
            }

            function apriModulo(){
                return new Promise((resolve, reject) => {
                    var current_user = $('#current_user').val();
                    var current_key = $('#current_key').val();
                    var stato_principale = $('#stato_principale').val();
                    var db_key = $('#db_key').val();

                    var request = $.ajax({
                        url: url_api + "registrations/editor/"+idModulo,
                        method: "GET",
                        headers: { 'Authorization': $('#current_key').val() },
                        data: {},
                        cache: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        complete: function (response) {
                            $('#jqxLoader').jqxLoader('close');
                            datiModulo = response.responseJSON;
                            var html = "";

                            document.title = datiModulo.nome + " - " + datiModulo.label_studente;

                            html = scriviModulo(datiModulo);

                            $("#div_dati_modulo").html(html);

                            getPrenotazioni().then(() => { scriviPrenotazioni(); });

                            if (modifica == 'SI'){
                                $('#btn_salva').show();
                            }

                            resolve();
                        }
                    });
                });
            }

            function scriviPrenotazioni(){
                var html = "";
                var html2 = "";
                var prenotazioniEffettuate = 0;

                if (prenotazioni.length > 0){
                    html += "<table>";
                    html +=     "<tr>";
                    html +=         "<td class='padding3 bold' align='center'>Data</td>";
                    html +=         "<td class='padding3 bold' align='center'>Ora</td>";
                    html +=         "<td></td>";
                    html +=     "</tr>";

                    Object.keys(prenotazioni).forEach(function(index){
                        var prenotazione = prenotazioni[index];
                        if (prenotazione.slots.length > 0){
                            Object.keys(prenotazione.slots).forEach(function(index2){
                                var slot = prenotazione.slots[index2];

                                var inizio = new Date(slot.start*1000);
                                var fine = new Date(slot.end*1000);
                                slot.data = fillZero(inizio.getDate())+"/"+fillZero((inizio.getMonth()+1))+"/"+inizio.getFullYear();
                                slot.ora = fillZero(inizio.getHours())+":"+fillZero(inizio.getMinutes())+" - "+fillZero(fine.getHours())+":"+fillZero(fine.getMinutes());

                                if (slot.reserved == 1 && slot.module == idModulo){
                                    prenotazioniEffettuate++;

                                    html +=     "<tr class='bordo_alto_generico'>";
                                    html +=         "<td class='padding3' align='center'>"+slot.data+"</td>";
                                    html +=         "<td class='padding3' align='center'>"+slot.ora+"</td>";
                                    html +=         "<td class='padding3'>";
                                    html +=             "<button type='button'";
                                    html +=                "class='btn_flat btn_padding_ridotto sfondo_rosso testo_bianco ombra_testo margin-left8'";
                                    html +=                "onclick='eliminaPrenotazione(\""+slot._id+"\");'";
                                    html +=             ">&times;</button>";
                                    html +=         "</td>";
                                    html +=     "</tr>";
                                } else if (slot.reserved == 0){
                                    if (!prenotazioniDisponibili[slot.data]){
                                        prenotazioniDisponibili[slot.data] = [];
                                    }
                                    prenotazioniDisponibili[slot.data].push(slot);
                                }
                            });
                        }
                    });

                    if (prenotazioniEffettuate == 0){
                        html +=     "<tr class='bordo_alto_generico'>";
                        html +=         "<td class='padding3 annotazione_leggera' align='center' colspan='3'>Nessuna prenotazione effettuata</td>";
                        html +=     "</tr>";
                    }

                    html += "</table>";

                    if (Object.keys(prenotazioniDisponibili).length > 0){
                        html2 += "Slot:";

                        html2 += "<select class='select margin-left8' onchange='caricaSlotGiorno(this.value);' id='selectSlotDate'>";
                        Object.keys(prenotazioniDisponibili).forEach(function (indexData){
                            var slotGiorno = prenotazioniDisponibili[indexData];
                            html2 += "<option value='"+indexData+"'>"+indexData+"</option>";
                        });
                        html2 += "</select>";

                        html2 += "<select class='select margin-left8' id='seletSlotOrari'>";
                        html2 += "</select>";
                        
                        html2 += "<br><button type='button'";
                        html2 +=    "class='btn_flat btn_padding_ridotto bordo_verde testo_verde margin6'";
                        html2 +=    "onclick='inserisciPrenotazione();'";
                        html2 += ">Inserisci prenotazione</button>";
                    } else {
                        html2 += "<p class='annotazione_leggera'>Nessuna prenotazione libera</p>";
                    }
                } else {
                    html += "<p class='annotazione_leggera'>Nessuna prenotazione disponibile</p>";
                    html2 += "<p class='annotazione_leggera'>Nessuno slot disponibile</p>";
                }

                $('#div_prenotazioni_effettuate').html(html);
                $('#div_prenotazioni_disponibili').html(html2);

                $('#selectSlotDate').trigger('change');
            }

            function caricaSlotGiorno(data){
                var html = "";
                Object.keys(prenotazioniDisponibili[data]).forEach(function (index){
                    var slot = prenotazioniDisponibili[data][index];
                    html += "<option value='"+slot._id+"'>"+slot.ora+"</option>";
                });

                $('#seletSlotOrari').html(html);
            }

            function getElencoComuni(){
                return new Promise((resolve, reject) => {
                    var request = $.ajax({
                        url: url_api + "school/presets/comuni",
                        method: "GET",
                        headers: { 'Authorization': $('#current_key').val() },
                        data: {},
                        cache: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        complete: function (response) {
                            comuni = response.responseJSON;                        
                            resolve();
                        }
                    });
                });
            }

            function getElencoStati(){
                return new Promise((resolve, reject) => {
                    var request = $.ajax({
                        url: url_api + "school/presets/stati",
                        method: "GET",
                        headers: { 'Authorization': $('#current_key').val() },
                        data: {},
                        cache: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        complete: function (response) {
                            stati = response.responseJSON;  
                            resolve();
                        }
                    });
                });
            }

            function getElencoProfessioni(){
                return new Promise((resolve, reject) => {
                    var request = $.ajax({
                        url: url_api + "school/presets/professioni",
                        method: "GET",
                        headers: { 'Authorization': $('#current_key').val() },
                        data: {},
                        cache: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        complete: function (response) {
                            professioni = response.responseJSON;  
                            resolve();
                        }
                    });
                });
            }

            function getElencoTitoliStudio(){
                return new Promise((resolve, reject) => {
                    var request = $.ajax({
                        url: url_api + "school/presets/titoli_studio",
                        method: "GET",
                        headers: { 'Authorization': $('#current_key').val() },
                        data: {},
                        cache: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        complete: function (response) {
                            titoli_studio = response.responseJSON;  
                            resolve();
                        }
                    });
                });
            }

            function getPrenotazioni(){
                return new Promise((resolve, reject) => {
                    var request = $.ajax({
                        url: url_api + "calendar/availabilities/registration/"+idModulo,
                        method: "GET",
                        headers: { 'Authorization': $('#current_key').val() },
                        data: {},
                        cache: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        complete: function (response) {
                            prenotazioni = response.responseJSON;  
                            resolve();
                        }
                    });
                });
            }

            function inserisciPrenotazione(){
                idSlot = $('#seletSlotOrari').val();
                $('#jqxModalLoader').jqxLoader('open');

                var request = $.ajax({
                    url: url_api + "registrations/"+idModulo+"/reserve-meeting",
                    method: "POST",
                    headers: { 'Authorization': $('#current_key').val() },
                    data: {"selected_slot": idSlot},
                    cache: false,
                    dataType: "json",
                    contentType: "application/json; charset=utf-8",
                    complete: function (response) {
                        var r = response.responseJSON;  
                        
                        getPrenotazioni().then(() => { scriviPrenotazioni(); $('#jqxModalLoader').jqxLoader('close'); });
                    }
                });
            }

            function eliminaPrenotazione(idSlot){
                if (confirm("Eliminare la prenotazione?")){
                    $('#jqxModalLoader').jqxLoader('open');

                    var request = $.ajax({
                        url: url_api + "registrations/"+idModulo+"/cancel-meeting",
                        method: "POST",
                        headers: { 'Authorization': $('#current_key').val() },
                        data: {"selected_slot": idSlot},
                        cache: false,
                        dataType: "json",
                        contentType: "application/json; charset=utf-8",
                        complete: function (response) {
                            var r = response.responseJSON;  
                            
                            getPrenotazioni().then(() => { scriviPrenotazioni(); $('#jqxModalLoader').jqxLoader('close'); });
                        }
                    });
                }
            }

            function salvaModulo(){
                $('#jqxModalLoader').jqxLoader('open');
                $('#operazione').val('salva');
                $('#form-container').submit();
            }

            function inserisciColloquio(){
                $('#jqxModalLoader').jqxLoader('open');
                $('#operazione').val('inserisci_colloquio');
                $('#form-container').submit();
            }

            function modificaColloqui(){
                $('#jqxModalLoader').jqxLoader('open');
                $('#operazione').val('modifica_colloqui');
                $('#form-container').submit();
            }

            function eliminaColloquio(indexColloquio){
                if (confirm("Eliminare?")){
                    $('#operazione').val('elimina_colloquio');
                    $('#index_colloquio').val(indexColloquio);
                    $('#form-container').submit();
                }
            }

            function aggiornaFase(){
                $('#jqxModalLoader').jqxLoader('open');
                $('#operazione').val('aggiorna_fase');
                $('#form-container').submit();
            }

            $(document).ready( function(){
                $("#jqxLoader").jqxLoader({ text: "", width: 60, height: 60 });
                $("#jqxModalLoader").jqxLoader({ text: "", width: 60, height: 60, isModal: true });
                $('#jqxLoader').jqxLoader('open');
                
                var p1 = getElencoComuni();
                var p2 = getElencoStati();
                var p3 = getElencoProfessioni();
                var p4 = getElencoTitoliStudio();
                idModulo = $('#id_modulo').val();

                Promise.all([p1, p2, p3, p4]).then(() => { apriModulo(); });
            });
        </script>
        {/literal}
        <link rel="stylesheet" href="css/style.css">
        <link rel="shortcut icon" type="image/x-icon" href="icone/mastercom.ico">
    </HEAD>
    <body class="font-normal">
        <form method='post' id="form-container">
            <div class="margin8">
                <div class="div_titolo_scheda_generica" align="left" style="background-color: #227c5e; color: white; font-size: 110%;">
                    <div style="font-weight: bold; padding-left: 10px;">Modulo</div>
                </div>
                <div class="div_corpo_scheda_generica padding8" >
                    <div id="div_dati_modulo" style="display: flex; flex-wrap: wrap;">

                    </div>
                    <div align="center">
                        <button type="button"
                            id="btn_salva"
                            style="display: none;"
                            class="btn_pieno sfondo_verde testo_bianco ombra_testo ripples"
                            onclick="salvaModulo();"
                            >Salva</button>
                    </div>
                </div>
            </div>
        
            <div id="jqxLoader"></div>
            <div id="jqxModalLoader"></div>

            <input type='hidden' name='current_user' id="current_user" value="{$current_user}">
            <input type='hidden' name='current_key' id="current_key" value="{$current_key}">
            <input type='hidden' name='nx' id="nx" value="{$nx}">
            <input type='hidden' name='operazione' id="operazione" value="{$operazione}">
            <input type='hidden' name='id_modulo' id="id_modulo" value="{$id_modulo}">
            <input type='hidden' name='index_colloquio' id="index_colloquio" value="">
        </form>
    </body>
</html>
