<?php

/*
 * Tipo: Pagella Intermedia myschoolticino
 * Nome: ee_12345_finale_myschoolticino_01
 * <PERSON><PERSON> da: myschoolticino
 * Data: 7/6/22
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H', 'ee_12345_finale_myschoolticino_01', 'Valutazini di Fine anno Scuola Primaria', 1, 'myschoolticino', 2);

 */

$orientamento = 'P';
$formato = 'A4';
$periodo_pagella = 'finale';

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">

    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_studente = $studente['id_studente'];
    $id_classe = $parametri_stampa['id_classe'];
    $data_att = "{$parametri_stampa['data_day']}/{$parametri_stampa['data_month']}/{$parametri_stampa['data_year']}";

    $luogo_nascita = $provincia_nascita = '';
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .=  ' (' . $stato['descrizione'] . ')';
            $provincia_nascita = ' ';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_finale = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $id_studente);

    $cls_st = $studente['classe'];
    if ($studente['classe'] == 4) {
        if (in_array("{$studente['cognome']} {$studente['nome']}",
                [
                    "ADRIANO FEDERICO",
                    "BELUSSI SAMUELE",
                    "DIAMANTIS GALLO ELIAN",
                    "FLACHSMANN JAMES",
                    "FOSSATI MAELLE",
                    "GODIO PIETRO",
                    "LONGHI AURORA",
                    "MARZORATI LUCA EMANUELE",
                    "SOMARUGA ALICE NOEMI",
                ])
            ) {
            $cls_st = 5;
        }
    }

    $professori = [];
    $arr_voti = [];
    $assenze_giustificate = $assenze_arbitrarie = $decisione_fine = $osservazioni = '';
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
               !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO', 'OPZIONALE'])
                )
            )
        {
            $arr_voti[$id_materia] = $voti_finale[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                if (stripos($materia['descrizione'], 'francese') !== false && ($cls_st == 4 || $cls_st == 5)) {
                    $prof = estrai_utente($id_professore);

                    if ($cls_st == 4) {
                        if (stripos('Bouille', $prof['cognome'])===false){ 
                            continue; 
                        }
                    } elseif ($cls_st == 5) {
                        if (stripos('Peverelli', $prof['cognome'])===false){ 
                            continue; 
                        }
                    }              
                }
                $professori[$id_professore] = $id_professore;
            }
        }

        // religione
        if ($materia['in_media_pagelle'] != 'NV' &&
                (
                    ($materia['tipo_materia'] == 'RELIGIONE' && $studente['esonero_religione'] == 0)
                    ||
                    ($materia['tipo_materia'] == 'ALTERNATIVA' && $studente['esonero_religione'] == 1)
                )
            )
        {
            $arr_voti[$id_materia] = $voti_finale[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }
        }

        // condotta
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
           $arr_voti[$id_materia] = $voti_finale[$id_materia];

            foreach ($materia['professori'] as $id_professore)
            {
                $professori[$id_professore] = $id_professore;
            }

        }
        foreach ($voti_finale[$id_materia]['campi_liberi'] as $campo_libero) {
            if (stripos($campo_libero['nome'], 'Assenze giustificate') !== false) {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value !== '') {
                    $assenze_giustificate .= $value;
                }
            }
        }
        foreach ($voti_finale[$id_materia]['campi_liberi'] as $campo_libero) {
            if (stripos($campo_libero['nome'], 'Assenze arbitrarie') !== false) {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value !== '') {
                    $assenze_arbitrarie .= $value;
                }
            }
        }
        foreach ($voti_finale[$id_materia]['campi_liberi'] as $campo_libero) {
            if (stripos($campo_libero['nome'], 'Decisione di fine anno') !== false) {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value !== '') {
                    $decisione_fine .= $value;
                }
            }
        }
        foreach ($voti_finale[$id_materia]['campi_liberi'] as $campo_libero) {
            if (stripos($campo_libero['nome'], 'Osservazioni generali') !== false) {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value !== '') {
                    $osservazioni .= $value;
                }
            }
        }
    }

    // ordinamento diverso professori per classe 1SE
    if ($studente['classe'] == 1 && $studente['sezione'] == 'SE') {
        $prof_nths = array_keys($professori);

        $professori = array_slice($professori, 0, 1) +
                array( $prof_nths[2] => $professori[$prof_nths[2]] ) +
                array( $prof_nths[1] => $professori[$prof_nths[1]] ) +
                array_slice($professori, 2);
    }
    $stringa_docenti = [];
    $stringa_docenti_pre = []   ;
    foreach($professori as $prof) {
        $prof = estrai_utente($prof);

        $doc_included = false;
        switch($studente['classe']) {
            case 1:
                if (
                    (stripos('Lattuada', $prof['cognome']) !== false && stripos('Patrizia', $prof['nome']) !== false)
                    ||
                    (stripos('Digonzelli', $prof['cognome']) !== false && stripos('Elena', $prof['nome']) !== false)
                    ) {
                    $stringa_docenti_pre[] = ucwords(strtolower("{$prof['cognome']} {$prof['nome']}"));
                    $doc_included = true;
                }
                break;
            case 2:
                if (
                    (stripos('Simoncelli', $prof['cognome']) !== false && stripos('Barbara', $prof['nome']) !== false)
                    ||
                    (stripos('Littlewood', $prof['cognome']) !== false && stripos('Suzanne', $prof['nome']) !== false)
                    ) {
                    $stringa_docenti_pre[] = ucwords(strtolower("{$prof['cognome']} {$prof['nome']}"));
                    $doc_included = true;
                }
                break;
            case 3:
                if (
                    (stripos('Caprari', $prof['cognome']) !== false && stripos('Jessica', $prof['nome']) !== false)
                    ||
                    (stripos('Rengifo', $prof['cognome']) !== false && stripos('July', $prof['nome']) !== false)
                    ) {
                    $stringa_docenti_pre[] = ucwords(strtolower("{$prof['cognome']} {$prof['nome']}"));
                    $doc_included = true;
                }
                break;
            case 4:
            case 5:
                if (
                    (stripos('Casnati', $prof['cognome']) !== false && stripos('Claudia', $prof['nome']) !== false)
                    ||
                    (stripos('Rae', $prof['cognome']) !== false && stripos('Samantha', $prof['nome']) !== false)
                    ) {
                    $stringa_docenti_pre[] = ucwords(strtolower("{$prof['cognome']} {$prof['nome']}"));
                    $doc_included = true;
                }
        }
        
        if (!$doc_included) {
            if (stripos('utente', $prof['cognome'])===false && stripos('utente', $prof['nome'])===false
                    && stripos('Terzaquarta', $prof['nome'])===false){
                $stringa_docenti[] = ucwords(strtolower("{$prof['cognome']} {$prof['nome']}"));
            }
        }
    }
    $stringa_docenti = array_merge($stringa_docenti_pre, $stringa_docenti);
    $stringa_docenti = implode(', ', $stringa_docenti);
    $stringa_docenti = str_replace('G.o.mersi Verislene', 'G.O.Mersi Verislene', $stringa_docenti);
    // Dizionario temporaneo
    $labels = [
        "allievo"   => "Alliev||min_oa||"
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    $firma_dirigente = $firma_docenti = $firma_supervisione = '';
    switch ($studente['classe']) {
        case 1:
            $firma_docenti = '<img src="immagini_scuola/patrizia-lattuada.png" height="30" width="130"> <br>'
                . '<img src="immagini_scuola/elena-digonzelli.png" height="30" width="130">';
            break;
        case 2:$firma_docenti = '<img src="immagini_scuola/littlewood_suzanne.png" height="30" width="130"> <br>'
                . '<img src="immagini_scuola/barbara-simoncelli.png" height="30" width="130">';
            break;
        case 3:
            $firma_docenti = '<img src="immagini_scuola/rengifo_july.jpg" height="35" width="130"> <br>'
                . '<img src="immagini_scuola/jessica-caprari.png" height="30" width="130">';
            break;
        case 4:
        case 5:
            $firma_docenti = '<img src="immagini_scuola/casnati-claudia.png" height="30" width="130"> <br>'
                . '<img src="immagini_scuola/samantha-rae.png" height="30" width="130"> <br>'
                // . '<img src="immagini_scuola/cowan-arianna.png" height="30" width="130"> <br>'
                ;
            break;
    }
    // $firma_dirigente = '<img src="immagini_scuola/direzione-alessandra-milani.png" height="30" width="130">';
    $firma_dirigente = '<img src="immagini_scuola/elisabetta-masini.png" height="30" width="130">';
    $firma_supervisione = '<img src="immagini_scuola/micaela-mecocci.png" height="30" width="130">';

    $esito_stampa = '';
    if (stripos($studente['esito'], 'non')) {
        $esito_stampa = 'non Promosso';
//    } elseif ($studente['esito'] != 'Iscritto') {
    } else {
        $esito_stampa = 'Promosso';
    }
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf">

    // Image($file, $x='', $y='', $w=0, $h=0, $type='', $link='', $align='', $resize=false, $dpi=300, $palign='', $ismask=false, $imgmask=false, $border=0, $fitbox=false, $hidden=false, $fitonpage=false)
    $pdf->AddPage('P');
    $f = 'helvetica';
    $fd = 10;
    $pdf->SetAutoPageBreak("off", 0);
    $img_bg = 'immagini_scuola/carta_intestata.jpg';
    $pdf->Image($img_bg, 5, 0, 220, 315, 'JPG', false, '', true); $pdf->setPageMark();

    $pdf->SetY(30);
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->writeHTMLCell(0, 8, '', '', "VALUTAZIONI DI FINE ANNO", 0, 1, false, true, 'L');
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell(0, 0, '', '', '<table cellpadding="4">'
        . '<tr>'
            . '<td width="75%">My School Ticino</td>'//Ticino
            . '<td width="25%">' . "Anno $anno_scolastico_attuale" . '</td>'
        . '</tr>'
        . '<tr>'
. '<td width="75%">' . "{$labels['allievo']} {$studente['cognome']} {$studente['nome']}" . '</td>'
            . '<td width="25%">' . "Classe {$cls_st} elementare" . '</td>'
        . '</tr>'
        . '<tr>'
            . '<td width="100%">' . "Docenti : $stringa_docenti" .  '</td>'
        . '</tr>'
        . '</table>', 'B', 1, false, true, 'L');
    $pdf->ln(5);
    $yst = $pdf->GetY();

    $tbl_voti = '<table border="0.1px" cellpadding="3">';
    foreach ($arr_voti as $id_materia => $voto)
    {
        $desc_materia = explode('/', $voto['descrizione'])[0];
        $voto_st = $voto['voto_pagellina'];
        foreach ($voto['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto['voto_pagellina'])
            {
                $voto_st = decode($significato['valore']);
            }
        }

        if (!empty($voto_st)) {
        $tbl_voti .=
            '<tr cellpadding="2"'
            . 'style="line-height: 30px;"'
            . '>'
                . '<td width="60%">'.$desc_materia.'</td>'
                . '<td width="40%" align="center">'.$voto_st.'</td>'
            . "</tr>";
        }
    }
    $tbl_voti .= '</table>';
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell(90, 0, '', '', $tbl_voti, 0, 0, false, true, 'L');

    $pdf->SetY($yst);
    $pdf->SetFont($f, '', $fd);

    $tbl_assenze =
            '<table cellpadding="3">'
                . '<tr>'
                    . '<td align="center"><b>Assenze</b></td>'
                . '</tr>'
                . '<tr>'
                    . '<td>giustificate: '.$assenze_giustificate.'</td>'
                . '</tr>'
                . '<tr>'
                    . '<td>non giustificate: '.$assenze_arbitrarie.'<br>&nbsp;</td>'
                . '</tr>'
            . '</table>';
//    $pdf->writeHTMLCell(0, 0, 105, '', $tbl_assenze, 1, 1, false, true, 'L');

//    $pdf->ln(3);
    $tbl_decisione =
            '<table cellpadding="3">'
                . '<tr>'
                    . '<td align="center"><b>Decisione di fine anno</b></td>'
                . '</tr>'
                . '<tr>'
                    . '<td align="center">'.$esito_stampa.'</td>'
                . '</tr>'
            . '</table>';
    $pdf->writeHTMLCell(0, 0, 105, '', $tbl_decisione, 1, 1, false, true, 'L');

    $pdf->ln(3);
    $tbl_osservazioni =
            '<table cellpadding="3">'
                . '<tr>'
                    . '<td align="center"><b>Osservazioni generali</b></td>'
                . '</tr>'
                . '<tr>'
                    . '<td>'.$osservazioni.'</td>'
                . '</tr>'
            . '</table>';
    $pdf->writeHTMLCell(0, 0, 105, '', $tbl_osservazioni, 1, 1, false, true, 'L');

    $pdf->ln(3);
    $tbl_firme =
            '<table cellpadding="3">'
                . '<tr>'
                    . '<td align="center"><b>FIRME</b></td>'
                . '</tr>'
                . '<tr>'
//                    . '<td style="line-height: 25px; border-bottom-width: 0.1px; font-size: 110%;"></>'
                    . '<td style="line-height: 17px;"></>'
                        . 'I Docenti<br>'
                        . $firma_docenti.'<br>'
                        . 'Data '.$data_att.'<br>'
                        . 'Supervisione Didattica e Pedagogica<br>'
                        . $firma_supervisione .'<br>'
                        . 'Dirigente Scolastico<br>'
                        . $firma_dirigente.'<br>'
                        . 'Data ____________________________________<br><br>'
                        . 'Per l’autorità parentale<br><br>'
                        . '______________________________________________<br><br>______________________________________________<br>'
//                        . 'Data ____________________________________'
                        . '<br>'
                    . '</td>'
                . '</tr>'
            . '</table>';
    $pdf->writeHTMLCell(0, 0, 105, '', $tbl_firme, 1, 1, false, true, 'L');
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'periodo_pagella'   => $periodo_pagella,
    'orientamento'      => $orientamento

];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Valutazioni di fine anno ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Valutazioni di fine anno ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
