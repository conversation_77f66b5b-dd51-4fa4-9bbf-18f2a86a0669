<?php

/*
 * Tipo: Pagella II Quadrimestre
 * Nome: ss_12345_finale_A3_artigianelli_tn_01
 * Richiesta da: Artigianelli TN
 * Data: 2019/02/26
 *
 * Materie particolari:
 * -
 * -
 */
/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('G', 'ss_12345_finale_A3_artigianelli_tn_01', 'Pagella Fine Anno Scuola Secondaria di II Grado', 1, 'Artigianelli TN', 2);

 */

$stampa_personalizzata = 'SI';
$orientamento = 'L';
$formato = 'A3';
$periodo_pagella = "finale";

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">

    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    // -- PARAMETRI
    $anno_scolastico_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");

    $id_classe = $parametri_stampa['id_classe'];
    $data_Day = $parametri_stampa['data_day'];
    $data_Month = $parametri_stampa['data_month'];
    $data_Year = $parametri_stampa['data_year'];
    $periodo_pagella = $parametri_stampa['periodo_pagella'];
    $orientamento = $parametri_stampa['orientamento'];
    $formato = $parametri_stampa['formato'];

    $esito_finale = '';
    $giudizio_globale = '';
    $id_studente = $studente['id_studente'];
    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $elenco_id_materie = $lettera = $note = $arr_voti_finale = [];

    // Estrazione voti
    $voti_pagellina_primo_trimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 7, $studente['id_studente']);
    $voti_pagellina_secondo_trimestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 8, $studente['id_studente']);
    $voti_pagella_fine_anno = estrai_voti_pagellina_studente_multi_classe($id_classe, 9, $studente['id_studente']);

    $materia_to_area = [
        "LINGUA ITALIANA" => "Linguistica",
        "LINGUA COMUNITARIA - INGLESE" => "Linguistica",
        "LINGUA COMUNITARIA - TEDESCO" => "Linguistica",
        "EDUCAZIONE ALLA CITTADINANZA" => "Linguistica",
        "COMUNICAZIONE" => "Linguistica",
        "LABORATORIO SCRITTURA" => "Linguistica",
        "LETTERATURA E ARTE" => "Linguistica",
        "INGLESE" => "Linguistica",
        "LINGUA E LETTERATURA ITALIANA" => "Linguistica",
        "LINGUA INGLESE" => "Linguistica",
        "ITALIANO E ARTE" => "Linguistica",

        "MATEMATICA" => "Matematica e scientifica",
        "SCIENZE INTEGRATE" => "Matematica e scientifica",
        "SCIENZE" => "Matematica e scientifica",
        "SCIENZE APPLICATE" => "Matematica e scientifica",
        "INFORMATICA" => "Matematica e scientifica",
        "CALCOLO PROFESSIONALE E INFORMATICA APPLICATA" => "Matematica e scientifica",
        "ELEMENTI DI MATEMATICA" => "Matematica e scientifica",
        "WEB E NUOVI MEDIA" => "Matematica e scientifica",
        "FISICA" => "Matematica e scientifica",
        "TECNICHE DI PRODUZIONE E DI ORGANIZZAZIONE" => "Matematica e scientifica",

        "STUDI STORICO-ECONOMICI E SOCIALI" => "Storica, giuridica ed economica",
        "STORIA DELL&#039;ARTE E DELLA GRAFICA" => "Storica, giuridica ed economica",
        "STORIA DELL'ARTE E DELLA GRAFICA" => "Storica, giuridica ed economica",
        "SISTEMI ORGANIZZATIVI E LAVORO" => "Storica, giuridica ed economica",
        "DIRITTO ED ECONOMIA IN AZIENDA" => "Storica, giuridica ed economica",
        "STORIA" => "Storica, giuridica ed economica",

        "GRAFICA MULTICANALE" => "Tecnico-professionale",
        "WORKFLOW GRAFICO" => "Tecnico-professionale",
        "ARTIMPRESA" => "Tecnico-professionale",
        "PROGETTAZIONE GRAFICA" => "Tecnico-professionale",
        "EDITING VIDEO" => "Tecnico-professionale",
        "VERBAL DESIGN" => "Tecnico-professionale",
        "ART-IMPRESA" => "Tecnico-professionale",
        "PROJECT WORK" => "Tecnico-professionale",

        "EDUCAZIONE FISICA" => "Educazione fisica",

        "INSEGNAMENTO DELLA RELIGIONE CATTOLICA" => "IRC",

        "ESPERIENZA DI PARTECIPAZIONE ALLO STAGE" => "Formazione in contesto lavorativo",
        "FORMAZIONE IN AZIENDA" => "Formazione in contesto lavorativo",
        "CORSI OPZIONALI" => "Corsi opzionali",

        "CAPACITÀ RELAZIONALE" => "Capacità relazionale",
    ];

    $ordinam_area_st = [
        "Linguistica" , "Matematica e scientifica", "Storica, giuridica ed economica", "Tecnico-professionale", "Educazione fisica", "IRC", "Formazione in contesto lavorativo", "Corsi opzionali"
    ];
    $arr_voti_area = [];

    $monteore_finale = $assenze_finale = 0;
    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $elenco_id_materie[$id_materia] = $id_materia;

        if ($materia['in_media_pagelle'] == 'SI') {
            $monteore_finale += $voti_pagella_fine_anno[$id_materia]['monteore_totale'];
            $assenze_finale += $voti_pagella_fine_anno[$id_materia]['ore_assenza'];
        }

        // Materie in pagella
        if ($materia['in_media_pagelle'] != 'NV'
            && (
               !in_array($materia['tipo_materia'], ['ALTERNATIVA', 'CONDOTTA', 'RELIGIONE', 'SOSTEGNO'])
//               ||
//               ($studente['esonero_religione'] == 0 && $materia['tipo_materia'] == 'RELIGIONE')
                )
            )
        {
            $arr_voti_finale[$id_materia]['primo_trimestre']['descrizione'] = $materia['descrizione'];
            $arr_voti_finale[$id_materia]['primo_trimestre'] = $voti_pagellina_primo_trimestre[$id_materia];
            $arr_voti_finale[$id_materia]['secondo_trimestre']['descrizione'] = $materia['descrizione'];
            $arr_voti_finale[$id_materia]['secondo_trimestre'] = $voti_pagellina_secondo_trimestre[$id_materia];
            $arr_voti_finale[$id_materia]['fine_anno']['descrizione'] = $materia['descrizione'];
            $arr_voti_finale[$id_materia]['fine_anno'] = $voti_pagella_fine_anno[$id_materia];

            $nome_materia_area = $materia_to_area[strtoupper($materia['descrizione'])];
            $arr_voti_area[$nome_materia_area][$id_materia] = $voti_pagellina_primo_trimestre[$id_materia];
        }

        // Giudizio comportamento
        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA') {
            $comportamento['giudizio_primo_trimestre']['voto']['descrizione'] = $materia['descrizione'];
            $comportamento['giudizio_primo_trimestre']['voto'] = $voti_pagellina_primo_trimestre[$id_materia];
            $comportamento['giudizio_secondo_trimestre']['voto']['descrizione'] = $materia['descrizione'];
            $comportamento['giudizio_secondo_trimestre']['voto'] = $voti_pagellina_secondo_trimestre[$id_materia];
            $comportamento['giudizio_fine_anno']['voto']['descrizione'] = $materia['descrizione'];
            $comportamento['giudizio_fine_anno']['voto'] = $voti_pagella_fine_anno[$id_materia];


            foreach ($voti_pagellina_primo_trimestre[$id_materia]['campi_liberi'] as $campo_libero) {
                if ( strtoupper($campo_libero['nome']) == 'GIUDIZIO' )
                {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $comportamento['giudizio_primo_trimestre']['giudizio'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                        $comportamento['giudizio_primo_trimestre']['giudizio'] .= " ";
                    }
                }
            }

            foreach ($voti_pagellina_secondo_trimestre[$id_materia]['campi_liberi'] as $campo_libero) {
                if ( strtoupper($campo_libero['nome']) == 'GIUDIZIO' )
                {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $comportamento['giudizio_secondo_trimestre']['giudizio'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                        $comportamento['giudizio_secondo_trimestre']['giudizio'] .= " ";
                    }
                }
            }

            foreach ($voti_pagella_fine_anno[$id_materia]['campi_liberi'] as $campo_libero) {
                if ( strtoupper($campo_libero['nome']) == 'GIUDIZIO' )
                {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $comportamento['giudizio_fine_anno']['giudizio'] .= estrai_valore_campo_libero_selezionato($campo_libero);
                        $comportamento['giudizio_fine_anno']['giudizio'] .= " ";
                    }
                }

                if (strpos(strtoupper($campo_libero['nome']), 'ESITO') !== false)
                {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $esito_finale = '* ' . $value;
                    }
                }

                if (strpos(strtoupper($campo_libero['nome']), 'GIUDIZIO GLOBALE') !== false )
                {
                    $value = estrai_valore_campo_libero_selezionato($campo_libero);

                    if ($value !== '') {
                        $giudizio_globale .= $value . ' ';
                    }
                }
            }
        }


        // note + lettere
        foreach ($voti_pagellina_primo_trimestre[$id_materia]['campi_liberi'] as $campo_libero)
        {
            if (stripos($campo_libero['nome'], 'LETTERA INFORMATIVA') !== false)
            {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $lettera['1P'] .= $value;
                }
            }

            if (stripos($campo_libero['nome'], 'NOTE') !== false)
            {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $note['1P'] .= $value;
                }
            }
        }
        foreach ($voti_pagellina_secondo_trimestre[$id_materia]['campi_liberi'] as $campo_libero)
        {
            if (stripos($campo_libero['nome'], 'LETTERA INFORMATIVA') !== false)
            {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $lettera['2P'] .= $value;
                }
            }

            if (stripos($campo_libero['nome'], 'NOTE') !== false)
            {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $note['2P'] .= $value;
                }
            }
        }
        foreach ($voti_pagella_fine_anno[$id_materia]['campi_liberi'] as $campo_libero)
        {
//            if (stripos($campo_libero['nome'], 'LETTERA INFORMATIVA') !== false)
//            {
//                $value = estrai_valore_campo_libero_selezionato($campo_libero);
//                if ($value != '') {
//                    $lettera['3P'] .= $value;
//                }
//            }

            if (stripos($campo_libero['nome'], 'NOTE') !== false)
            {
                $value = estrai_valore_campo_libero_selezionato($campo_libero);
                if ($value != '') {
                    $note['3P'] .= $value;
                }
            }
        }

    }

    $mat_risultato = calcola_esito_finale_studente($id_studente, $current_user, false);

    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = $mat_risultato['scrutinato'] ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }

    // estrazione corsi e voti
    $elenco_corsi = estrai_corsi_per_studente($id_studente);
    $data_inizio = estrai_parametri_singoli('DATA_INIZIO_LEZIONI');
    $data_fine = estrai_parametri_singoli('DATA_FINE_LEZIONI');
    $elenco_voti = estrai_voti_studente_materie($id_studente, $data_inizio, $data_fine, $studente['id_classe'], $classi = "TUTTE_CON_CORSI");

    foreach ($elenco_corsi as $corso)
    {
        if (in_array($corso['id_materia_riferimento'], $elenco_id_materie) == true)
        {
            $arr_corsi_finale[$corso['id_materia_riferimento']]['dati_materia'] = estrai_dati_materia($corso['id_materia_riferimento']);
            $arr_corsi_finale[$corso['id_materia_riferimento']]['corsi'][$corso['id_classe']] = $corso;

//            $arr_corsi_finale[$corso['id_materia_riferimento']]['corsi'][$corso['id_classe']]['media_voti'] = calcola_media_voti_studente_singola_materia($id_studente, $corso['id_materia'], $data_inizio, $data_fine_quadrimestre, "100", 0, $corsi_abbinati = "NO", 'valore_pagella'); //$corsi_abbinati = "NO" perchè estraggo già con l'id del corso, non quello della materia al quale il corso è abbinato
            foreach ($elenco_voti[$corso['id_materia']] as $voto)
            {
                if (isset($voto['id_materia']))
                {
                    $arr_corsi_finale[$corso['id_materia_riferimento']]['corsi'][$corso['id_classe']]['voti'][] = $voto;
                }
            }
        }
        else
        {
            if (!empty($corso['materie_riferimento_aggiuntive']))
            {
                foreach ($corso['materie_riferimento_aggiuntive'] as $id_materia)
                {
                    if (in_array($id_materia, $elenco_id_materie) == true)
                    {
                        $arr_corsi_finale[$id_materia]['dati_materia'] = estrai_dati_materia($id_materia);
                        $arr_corsi_finale[$id_materia]['corsi'][$corso['id_classe']] = $corso;
                    }
                }
            }
        }
    }


    // Dizionario temporaneo
    $labels = [
        "firma_dirigente"       => "Il Dirigente Scolastico",


        "p1_intestazione_1"     => "Istituto di Istruzione ",
        "p1_intestazione_2"     => "Secondaria Superiore Artigianelli",
        "p1_intestazione_3"     => "Trento, Settore Industria e Artigianato",
        "p1_titolo_1"           => "DOCUMENTO DI VALUTAZIONE",
        "p1_cognome"            => "Cognome",
        "p1_nome"               => "Nome",
        "p1_nascita_luogo"      => "Nat||min_oa|| a",
        "p1_nascita_data"       => "Il",
        "p1_classe"             => "Classe",
        "p1_sezione"            => "Sezione",
        "p1_corso"              => "Corso",
        "p1_monteore"           => "Monte ore annuo:",

        "p23_1_trimestre"       => "VALUTAZIONE I° TRIMESTRE",
        "p23_2_trimestre"       => "VALUTAZIONE II° TRIMESTRE",
        "p23_3_trimestre"       => "VALUTAZIONE III° TRIMESTRE",
        "p23_dal"               => "Dal",
        "p23_al"                => "al",
        "p23_materia"           => "MATERIA",
        "p23_valutazione"       => "VALUTAZIONE",
        "p23_ore_assenza"       => "Ore assenza",
        "p23_totale"            => "TOTALE",

        "p4_giudizio_globale"   => "GIUDIZIO GLOBALE",
        "p4_esito_finale"       => "Esito Finale",
        "p4_trento"             => "Trento",
        "p4_firma_genitore"     => "Firma del genitore o di chi ne fa le veci",

        "p5_titolo_1"           => "DOCUMENTO DI VALUTAZIONE PER L'ANNO SCOLASTICO",
        "p5_titolo_2"           => "ALLEGATO ALLA VALUTAZIONE PER L'ANNO SCOLASTICO",
        "p5_titolo_corso"       => "CORSI",
        "p5_titolo_voto"        => "VALUTAZIONI",
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k => $label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }

    if ($esito_finale == '') {
        $esito_finale = $studente['esito_corrente_calcolato'];
    }
    //}}} </editor-fold>

    $pdf->AddPage($orientamento, $formato);
    $pdf->SetAutoPageBreak("off", 1);

    $default_margins = $pdf->getMargins();
    $default_margins['top'] = 10; // se non specificato e assegnato più avanti, a un certo punto per qualche motivo si incrementa e sfasa tutto

    //{{{ <editor-fold defaultstate="collapsed" desc="pag4">
    $pdf->SetRightMargin(215);
    $pdf->SetFont('helvetica', '', 11);
    $pdf->ln(10);
    $tbl = '
        <table cellspacing="0" cellpadding="3" border="0">
            <tr>
                <td width="100%" align="left" style="line-height: 60pt; font-size: 120%;"><b>'
                    . $labels['p4_giudizio_globale'] .
                '</b></td>
            </tr>
            <tr>
                <td width="100%" style=" height: 170pt;">'
                    . $giudizio_globale .
                '</td>
            </tr>
            <tr>
                <td width="100%" style=" font-size: 75%;">'
                    . $labels['p4_esito_finale'] .
                '</td>
            </tr>
            <tr>
                <td width="100%" style=" line-height: 30pt;">'
//                    . $studente['esito_corrente_calcolato'] .
                    . $esito_finale .
                '</td>
            </tr>
        </table>
        ';
    $pdf->writeHTML($tbl, true, false, false, false, '');

    $pdf->ln(20);
    $mese = traduci_mese_in_lettere($data_Month, 'esteso');
    $text = $studente['descrizione_comuni'] . ", " . $data_Day . " " . $mese . " " . $data_Year;
    $pdf->write(0, $text);

    $pdf->ln(40);
    $logo_f = '';
    if (file_exists('immagini_scuola/logo_f.png')) {
        $logo_f = '<img src="immagini_scuola/logo_f.png" height="40">';
    }
    $tbl = '
        <table cellspacing="0" cellpadding="3" border="0">
            <tr>
                <td width="25%"><b>'
                    . $labels['firma_dirigente'] .
                '</b></td>
                <td width="25%"></td>
                <td width="50%" align="center">'
                    . $labels['p4_firma_genitore'] .
                '</td>
            </tr>
            <tr>
                <td width="25%">'
                    . $studente['nome_dirigente'] .
                '</td>
                <td width="25%">'.$logo_f.'</td>
                <td width="50%" align="center" style="line-height: 40pt;">___________________________________</td>
            </tr>
        </table>
        ';

    $pdf->writeHTML($tbl, true, false, false, false, '');

    $y_corrente = $pdf->GetY();
    $pdf->Image('immagini_scuola/firma_dirigente.jpg', 10, $y_corrente - 15, 70, 16, '', '', '', false, 300, '', false, false, 0, true);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pag1">
    $x_base = 235;
    $y_base = 0;
    $x_rel = $x_base + 30;

	$y_rel = $y_base + 10;
    $pdf->SetXY($x_base, $y_rel);

    $pdf->SetLeftMargin($x_base);
    $pdf->SetRightMargin($default_margins['right']);
    $pdf->SetTopMargin($default_margins['top']);

    //loghi
    $pdf->Image('immagini_scuola/logo_trento_2.jpg', '', '', 0, 30, '', '', '', false, 300, 'R');
    $pdf->Image('immagini_scuola/logo.jpg', '', '', 0, 35, '', '', '', false, 300, 'L');
    $pdf->SetFont('helvetica', 'B', 8);
    $pdf->writeHTMLCell(0, 0, $x_base, 45,"Istituto Pavoniano Artigianelli<br>Piazza Fiera 4, 38122 Trento", 0, 0, false, true, 'L');
    $pdf->writeHTMLCell(0, 0, $x_base, 45,"Servizio formazione professionale<br>Formazione terziaria e funzioni di sistema", 0, 0, false, true, 'R');


    $pdf->SetY(55);
//    //titolo 1
//    $pdf->SetFont('helvetica', 'B', 12);
//    $pdf->Write('', $labels['p1_intestazione_1'], '', false, 'L', true);
//    $pdf->Write('', $labels['p1_intestazione_2'], '', false, 'L', true);
//    $pdf->SetFont('helvetica', '', 11);
//    $pdf->ln(2);
//    $pdf->Write('', $labels['p1_intestazione_3'], '', false, 'L', true);

    $pdf->ln(10);

    //titolo 2

    $pdf->ln(25);

    //titolo 3
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Write('', $labels['p1_titolo_1'], '', false, 'L', true);
    $pdf->Write('', "PER L'ANNO SCOLASTICO " . $anno_scolastico_attuale, '', false, 'L', true);

    $pdf->ln(25);
    $pdf->SetFont('helvetica', '', 10);
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .= ' (' . $stato['descrizione'] . ')';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'] . " (" . $studente['provincia_nascita_da_comune'] . ")";
    }

    $freq_txt = '';
    switch ($validazione_anno)
    {
        case 'SI':
            $testo_validazione = "Lo studente ha frequentato almeno il 75% dell'orario annuale.<br>E' ammesso allo scrutinio per la valutazione finale.";
            $freq_txt = '75%';
            break;
        case 'NO':
            $testo_validazione = "Lo studente non ha frequentato almeno il 75% dell'orario annuale.<br>Non è ammesso allo scrutinio per la valutazione finale.";
            $freq_txt = 'minore di 75%';
            break;
        case 'DEROGA':
            $testo_validazione = "Lo studente non ha frequentato almeno il 75% dell'orario annuale.<br>E' ammesso allo scrutinio per la valutazione finale con deroga.";
            $freq_txt = 'minore di 75%';
            break;
        default:
            break;
    }
    if ($studente['classe'] == 4) {
        if (stripos('groaz', $studente['cognome']) === FALSE) {
            $freq_txt = '75%';
        }
    }

    $tbl = '
        <table cellspacing="0" cellpadding="1.5" border="0">
            <tr>
                <td colspan="3" width="50%" style="border-top-width: 0.1px;  font-size: 75%;"> ' . $labels['p1_cognome'] . '</td>
                <td width="50%" style="border-top-width: 0.1px;   font-size: 75%;"> ' . $labels['p1_nome'] . '</td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style=" line-height: 40pt;"><b> ' . $studente['cognome'] . '</b></td>
                <td width="50%" style="  line-height: 40pt;"><b> ' . $studente['nome'] . '</b></td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style="border-top-width: 0.1px;  font-size: 75%;"> ' . $labels['p1_nascita_luogo'] . '</td>
                <td width="50%" style="border-top-width: 0.1px;   font-size: 75%;"> ' . $labels['p1_nascita_data'] . '</td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style=" line-height: 40pt;"><b> ' . $luogo_nascita . '</b></td>
                <td width="50%" style="  line-height: 40pt;"><b> ' . $studente[71] . '</b></td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style="border-top-width: 0.1px;  font-size: 75%;"> ' . 'Percorso di studi' . '</td>
                <td width="50%" style="border-top-width: 0.1px;  font-size: 75%;">'  . 'Anno di frequenza' . '</td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style=" line-height: 40pt;"><b> ' . $studente['descrizione_indirizzi'] . '</b></td>
                <td width="50%" style=" line-height: 40pt;"><b> ' . $studente['classe'].' '.$studente['sezione'] . '</b></td>
            </tr>
            <tr>
                <td colspan="3" width="25%" style="border-top-width: 0.1px;  font-size: 75%;"> ' . 'Monte ore annuo' . '</td>
                <td width="25%" style="border-top-width: 0.1px;  font-size: 75%;">'  . 'Percentuale di frequenza minima' . '</td>
                <td width="50%" style="border-top-width: 0.1px;  font-size: 75%;">'  . 'Dichiarazione di ammissione/non ammissione' . '</td>
            </tr>
            <tr>
                <td width="25%" style="border-bottom-width: 0.1px; line-height: 40pt;"><b> ' . '1066' . '</b></td>
                <td width="25%" style="border-bottom-width: 0.1px; line-height: 40pt;"><b> ' .$freq_txt . '</b></td>'.
//                '<td width="40%" style="border-bottom-width: 0.1px; line-height: 40pt;"><b> ' . $studente['esito_corrente_calcolato'] . '</b></td>' .
                '<td width="50%" style="border-bottom-width: 0.1px; line-height: 40pt;"><b> ' . $esito_finale . '</b></td>' .
            '</tr>.'.
//            '<tr>
//                <td colspan="2" align="left" width="34%" style="border-top-width: 0.1px;  line-height: 50pt; "> ' . $labels['p1_monteore'] . ' <b>1066</b></td>
//                <td colspa="2" align="center" width="66%" style=" border-top-width: 0.1px;  "><div style="line-height: 11pt;"> </div>' . $testo_validazione . '</td>
//            </tr>'.
        '</table>
        ';
    $pdf->writeHTML($tbl, true, false, false, false, '');

    $pdf->ln(25);
    $pdf->SetFont('helvetica', '', 11);
    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td width="66%">
                </td>
                <td align="center" width="34%">
                    <b>' . $labels['firma_dirigente'] . '</b>
                </td>
            </tr>
            <tr>
                <td width="66%"></td>
                <td align="center" width="34%">
                    ' . $studente['nome_dirigente'] . '
                </td>
            </tr>
        </table>
        ';
//    $pdf->writeHTML($tbl, true, false, false, false, '');
//
//    $y_corrente = $pdf->GetY();
//    $pdf->Image('immagini_scuola/firma_dirigente.jpg', 365, $y_corrente - 6, 70, 16, '', '', '', false, 300, '', false, false, 0, true);
    //}}} </editor-fold>

    // ######   PAGINA 2 - 3   ######
    $pdf->AddPage($orientamento, $formato);
    $pdf->SetLeftMargin($default_margins['left']);
    $pdf->SetRightMargin($default_margins['right']);

    $pdf->ln(10);

    $y_corrente = $pdf->GetY();

    $inizio_anno = estrai_parametri_singoli('DATA_INIZIO_LEZIONI');
    $inizio_secondo_trimestre = estrai_parametri_singoli('DATA_INIZIO_SECONDO_QUADRIMESTRE');
    $inizio_terzo_trimestre = estrai_parametri_singoli('DATA_INIZIO_TERZO_TRIMESTRE');
    $fine_anno = estrai_parametri_singoli('DATA_FINE_LEZIONI');

    $pdf->SetFont('helvetica', '', 8);

    //{{{ <editor-fold defaultstate="collapsed" desc="Intestazioni">
    // Intestazioni
    $tbl = '
    <table cellspacing="0" cellpadding="4" border="0">
        <tr>
            <td align="left" colspan="4" width="30%" style=""><br><br><b>'
                . $labels['p23_1_trimestre'] .
            '</b><br>'
                . $labels['p23_dal'] . ' ' . date("d/m/Y", $inizio_anno) . ' ' . $labels['p23_al'] . ' ' . date("d/m/Y", ($inizio_secondo_trimestre - (60*60*24))) .
            '<br></td>
            <td width="1%"></td>
            <td align="left" colspan="4" width="30%" style=""><br><br><b>'
                . $labels['p23_2_trimestre'] .
            '</b><br>'
                . $labels['p23_dal'] . ' ' . date("d/m/Y", $inizio_secondo_trimestre) . ' ' . $labels['p23_al'] . ' ' . date("d/m/Y", ($inizio_terzo_trimestre - (60*60*24))) .
            '<br></td>
            <td width="1%"></td>
            <td align="left" colspan="4" width="30%" style=""><br><br><b>'
                . $labels['p23_3_trimestre'] .
            '</b><br>'
                . $labels['p23_dal'] . ' ' . date("d/m/Y", $inizio_terzo_trimestre) . ' ' . $labels['p23_al'] . ' ' . date("d/m/Y", $fine_anno) .
            '<br></td>
            <td width="1%"></td>
            <td align="center" rowspan="2" style=""><br><br><br><br><b>'
                . $labels['p23_totale'] .
            '</b><br>'
                . $labels['p23_ore_assenza'] .
            '</td>
        </tr>
        <tr>
            <td align="center" width="10%" style="border-bottom-width: 0.1px;"><b>' . 'AREE DI APPRENDIMENTO' . '</b></td>
            <td align="center" width="14%" style="  "><b>' . $labels['p23_materia'] . '</b></td>
            <td align="center" width="6%" style="  "><b>' . $labels['p23_valutazione'] . '</b></td>
            <td width="1%"></td>
            <td align="center" width="10%" style="border-bottom-width: 0.1px;"><b>' . 'AREE DI APPRENDIMENTO' . '</b></td>
            <td align="center" width="14%" style="  "><b>' . $labels['p23_materia'] . '</b></td>
            <td align="center" width="6%" style="  "><b>' . $labels['p23_valutazione'] . '</b></td>
            <td width="1%"></td>
            <td align="center" width="10%" style="border-bottom-width: 0.1px;"><b>' . 'AREE DI APPRENDIMENTO' . '</b></td>
            <td align="center" width="14%" style="  "><b>' . $labels['p23_materia'] . '</b></td>
            <td align="center" width="6%" style="  "><b>' . $labels['p23_valutazione'] . '</b></td>
            <td width="1%"></td>
        </tr>';

    //}}} </editor-fold>

//        foreach ($ordinam_area_st as $nome_area_ord)
//        {
//            $arr_voti = $arr_voti_area[$nome_area_ord];
//            if (!empty($arr_voti)) {
//                //intestazione
//                $area_cont_materie = count($arr_voti);
//                $tbl .= '
//                <tr>
//                    <td rowspan="'.$area_cont_materie.'" style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>';
//
//                //voti
//                end($arr_voti);
//                $last_key = key($arr_voti);
//                reset($arr_voti);
//                $first_key = key($arr_voti);
//                foreach ($arr_voti as $id_voto => $voto)
//                {
//                    $cella_voto = '';
//                    $cella_descrizione = '';
//                    $voto_tradotto = "";
//
//                    foreach ($voto['significati_voto'] as $significato)
//                    {
//                        if ($significato['voto'] == $voto['voto_pagellina'])
//                        {
//                            $voto_tradotto = $significato['valore_pagella'];
//                        }
//                    }
//                    $voto_tradotto = $voto['voto_pagellina'];
//
//                    $cella_voto = decode(strtoupper($voto_tradotto));
//            //        $cella_voto = $voto['voto_pagellina'];
//                    $cella_descrizione = ucwords(strtolower($voto['descrizione']));
//
//                    if ($voto['ore_assenza'] > 0)
//                    {
//                        $ore_assenza = '';
//                        $secondi_assenza = $voto['ore_assenza'] * 60;
//                        $hrs = intval(intval($secondi_assenza) / 3600);
//                        $mns = intval(($secondi_assenza / 60) % 60);
//                        if ($mns >= 0 && $mns <=9)
//                        {
//                            $mns = '0' . $mns;
//                        }
//
//                            $ore_assenza .= $hrs . '.' . $mns;
//                    }
//                    else
//                    {
//                        $ore_assenza = '0:00';
//                    }
//
//                    if ($first_key != $id_voto) {
//                        $tbl .= '<tr>';
//                    }
//                    $tbl .= '
//                        <td style="border-bottom-width: 0.1px;">' . $cella_descrizione . '</td>
//                        <td style="border-bottom-width: 0.1px;">' . $cella_voto . '</td>
//                        <td style="border-bottom-width: 0.1px;">' . $ore_assenza . '</td>
//                    ';
//                    if ($last_key != $id_voto) {
//                        $tbl .= '</tr>';
//                    }
//                }
//                $tbl .= '</tr>';
//            }
//        }


    foreach ($ordinam_area_st as $nome_area_ord)
    {
        $area_materie = $arr_voti_area[$nome_area_ord];
        if (!empty($area_materie)) {
            //intestazione
            $area_cont_materie = count($area_materie);
//            $tbl .= '
//            <tr>
//                <td rowspan="'.$area_cont_materie.'" style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>';

            //voti
            end($area_materie);
            $last_key = key($area_materie);
            reset($area_materie);
            $first_key = key($area_materie);
            foreach ($area_materie as $id_materia_area => $val_materia)
            {
//                foreach ($arr_voti_finale[$id_materia_area] as $materia)
//                {
                    $materia = $arr_voti_finale[$id_materia_area];
                    $totale_assenze = 0;
                    $tbl .= '
                    <tr>';

                    //{{{ <editor-fold defaultstate="collapsed" desc="1° Trimestre">
                    // 1° Trimestre
                    if ($materia['primo_trimestre']['ore_assenza'] > 0)
                    {
                        $ore_assenza = '';
                        $secondi_assenza = $materia['primo_trimestre']['ore_assenza'] * 60;
                        $hrs = intval(intval($secondi_assenza) / 3600);
                        $mns = intval(($secondi_assenza / 60) % 60);
                        if ($mns >= 0 && $mns <=9)
                        {
                            $mns = '0' . $mns;
                        }

                        $ore_assenza .= $hrs . ':' . $mns;
                        $totale_assenze += $materia['primo_trimestre']['ore_assenza'];
                    }
                    else
                    {
                        $ore_assenza = '0:00';
                    }

                    if ($first_key == $id_materia_area) {
                        $tbl .= '
                            <td rowspan="'.$area_cont_materie.'" style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>';
                    }
//    <td style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>
                    $tbl .= '
                        <td align="left"  style="border-top-width: 0.1px; ">' . $materia['primo_trimestre']['descrizione'] . '</td>
                        <td align="left"  style="border-top-width: 0.1px; ">' . $materia['primo_trimestre']['voto_pagellina'] . '</td>
                    ';
                    //}}} </editor-fold>

                    $tbl .= '<td width="1%"></td>';

                    //{{{ <editor-fold defaultstate="collapsed" desc="2° Trimestre">
                    // 2° Trimestre
                    if ($materia['secondo_trimestre']['ore_assenza'] > 0)
                    {
                        $ore_assenza = '';
                        $secondi_assenza = $materia['secondo_trimestre']['ore_assenza'] * 60;
                        $hrs = intval(intval($secondi_assenza) / 3600);
                        $mns = intval(($secondi_assenza / 60) % 60);
                        if ($mns >= 0 && $mns <=9)
                        {
                            $mns = '0' . $mns;
                        }

                        $ore_assenza .= $hrs . ':' . $mns;
                        $totale_assenze += $materia['secondo_trimestre']['ore_assenza'];
                    }
                    else
                    {
                        $ore_assenza = '0:00';
                    }


                    if ($first_key == $id_materia_area) {
                        $tbl .= '
                            <td rowspan="'.$area_cont_materie.'" style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>';
                    }
//    <td style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>
                    $tbl .= '
                        <td align="left"  style="border-top-width: 0.1px; ">' . $materia['secondo_trimestre']['descrizione'] . '</td>
                        <td align="left"  style="border-top-width: 0.1px; ">' . $materia['secondo_trimestre']['voto_pagellina'] . '</td>
                    ';
                    //}}} </editor-fold>

                    $tbl .= '<td width="1%"></td>';

                    //{{{ <editor-fold defaultstate="collapsed" desc="Fine anno">
                    // Fine anno
                    if ($materia['fine_anno']['ore_assenza'] > 0)
                    {
                        $ore_assenza = '';
                        $secondi_assenza = $materia['fine_anno']['ore_assenza'] * 60;
                        $hrs = intval(intval($secondi_assenza) / 3600);
                        $mns = intval(($secondi_assenza / 60) % 60);
                        if ($mns >= 0 && $mns <=9)
                        {
                            $mns = '0' . $mns;
                        }

                        $ore_assenza .= $hrs . ':' . $mns;
                        $totale_assenze += $materia['fine_anno']['ore_assenza'];
                    }
                    else
                    {
                        $ore_assenza = '0:00';
                    }

                    if ($first_key == $id_materia_area) {
                        $tbl .= '
                            <td rowspan="'.$area_cont_materie.'" style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>';
                    }
//    <td style="border-bottom-width: 0.1px;"><b>' . $nome_area_ord . '</b></td>
                    $tbl .= '
                        <td align="left"  style="border-top-width: 0.1px; ">' . $materia['fine_anno']['descrizione'] . '</td>
                        <td align="left"  style="border-top-width: 0.1px; ">' . $materia['fine_anno']['voto_pagellina'] . '</td>
                    ';
                    //}}} </editor-fold>

                    $tbl .= '<td width="1%"></td>';

                    //{{{ <editor-fold defaultstate="collapsed" desc="TOTALE Ore assenza">
                    // TOTALE Ore assenza
                    $totale_assenze = $materia['fine_anno']['ore_assenza'];
                    if ($totale_assenze > 0)
                    {
                        $ore_assenza = '';
                        $secondi_assenza = $totale_assenze * 60;
                        $hrs = intval(intval($secondi_assenza) / 3600);
                        $mns = intval(($secondi_assenza / 60) % 60);
                        if ($mns >= 0 && $mns <=9)
                        {
                            $mns = '0' . $mns;
                        }

                        $ore_assenza .= $hrs . ':' . $mns;
                    }
                    else
                    {
                        $ore_assenza = '0:00';
                    }

                    $tbl .= '<td align="left" style="border-top-width: 0.1px;  ">'
                                . $ore_assenza .
                            '</td>';
                    //}}} </editor-fold>

                    $tbl .= '
                    </tr>';
//                }
            }
        }
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="Comportamento">
    // Comportamento
    $tbl .= '
        <tr>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . $comportamento['giudizio_primo_trimestre']['voto']['descrizione'] . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . $comportamento['giudizio_primo_trimestre']['voto']['voto_pagellina'] . '</td>
            <td width="1%"></td>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . $comportamento['giudizio_secondo_trimestre']['voto']['descrizione'] . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . $comportamento['giudizio_secondo_trimestre']['voto']['voto_pagellina'] . '</td>
            <td width="1%"></td>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . $comportamento['giudizio_fine_anno']['voto']['descrizione'] . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . $comportamento['giudizio_fine_anno']['voto']['voto_pagellina'] . '</td>
            <td width="1%"></td>
            <td style="border-top-width: 0.1px;   "></td>
        </tr>
        <tr>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $comportamento['giudizio_primo_trimestre']['giudizio'] . '</td>
            <td width="1%"></td>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $comportamento['giudizio_secondo_trimestre']['giudizio'] . '</td>
            <td width="1%"></td>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $comportamento['giudizio_fine_anno']['giudizio'] . '</td>
            <td width="1%"></td>
        </tr>';
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="note e lettere">
    if (!empty($lettera))
    {
    $tbl .= '
        <tr>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . 'Lettera' . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . '' . '</td>
            <td width="1%"></td>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . 'Lettera' . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . '' . '</td>
            <td width="1%"></td>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . 'Lettera' . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . '' . '</td>
            <td width="1%"></td>
        </tr>
        <tr>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $lettera['1P'] . '</td>
            <td width="1%"></td>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $lettera['2P'] . '</td>
            <td width="1%"></td>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . '' . '</td>
            <td width="1%"></td>
        </tr>';
    }
    if (!empty($note))
    {
    $tbl .= '
        <tr>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . 'Note' . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . '' . '</td>
            <td width="1%"></td>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . 'Note' . '</td>
            <td align="left" colspan="2" width="16%" style="border-top-width: 0.1px;   ">' . '' . '</td>
            <td width="1%"></td>
            <td align="left" width="14%" style="border-top-width: 0.1px;  ">' . 'Note' . '</td>
            <td align="left" colspan="2" width="16%">' . '' . '</td>
            <td width="1%"></td>
        </tr>
        <tr>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $note['1P'] . '</td>
            <td width="1%"></td>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $note['2P'] . '</td>
            <td width="1%"></td>
            <td align="left" width="30%" style="border-bottom-width: 0.1px;  ">' . $note['3P'] . '</td>
            <td width="1%"></td>
        </tr>';
    }
    //}}} </editor-fold>

    $tbl .= '</table>';
    $pdf->writeHTML($tbl, false, false, false, false, '');

    if (false)
    {
    // ######   PAGINA 5 - Riepilogo corsi   ######
    $pdf->AddPage('P', 'A4');

    //{{{ <editor-fold defaultstate="collapsed" desc="Inserimento loghi e Intestazione">
    //loghi
    $pdf->Image('immagini_scuola/logo_trento_2.jpg', '', '', 0, 30, '', '', '', false, 300, 'L');
    $pdf->Image('immagini_scuola/logo.jpg', '', '', 0, 35, '', '', '', false, 300, 'R');

    //titolo 1
    $pdf->SetY(48);
    $pdf->SetFont('helvetica', 'B', 14);
    $pdf->Write('', $labels['p1_intestazione_1'], '', false, 'C', true);
    $pdf->SetFont('helvetica', '', 14);
    $pdf->Write('', $labels['p1_intestazione_2'], '', false, 'C', true);

    //titolo 2
    $pdf->ln(10);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Write('', $labels['p5_titolo_2'] . ' ' . $anno_scolastico_attuale, '', false, 'C', true);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Dati dello studente">
    $pdf->ln(4);
    $pdf->SetFont('helvetica', '', 12);
    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $luogo_nascita = $studente['citta_nascita_straniera'];
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $luogo_nascita .= ' (' . $stato['descrizione'] . ')';
        }
    }
    else
    {
        $luogo_nascita = $studente['descrizione_nascita'] . " (" . $studente['provincia_nascita_da_comune'] . ")";
    }

    $tbl = '
        <table cellspacing="0" cellpadding="1.5" border="0">
            <tr>
                <td colspan="3" width="50%" style="border-top-width: 0.1px;  font-size: 60%;">' . $labels['p1_cognome'] . '</td>
                <td width="50%" style="border-top-width: 0.1px;  font-size: 60%;">' . $labels['p1_nome'] . '</td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style=""><b>' . $studente['cognome'] . '</b></td>
                <td width="50%" style=""><b>' . $studente['nome'] . '</b></td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style="border-top-width: 0.1px;  font-size: 60%;">' . $labels['p1_nascita_luogo'] . '</td>
                <td width="50%" style="border-top-width: 0.1px;  font-size: 60%;">' . $labels['p1_nascita_data'] . '</td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style=""><b>' . $luogo_nascita . '</b></td>
                <td width="50%" style=""><b>' . $studente[71] . '</b></td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style="border-top-width: 0.1px;  font-size: 60%;">' . 'Percorso di studi' . '</td>
                <td  width="50%" style="border-top-width: 0.1px; font-size: 60%;">' . 'Anno di frequenza' . '</td>
            </tr>
            <tr>
                <td colspan="3" width="50%" style="border-bottom-width: 0.1px; "><b>' . $studente['descrizione_indirizzi'] . '</b></td>
                <td width="50%" style="border-bottom-width: 0.1px;"><b>' . $studente['classe'].$studente['sezione'] . '</b></td>
            </tr>
        </table>
        ';

    $pdf->writeHTML($tbl, true, false, false, false, '');
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Tabella Corsi">
    $pdf->ln(1);
    $pdf->SetFont('helvetica', '', 9);

//    <td align="left" width="80%" style="font-size: 130%; background-color: #effdff;" colspan="3"><i>' . $labels['p2_titolo_corso'] . '</i></td>
//    <td align="left" width="20%" style="font-size: 130%; background-color: #effdff;"><i>' . $labels['p2_titolo_voto'] . '</i></td>

    $tbl = '
        <table cellpadding="4" cellspacing="0" border="0.1">
            <thead>
                <tr>
                    <td align="left" width="80%" style="font-size: 130%;" colspan="3">' . $labels['p5_titolo_corso'] . '</td>
                    <td align="left" width="20%" style="font-size: 130%;">' . $labels['p5_titolo_voto'] . '</td>
                </tr>
            </thead>
        ';

    foreach ($arr_corsi_finale as $id_materia => $materia)
    {
        if (count($materia['corsi'] > 0))
        {
            $primo = true;
            $numero_corsi = count($materia['corsi']);
            $stringa_voti = '';

            foreach ($materia['corsi'] as $corso)
            {
//                $media_voti = (isset($corso['media_voti']['totale'])) ? $corso['media_voti']['totale'] : '';
                if (isset($corso['voti']))
                {
                    $arr_voti = [];
                    foreach ($corso['voti'] as $voto)
                    {
                        $arr_voti[] = $voto['voto'];
                    }
                    $stringa_voti = implode(", ", $arr_voti);
                }
                else
                {
                    $stringa_voti = '';
                }

                $tbl .= '
            <tr>';

                if ($primo)
                {
                    $tbl .= '
                <td align="center" width="20%" style="font-size: 115%;" rowspan="' . $numero_corsi . '">
                    <b>' . $materia['dati_materia']['nome_materia_breve'] . '</b>
                </td>
                    ';
                }

                $tbl .= '
                <td align="center" width="18%">
                    ' . $corso['attivita'] . '
                </td>
                <td align="left" width="42%">
                    ' . $corso['descrizione'] . '
                </td>
                <td align="center" width="20%">
                    ' . $stringa_voti . '
                </td>
            </tr>
                    ';

                $primo = false;
            }
        }
    }

    $tbl .= '</table>';

    $pdf->writeHTML($tbl, false, false, false, false, '');
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Firme">
    $pdf->ln(4);
    $pdf->SetFont('helvetica', '', 11);
    $tbl = '
        <table cellspacing="0" cellpadding="2" border="0">
            <tr>
                <td width="66%">
                </td>
                <td align="center" width="34%">
                    <b>' . $labels['firma_dirigente'] . '</b>
                </td>
            </tr>
            <tr>
                <td width="66%"></td>
                <td align="center" width="34%">
                    ' . $studente['nome_dirigente'] . '
                </td>
            </tr>
        </table>
        ';
    $pdf->writeHTML($tbl, true, false, false, false, '');

    $y_corrente = $pdf->GetY();
    $pdf->Image('immagini_scuola/firma_dirigente.jpg', 150, $y_corrente - 6, 70, 16, '', '', '', false, 300, '', false, false, 0, true);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="Luogo e Data">
    $pdf->ln(1);
    $pdf->SetFont('helvetica', '', 9);
    $mese = traduci_mese_in_lettere($data_Month, 'esteso');
    $text = $studente['descrizione_comuni'] . ", " . $data_Day . " " . $mese . " " . $data_Year;
    $pdf->write(0, $text);
    //}}} </editor-fold>
    }
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'periodo_pagella'   => $periodo_pagella,
    'orientamento'      => $orientamento,
    'formato'           => $formato,

];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
