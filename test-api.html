<html>
<head>
	<link rel="stylesheet" type="text/css" href="css/jquery-ui-1.8.16.custom.css" />
	<link rel="stylesheet" type="text/css" href="css/style.css">
	<script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script>
	<script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
</head>
<body>
	<script>
		function test_login() {
			var query='uname='+$('#user').val()+'&upass='+$('#pwd').val();
			$.ajax({
				type: 'GET',
				url: "api.php/login",
				data: query,
				cache: false,
				complete: function(response) {
					$('#token').html(response.responseText);
				}
			});
		}
		function test_call() {
			var type=$('#type').val();
			var url=$('#url').val();
			var query=$('#query').val();
			$.ajax({
				type: type,
				url: "api.php"+url,
				data: query,
				cache: false,
				headers: {'Authorization':$('#token').html()},
				complete: function(response) {
					console.log(response);
					$('#display_msg').html(response.responseText);
				}
			});
		}
	</script>
<input type='text' id='user' value='pippo'>
<input type='text' id='pwd' value='pluto123'>
<input type='button' value='LOGIN' onclick='test_login();'>
<div id='token'></div>


<br>
<input type='text' id='type' value='GET'>
<input type='text' id='url' value=''>
<input type='text' id='query' value=''>
<input type='button' value='TEST' onclick='test_call();'>
<br>
<div id='display_msg'></div>
</body>
</html>
