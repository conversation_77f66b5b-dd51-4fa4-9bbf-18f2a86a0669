<!DOCTYPE html>
<html lang="it">
    <head>
        <title>{$titolo} - {$nome}</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="google" value="notranslate">
        <link rel="stylesheet" href="css/style.css">
        <link rel="shortcut icon" type="image/x-icon" href="icone/mastercom.ico">
        <link rel="stylesheet" type="text/css" href="css/jquery-ui-1.8.16.custom.css" />
        <link rel="stylesheet" href="css/style.css">
        {* <script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script> *}
		<script type="text/javascript" src="libs/jquery/jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
        <script type="text/javascript" src="javascript/messenger.js?v={$js_version}"></script>
        <script type="text/javascript" src="/mastertek-api/ckeditor-mastercom/ckeditor.js"></script>
        <script type="text/javascript" src="javascript/anylink.js?v={$js_version}"></script>

		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.base.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.darkblue.css" type="text/css" />
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">

		<script type="text/javascript" src="libs/jqwidgets/jqx-all.js"></script>
		<script type="text/javascript" src="libs/jqwidgets/globalization/globalize.culture.it-IT.js"></script>

		<!-- {*{{{*} Questo pezzo di javascript ha il compito di monitorare il tempo di caricamento complessivo di una pagina. In caso di login, invia l'informazione ad un webservice ajax per essere loggato nel db ed effettuare statistichee monitoraggio omogeneo del tempo di esecuzione delle pagine. -->
	   	<script type="text/javascript">
			var login_iniziale 	= '{$login_iniziale}';
			var current_user 	= '{$current_user}';
			var current_key 	= '{$current_key}';
			var tipo_utente 	= '{$form_stato}';

			{literal}
			window.addEventListener("load", loadTime, false);
			function loadTime() {
				var now = new Date().getTime();
				var tempo_caricamento = now - performance.timing.navigationStart;
				if (window.console ) console.log(tempo_caricamento);
				if(login_iniziale == 'SI') {
					var query = {
							current_user:  current_user,
							current_key: current_key,
							form_tipo_utente: tipo_utente,
							form_cosa:  tempo_caricamento,
							form_tipo: 'LOGIN LOAD TIME',
							form_azione: 'registra_log_storico'
						};
					$.ajax({ type: "POST",
						 url: "ajat_manager.php",
						 data: query,
						 cache: false,
						 success: function(response) {
						 },
						 complete: function(response) {
						 },
					});
				}
			}
			{/literal}
		</script>
		<!-- {*}}}*} -->
        <script type="text/javascript" src="javascript/include_header_professore.js?v={$js_version}"></script>

    <script type="text/javascript">
        window.name = 'mastercom_main_window';
    </script>
</head>
<body>
<input type="hidden" name="current_user"id="current_user" 	value="{$current_user}">
<input type="hidden" name="current_key" id="current_key" 	value="{$current_key}">
<input type="hidden" name="db_key" 		id="db_key" 		value="{$db_key}">
<input type="hidden" name="tipo_utente"	id="tipo_utente" 	value="{$form_stato}">
<input type="hidden" name="id_materia"	id="id_materia">
<input type="hidden" name="id_classe"	id="id_classe">
<input type="hidden" name="id_studente"	id="id_studente">
<input type="hidden" name="periodo"		id="periodo">

	<div id = 'header_mastercom'>
    <table width='100%' class='sfondo_scuro_generico bordo_superiore_rilievo'>
            <tr>
                <td width='1%'>
                    <img src='images/logo2.png' height="40" width="69">
                </td>
 				<td  class="titolo_testo" height="54px">
					<form method='post' action='{$SCRIPT_NAME}' id='form_select_classe_corrente'>
                        <input type='hidden' name='form_stato' value=''>
                        <input type='hidden' name='stato_sesabilita' value='1'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
					<div style='float: left;' id="display_classe_corrente">
						<div style="border-color: transparent;" id="select_classe_corrente"></div>
					</div>
					&nbsp;
					&nbsp;
					&nbsp;
                    <strong> {$descrizione_pagina} </strong>
                </td>
                <td width='1%' align='right'>
                    <form method='post' action='{$SCRIPT_NAME}' target='new_tab_{math equation='rand(10,1000)'}'>
							{mastercom_auto_button
							icona="add"
							size=32
                            descrizione="Apri nuova finestra"
							label_bg='44621c'
							}
                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>
			</tr>
    </table>

    {if $privilegi == "2"}
        <table width='100%' class="sfondo_base_generico bordo_inferiore_rilievo">
            <tr valign='middle'>
                <td width='50%'>
                    <form method='post' action='{$SCRIPT_NAME}'>
						{mastercom_auto_button
							icona="uscita"
							size=48
							label="ESCI"
							label_bg='d11a2e'
                            descrizione="Esci da Mastercom"
						}
                        <input type='hidden' name='form_stato' value='logout'>
                        <input type='hidden' name='stato_sesabilita' value='1'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>
                <td  width='50%' align='center' class='sottotitolo_testo'>
                    Siamo spiacenti ma il suo utente non ha i privilegi per accedere all'area riservata di sua competenza,
                    per modificare i propri privilegi rivolgersi al responsabile scolastico.
                </td>
            </tr>
        </table>
    {else}
        <table width='100%' class="sfondo_base_generico bordo_generico_rilievo">
            <tr valign='middle'>
				<td>
					<form method='post' action='{$SCRIPT_NAME}'>
					{mastercom_auto_button
						icona="uscita"
						size=48
						label="ESCI"
						label_bg='d11a2e'
					}
						<input type='hidden' name='form_stato' value='logout'>
						<input type='hidden' name='stato_sesabilita' value='1'>
						<input type='hidden' name='current_user' value='{$current_user}'>
						<input type='hidden' name='current_key' value='{$current_key}'>
					</form>
				</td>
                <td  width='50%'>
                    <form method='post' action='{$SCRIPT_NAME}' name='form_cambio_pwd'>
						{mastercom_auto_button
							icona="password"
							size=48
							label="PASSWORD"
							tipo_bottone="image"
							label_bg='cc5e00'
							onclick="if (confirm('Si desidera procedere al cambio password?'))
                                    {
                                        document.forms['form_cambio_pwd'].submit();
                                    }"
						}
                        <input type='hidden' name='form_stato' value='change_password'>
                        <input type='hidden' name='stato_sesabilita' value='1'>
                        <input type='hidden' name='current_user' value='{$current_user}'>
                        <input type='hidden' name='current_key' value='{$current_key}'>
                    </form>
                </td>

                    <td align='center' >
                            {mastercom_smart_button
                        		estensione='NO'
                        		nome_pulsante="visualizzazione_assenze"
                        		immagine="icone_dinamiche/dispimg_icone_menu.php?icona=Window&text=10&text2=ASSENZE"
                            	icona="calendario"
								bullet="01"
								size=48
								label="AGENDA"
                       			contesto="istituto"
                        		tipo_bottone="input"
                        		descrizione="Gestione Calendario Docente"
                        		utente_corrente=$current_user
								label_bg='44621c'
								load_target='mainpage'
								load_page='professore/agenda'
                            }


                    </td>
                    <td align='center' >
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_voti"
								immagine="icona.php?icon=voti&bullet=08&size=48&label=VOTI"
								icona="voti"
								bullet="02"
								size=48
								label="APPELLO"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Visione Registro Classe"
								utente_corrente=$current_user
								label_bg='44621c'
								load_target='mainpage'
								load_page='professore/registro_classe'

                            }
                    </td>

                    <td align='center' >
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_voti"
								immagine="icona.php?icon=voti&bullet=08&size=48&label=VOTI"
								icona="voti"
								bullet="03"
								size=48
								label="REGISTRO"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Visione Registro Docente"
								utente_corrente=$current_user
								label_bg='44621c'
								load_target='mainpage'
								load_page='professore/registro_docente'

                            }
                    </td>
                    <td align='center' >
                            {mastercom_smart_button
								estensione='NO'
								nome_pulsante="visualizzazione_studenti"
								immagine="icona.php?icon=studente&bullet=07&size=48&label=STUDENTI"
								icona="studente"
								bullet="04"
								size=48
								label="STUDENTI"
								contesto="istituto"
								tipo_bottone="input"
								descrizione="Gestione Dati Studenti"
								utente_corrente=$current_user
								label_bg='44621c'
								load_target='mainpage'
								load_page='professore/studenti'
                            }
                    </td>

                {if $funzione_comunicazioni == "1"}
                    <td align='center' >
                            {mastercom_smart_button
                                estensione='NO'
                                nome_pulsante="visualizzazione_comunicazioni"
                                icona="comunicazioni"
                                bullet="05"
                                size=48
                                label="COMUNICAZIONI"
                                contesto="istituto"
                                tipo_bottone="input"
                                descrizione="Gestione Comunicazioni"
                                utente_corrente=$current_user
								label_bg='1d8eb6'
								load_target='mainpage'
								load_page='professore/comunicazioni'

                            }
                    </td>
                {/if}



                {if $funzione_pagelle == "1"}
                    <td align='center' >
						{mastercom_smart_button
                	        estensione='NO'
            	            nome_pulsante="visualizzazione_pagelle"
                            icona="pagella"
							bullet="06"
							size=48
							label="PAGELLE"
        	                contesto="istituto"
    	                    tipo_bottone="input"
	                        descrizione="Gestione Pagelle e Pagelline"
                        	utente_corrente=$current_user
							label_bg='3c2f67'
							load_target='mainpage'
							load_page='professore/pagelle'
                    	}
                    </td>
                {/if}

                {if $funzione_esami_stato == "1"}
                    <td align='center' >
						{mastercom_smart_button
							estensione='NO'
	                        nome_pulsante="visualizzazione_pagelle"
                            icona="esami"
							bullet="07"
							size=48
							label="ESAMI"
    	                    contesto="istituto"
        	                tipo_bottone="input"
            	            descrizione="Gestione esami di stato"
                	        utente_corrente=$current_user
							label_bg='3c2f67'
							load_target='mainpage'
							load_page='professore/esami_stato'
						}
					</td>
                {/if}


				{if $funzione_corsi == "1" and $abilita_corsi == 'SI'}
                    <td align='center'>
						{mastercom_smart_button
							estensione='NO'
							nome_pulsante="sezione_corsi"
							icona="corsi"
							bullet="08"
							size=48
							label="CORSI"
							contesto="istituto"
							tipo_bottone="input"
							descrizione="Gestione Corsi"
							utente_corrente=$current_user
							label_bg='44621c'
							load_target='mainpage'
							load_page='professore/corsi'
						}
                    </td>
                {/if}
				{if $funzione_mensa == "1" and $abilita_servizio_mensa == 'SI'}
                    <td align='center'>
						{mastercom_smart_button
							estensione='NO'
							nome_pulsante="visualizzazione_eventi"
							icona="mense"
							bullet="09"
							size=48
							label="MENSE"
							contesto="istituto"
							tipo_bottone="input"
							descrizione="Gestione Mense"
							utente_corrente=$current_user
							label_bg='44621c'
							load_target='mainpage'
							load_page='professore/mense'
						}
                    </td>
                {/if}

            </tr>
        </table>
    {/if}
	</div>
	<br>
	<div id='body_mastercom'>
		<table width='100%' class="sfondo_base_generico bordo_generico_rilievo">
			<tr>
				<td>
					<table width='100%' height='100%'>
						<tr>
							<td colspan='2' class="sfondo_contrasto_generico titolo_funzione">
								<table><tr><td align='center' width='100%' class='titolo_funzione'><label id='titolo_mainpage'></label></td><td align='right'><input type='button' value='M' id='btn_minmax'/></td></tr></table>
							</td>
						</tr>
						<tr>
							<td colspan='2' class="padding_cella_generica">&nbsp;</td>
						</tr>
						<tr height='100%'>
							<td colspan='2' class="padding_cella_generica" valign='top'>
								<div id='mainpage' height='100px' width='1000px'></div>
							</td>
						</tr>

					</table>
				</td>
			</tr>
		</table>
	</div>



<div id="jqxLoader">
</div>



