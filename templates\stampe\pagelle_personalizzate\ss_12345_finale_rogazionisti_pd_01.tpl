<table width='100%' align='center'>
    <tr>
        <td colspan='2' align='center' class='titolo_funzione sfondo_contrasto_generico'>
            Stampa Pagella di fine anno per le classi della scuola Secondaria di Secondo Grado
        </td>
    </tr>
    <tr><td><br></td></tr>
    <tr>
        <td align='right' width='60%' class='divisore_basso'>
            Selezionare la classe che si vuole utilizzare per la stampa:
        </td>
        <td width='40%' class='divisore_basso'>
            <SELECT name="id_classe">
                {section name=cont1 loop=$elenco_classi}
                    <OPTION value="{$elenco_classi[cont1][0]}">{$elenco_classi[cont1][3]} {$elenco_classi[cont1][1]}{$elenco_classi[cont1][2]}</OPTION>
                {/section}
            </SELECT>
        </td>
    </tr>
    <tr>
        <td align='right' class='divisore_basso'>
              Selezionare la data che si desidera far comparire nella stampa come data dello scrutinio del I quadrimestre:
        </td>
        <td class='divisore_basso'>
            {html_select_date prefix="data_primo_q_" start_year=$anno_inizio end_year=$anno_attuale_temp month_format="%m" field_order="DMY"}
        </td>
    </tr>
    <tr>
        <td align='right' class='divisore_basso'>
              Selezionare la data che si desidera far comparire nella stampa come data dello scrutinio di fine anno:
        </td>
        <td class='divisore_basso'>
            {html_select_date prefix="data_fine_anno_" start_year=$anno_inizio end_year=$anno_attuale_temp month_format="%m" field_order="DMY"}
        </td>
    </tr>
    <tr>
        <td align='right' class='divisore_basso'>
              Inserire, se si necessita, il numero di protocollo che verrà stampato sui moduli aggiuntivi:
        </td>
        <td class='divisore_basso'>
            <input type="text" name="numero_protocollo">
        </td>
    </tr>


    <tr>
        <td align='right' class='divisore_basso'>
            Selezionare se si vuole inserire, nella stampa, le lettere di aiuto e/o sospensione:
        </td>
        <td class='divisore_basso'>
            <SELECT name="stampa_lettere">
                <OPTION selected value="NO">NO</OPTION>
                <OPTION value="SI">SI</OPTION>
                <OPTION value="SOLO_LETTERE">Solo le lettere</OPTION>
            </SELECT>
        </td>
    </tr>

    <tr>
        <td align='right' class='divisore_basso'>
            Selezionare se si ha la necessità di stampare solo gli studenti sospesi a giugno (se impostato non stampa le lettere):
        </td>
        <td class='divisore_basso'>
            <SELECT name="stampa_solo_studenti_sospesi">
                <OPTION selected value="NO">NO</OPTION>
                <OPTION value="SI">SI</OPTION>
            </SELECT>
        </td>
    </tr>

    {* <tr>
        <td align='right' class='divisore_basso'>
            Selezionare se si desidera stampare solo le lettere di aiuto e/o sospensione degli alunni con giudizio sospeso:
        </td>
        <td class='divisore_basso'>
            <SELECT name="stampa_solo_lettere_sospesi">
                <OPTION selected value="NO">NO</OPTION>
                <OPTION value="SI">SI</OPTION>
            </SELECT>
        </td>
    </tr> *}

    {if $pagelle_ministeriali_sito == "SI"}
    <tr>
        <td align='right' class='divisore_basso'>
            Selezionare se si vuole produrre il file pdf per la stampa o generare le pagelle sul sito:
        </td>
        <td class='divisore_basso'>
            <SELECT name="output_stampa">
                <OPTION selected value="DOWNLOAD">PDF</OPTION>
                <OPTION value="SITO_GENITORI">Pagelle sul sito</OPTION>
                <OPTION value="ZIP">Zip per firma digitale</OPTION>
                <OPTION value="SEGRETERIA_DIGITALE">Segreteria Digitale</OPTION>
            </SELECT>
        </td>
    </tr>
    {/if}
    <tr>
        <td colspan='2' align='center'>
            <input type='image' name='bottone' value='OK' src='icone/icona.php?icon=stampa&label=stampa&label_bg=44621c&size={$dimensione_immagini}' alt='Conferma selezione'>
            <input type="hidden" name="periodo_pagella" value="finale">
        </td>
    </tr>
</table>
