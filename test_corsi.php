<!DOCTYPE html>
<html lang="en">
<head>
    <title id='Description'>Basic grouping Grid showing collapsible data groups that can
        be customized via the 'Group By' header menu option or via drag and drop of grid
        column headers.</title>
    <link rel="stylesheet" href="libs/jqwidgets/styles/jqx.base.css" type="text/css" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1 minimum-scale=1" />
        <!-- <script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script> -->
		<script type="text/javascript" src="libs/jquery/jquery-3.7.1.min.js"></script>
        <script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.darkblue.css" type="text/css" />

		<script type="text/javascript" src="libs/jqwidgets/jqx-all.js"></script>
		<script type="text/javascript" src="test_corsi_data.js"></script>
    <script type="text/javascript">
$(document).ready(function () {
			var theme='darkblue';

			// prepare the data
            var source =
            {
                datatype: "array",
                datafields: [
                    { name: 'id' 		},
                    { name: 'periodo' 		},
                    { name: 'descrizione' 	},
                    { name: 'docente' 		},
                    { name: 'materia' 		},
                    { name: 'inizio' 		},
					{ name: 'fine' 		 	},
					{ name: 'iscritti' 		},
					{ name: 'moduli'	 	},
					{ name: 'tipo'	 	},
                ],
				localdata: data
            };
            var dataAdapter = new $.jqx.dataAdapter(source);
            // Create jqxGrid
            $("#grid").jqxGrid(
            {
                width: '1200px',
				source: dataAdapter,
				theme: theme,
                groupable: true,
                columns: [
					{ text: 'Periodo', 		datafield: 'periodo', 		width: 50 },
					{ text: 'Materia', 		datafield: 'materia', 		width: 150 },
					{ text: 'Prof', 		datafield: 'docente', 		width: 150 },
					{ text: 'Corso', 		datafield: 'descrizione', 	width: 250 },
					{ text: 'Inizio', 		datafield: 'inizio', 		width: 100 },
					{ text: 'Fine', 		datafield: 'fine', 			width: 100 },
					{ text: 'Isc.', 		datafield: 'iscritti', 		width: 80 },
					{ text: 'Mod.', 		datafield: 'moduli' },
					{ text: 'Tipo', 		datafield: 'tipo', 			width: 150 },
					{ text: '', 		width: 60, columntype: 'button',
								cellsrenderer: function () {
									return "Modifica";
								},
							},
					{ text: '', 		width: 60, columntype: 'button',
								cellsrenderer: function () {
									return "Elimina";
								},
							}
                ],
                groups: ['materia']
            });
            $("#expandall").jqxButton({ theme: theme });
            $("#collapseall").jqxButton({ theme: theme });
            // expand all groups.
            $("#expandall").on('click', function () {
                $("#grid").jqxGrid('expandallgroups');
            });
            // collapse all groups.
            $("#collapseall").on('click', function () {
                $("#grid").jqxGrid('collapseallgroups');
            });
            // trigger expand and collapse events.
            $("#grid").on('groupexpand', function (event) {
                var args = event.args;
                //$("#expandedgroup").text("Group: " + args.group + ", Level: " + args.level);
            });
            $("#grid").on('groupcollapse', function (event) {
                var args = event.args;
                //$("#collapsedgroup").text("Group: " + args.group + ", Level: " + args.level);
			});

			$("#select_docente").jqxDropDownList({theme: theme, width: 180, placeHolder: "Docente"});
			$("#select_materia").jqxDropDownList({theme: theme, width: 180, placeHolder: "Materia"});
			$("#select_tipo").jqxDropDownList({theme: theme, width: 180, placeHolder: "Tipo"});
			$("#text_search").jqxInput({theme: theme, placeHolder: "Cerca", height: 30, width: 250, minLength: 1,});
            $("#jqxExpander").jqxExpander({theme: theme, expanded: false, width: '1200px'});
			$("#text_search").jqxInput({theme: theme, placeHolder: "Cerca", height: 30, width: 250, minLength: 1,});
			$("#text_search").jqxInput({theme: theme, placeHolder: "Cerca", height: 30, width: 250, minLength: 1,});
			$("#data_inizo").jqxDateTimeInput({theme: theme,  width: '300px', height: '25px' });
			$("#data_fine").jqxDateTimeInput({theme: theme,  width: '300px', height: '25px' });
			$("#jqxButtonGroup").jqxButtonGroup({theme: theme,   width: '1200px',mode: 'radio' });
			$('#jqxButtonGroup').jqxButtonGroup('setSelection', 0);
			$('#tabs').jqxTabs({theme: theme, width: '500px', height: 300, position: 'top'});
			$("#bottone_nuovo").jqxButton({theme: theme, width: 190, height: 40 ,template: "primary"});
			$("#bottone_salva").jqxButton({theme: theme, width: 190, height: 40 ,template: "success"});
			$("#bottone_annulla").jqxButton({theme: theme, width: 190, height: 40 ,template: "warning"});


			$("#txt_descrizione_corso").jqxInput({theme: theme, placeHolder: "Titolo corso", height: 30, width: 250, minLength: 1,});
			$("#txt_attivita_corso").jqxInput({theme: theme, placeHolder: "Sottotitolo corso", height: 30, width: 250, minLength: 1,});
    		$("#select_tipo_corso").jqxDropDownList({ theme: theme, disabled: false, width: 130,  placeHolder: "Tipo corso:"});
			$('#txt_descrizione_completa').jqxEditor({
				theme: 'darkblue',
				height: 240,
				width: 460,
				tools: 'bold italic underline | left center right | ul ol'
			});

			$("#input_max_partecipanti").jqxNumberInput({theme: theme, placeHolder: "Max partecipanti", height: 30, width: 50, decimalDigits: 0, inputMode: 'simple', spinButtons: true});
			$("#input_monteore_corso").jqxNumberInput({theme: theme, placeHolder: "Monteore", height: 30, width: 50, decimalDigits: 0, inputMode: 'simple', spinButtons: true});
			$("#input_classi_ammesse").jqxDropDownList({ theme: theme, disabled: false, width: 250,  placeHolder: "Cassi ammesse:", checkboxes: true, source: classi});
			$("#input_classi_ammesse").jqxDropDownList('checkAll');


        });
    </script>
</head>
<body class='default' width='100%'>
	<table>
		<tr>
			<td colspan=3>
				<div id='jqxButtonGroup'>
					<button style="padding:4px 16px;" id="periodo_0" checked>
						Iscrizioni Chiuse</button>
					<button style="padding:4px 16px;" id="periodo_1">
						Periodo 1 aperto</button>
					<button style="padding:4px 16px;" id="periodo_2">
						Periodo 2 aperto</button>
					<button style="padding:4px 16px;" id="periodo_3">
						Periodo 3 aperto</button>
					<button style="padding:4px 16px;" id="periodo_4">
						Periodo 4 aperto</button>
				</div>
			</td>
		</tr>
		<tr>
			<td colspan=3>
				<div id='jqxExpander'>
					<div ><input type="text" id="text_search"/></div>
					<div>
						<div  style='display: table'>
							<div style='display: row'>
								<span style="float: left; margin: 5px; margin-left: 20px;">
									<div id='select_docente'> </div>
								</span>
								<span style="float: left; margin: 5px;  margin-left: 20px;">
									<div id='select_materia'> </div>
								</span>
								<span style="float: left; margin: 5px;  margin-left: 20px;">
									<div id='select_tipo'> </div>
								</span>
							</div>
							<div style='display: row'>
								<span style="float: left; margin: 5px; margin-left: 20px;">
									<label>Data inizio</label>
									<div id='data_inizo'> </div>
								</span>
								<span style="float: left; margin: 5px;  margin-left: 20px;">
									<label>Data fine</label>
									<div id='data_fine'> </div>
								</span>
							</div>
							<div style='display: row'>
								<span style="float: left; margin: 5px; margin-left: 20px;">
									<button id='expandall'>Espandi tutti</button>
								</span>
								<span style="float: left; margin: 5px; margin-left: 20px;">
									<button id='collapseall'>Collassa tutti</button>
								</span>
							</div>
						</div>
					</div>
				</div>
			</td>
		</tr>
		<tr>
			<td colspan=3>
				<div id="grid" width='100%'>
				</div>
 			</td>
		</tr>
		<tr>
			<td valign='top'>
				<div>
					<div>
						<button id='bottone_nuovo'>Nuovo corso</button>
					</div>
				</div>
			</td>
			<td rowspan=2 valign='top'>
					<table>
						<tr>
							<td colspan='6' > <input type='text' id='txt_descrizione_corso' size='29' placeholder="Titolo"></td>
							<td colspan='2' align='right' ><label>Tipo:</label></td>
							<td colspan='4' >
                                <div id='select_tipo_corso'></div>
							</td>
						</tr>
						<tr>
							<td colspan='6'><input type='text' id='txt_attivita_corso' size='29' placeholder="Sottotitolo"></td>
							<td colspan='2' align='right'><label>Periodo:</label></td>
							<td colspan='4'>
								<select id='sel_periodo'>
									<option>1</option>
									<option>2</option>
									<option>3</option>
									<option>4</option>
								</select>
							</td>
						</tr>
                        <tr>
							<td colspan='6'>
								<label>Classi ammesse</label>
                                <div id='input_classi_ammesse'></div>
                            </td>
                            <td colspan='3'>
								<label>Max partecipanti</label>
                                <input type="text" id='input_max_partecipanti'>
                            </td>
                            <td colspan='3'>
								<label>Monteore</label>
                                <input type="text" id='input_monteore_corso'>
                            </td>
						</tr>
                        <tr>
                            <td colspan="12" style="/*opacity: 0.5;*/ padding-top: 4px;">Descrizione</td>
                        </tr>
						<tr>
							<td colspan='12'><textarea id='txt_descrizione_completa'></textarea></td>
						</tr>
						<tr>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
							<td>&nbsp;</td>
						</tr>
					</table>
 			</td>
			<td align='right' rowspan=2 valign='top'>
				<div id='tabs'>
					<ul>
						<li style="margin-left: 30px;">Studenti</li>
						<li>Orario</li>
						<li>Professori</li>
						<li>Materie</li>
						<li>Risorse</li>
					</ul>
					<div>1</div>
					<div>2</div>
					<div>3</div>
					<div>4</div>
					<div>5</div>
				</div>
 			</td>
		</tr>
		<tr>
			<td valign='bottom'>
				<div>
					<div>
						<button id='bottone_salva'>Salva</button>
					</div>
					<div>
						<button id='bottone_annulla'>Annulla</button>
					</div>
				</div>
			</td>
		</tr>
	</table>
</body>
</html>
