<?php

switch ($stato_secondario) {
    case 'risultati_esami_scuole_medie_update':
        //{{{ <editor-fold defaultstate="collapsed">
        if ($id_classe != "") {
            $elenco_studenti_classe = [];
            $elenco_studenti = estrai_studenti_classe((int) $id_classe);

            foreach ($elenco_studenti as $studente) {
                if ($studente['esito_terza_media'] == 'SI') {
                    $elenco_studenti_classe[] = $studente;
                }
            }

            foreach ($elenco_studenti_classe as $studente) {
                $voto_esame_medie_italiano = '';
                $tipo_voto_esame_medie_italiano = '';
                $voto_esame_medie_inglese = '';
                $tipo_voto_esame_medie_inglese = '';
                $numero_quesiti_esame_medie_matematica = '';
                $voto_esame_medie_matematica = '';
                $voto_esame_medie_seconda_lingua = '';
                $tipo_voto_esame_medie_seconda_lingua = '';
                $voto_esame_medie_invalsi_finale = '';
                $giudizio_prove_scritte_scuole_medie = '';
                $giudizio_descrittivo_finale_esame_terza_media = '';
                $consiglio_orientativo_trentino = '';

                if (strlen(${"ita_" . $studente['id_studente']}) > 0) {
                    $voto_esame_medie_italiano = intval(encode(${"ita_" . $studente['id_studente']}));
                }else{
                    $voto_esame_medie_italiano = '';
                }

                if (strlen(${"ita_tipo_" . $studente['id_studente']}) > 0) {
                    $tipo_voto_esame_medie_italiano = intval(encode(${"ita_tipo_" . $studente['id_studente']}));
                }else{
                    $tipo_voto_esame_medie_italiano = '';
                }

                if (strlen(${"ing_" . $studente['id_studente']}) > 0) {
                    $voto_esame_medie_inglese = intval(encode(${"ing_" . $studente['id_studente']}));
                }else{
                    $voto_esame_medie_inglese = '';
                }

                if (strlen(${"ing_tipo_" . $studente['id_studente']}) > 0) {
                    $tipo_voto_esame_medie_inglese = (encode(${"ing_tipo_" . $studente['id_studente']}));
                }else{
                    $tipo_voto_esame_medie_inglese = '';
                }

                if (strlen(${"mat_n_ques_" . $studente['id_studente']}) > 0) {
                    $numero_quesiti_esame_medie_matematica = intval(encode(${"mat_n_ques_" . $studente['id_studente']}));
                }else{
                    $numero_quesiti_esame_medie_matematica = '';
                }

                if (strlen(${"mat_" . $studente['id_studente']}) > 0) {
                    $voto_esame_medie_matematica = intval(encode(${"mat_" . $studente['id_studente']}));
                }else{
                    $voto_esame_medie_matematica = '';
                }

                if (strlen(${"mat_tipo_" . $studente['id_studente']}) > 0) {
                    $tipo_voto_esame_medie_matematica = intval(encode(${"mat_tipo_" . $studente['id_studente']}));
                }else{
                    $tipo_voto_esame_medie_matematica = '';
                }

                if (strlen(${"sec_lin_" . $studente['id_studente']}) > 0) {
                    $voto_esame_medie_seconda_lingua = intval(encode(${"sec_lin_" . $studente['id_studente']}));
                }else{
                    $voto_esame_medie_seconda_lingua = '';
                }

                if (strlen(${"sec_lin_tipo_" . $studente['id_studente']}) > 0) {
                    $tipo_voto_esame_medie_seconda_lingua = (encode(${"sec_lin_tipo_" . $studente['id_studente']}));
                }else{
                    $tipo_voto_esame_medie_seconda_lingua = '';
                }

                if (strlen(${"invalsi_finale_" . $studente['id_studente']}) > 0) {
                    $voto_esame_medie_invalsi_finale = intval(encode(${"invalsi_finale_" . $studente['id_studente']}));
                }else{
                    $voto_esame_medie_invalsi_finale = '';
                }

                if (strlen(${"giudizio_prove_scritte_scuole_medie_" . $studente['id_studente']}) > 0) {
                    $giudizio_prove_scritte_scuole_medie = encode(${"giudizio_prove_scritte_scuole_medie_" . $studente['id_studente']});
                }else{
                    $giudizio_prove_scritte_scuole_medie = '';
                }

                //echo "---" . ${"giudizio_prove_scritte_mat_scuole_medie_" . $studente['id_studente']} . "---" . ${"giudizio_prove_scritte_ing_scuole_medie_" . $studente['id_studente']} . "---<br>";

                if (strlen(${"giudizio_prove_scritte_mat_scuole_medie_" . $studente['id_studente']}) > 0) {
                    $giudizio_prove_scritte_mat_scuole_medie = encode(${"giudizio_prove_scritte_mat_scuole_medie_" . $studente['id_studente']});
                }else{
                    $giudizio_prove_scritte_mat_scuole_medie = '';
                }

                if (strlen(${"giudizio_prove_scritte_ing_scuole_medie_" . $studente['id_studente']}) > 0) {
                    $giudizio_prove_scritte_ing_scuole_medie = encode(${"giudizio_prove_scritte_ing_scuole_medie_" . $studente['id_studente']});
                }else{
                    $giudizio_prove_scritte_ing_scuole_medie = '';
                }

                if ($valle_aosta_abilitata == 'SI'){
                    
                }
                $result = aggiorna_dati_diplomi_medie(
                        (int) $studente['id_studente'], $voto_esame_medie_italiano, $tipo_voto_esame_medie_italiano, $voto_esame_medie_inglese, $tipo_voto_esame_medie_inglese,
                        $numero_quesiti_esame_medie_matematica, $voto_esame_medie_matematica, $voto_esame_medie_seconda_lingua, $tipo_voto_esame_medie_seconda_lingua,
                        $voto_esame_medie_invalsi_finale, $giudizio_prove_scritte_scuole_medie, $tipo_voto_esame_medie_matematica, $giudizio_prove_scritte_mat_scuole_medie,
                        $giudizio_prove_scritte_ing_scuole_medie, (int) $current_user
                );

                $cosa = "AGGIORNAMENTO DATI DIPLOMI SCUOLE MEDIE DELLO STUDENTE " . $studente['id_studente'];
                inserisci_log_storico((int) $current_user, "DATI_STUDENTE", $cosa);
            }
        }

        $template->assign("elenco_studenti", $elenco_studenti_classe);
        //}}} </editor-fold>
        break;

    case 'risultati_esami_orali_scuole_medie_update':
        //{{{ <editor-fold defaultstate="collapsed">
        if ($id_classe != "") {
            $elenco_studenti_classe = [];
            $elenco_studenti = estrai_studenti_classe((int) $id_classe);

            foreach ($elenco_studenti as $studente) {
                if ($studente['esito_terza_media'] == 'SI') {
                    $elenco_studenti_classe[] = $studente;
                }
            }

            foreach ($elenco_studenti_classe as $studente) {
                $voto_esame_medie_orale = '';
                $argomenti_orali_medie = '';
                $giudizio_1_medie = '';
                $giudizio_2_medie = '';
                $giudizio_3_medie = '';
                $prova_esame_esame_terza_media = '';

                if (strlen(${"orale_" . $studente['id_studente']}) > 0) {
                    $voto_esame_medie_orale = intval(encode(${"orale_" . $studente['id_studente']}));
                }else{
                    $voto_esame_medie_orale = '';
                }

                if (strlen(${"argomenti_orali_" . $studente['id_studente']}) > 0) {
                    $argomenti_orali_medie = encode(${"argomenti_orali_" . $studente['id_studente']});
                }else{
                    $argomenti_orali_medie = '';
                }

                if (strlen(${"giudizio_1_" . $studente['id_studente']}) > 0) {
                    $giudizio_1_medie = encode(${"giudizio_1_" . $studente['id_studente']});
                }else{
                    $giudizio_1_medie = '';
                }

                if (strlen(${"giudizio_2_" . $studente['id_studente']}) > 0) {
                    $giudizio_2_medie = encode(${"giudizio_2_" . $studente['id_studente']});
                }else{
                    $giudizio_2_medie = '';
                }

                if (strlen(${"giudizio_3_" . $studente['id_studente']}) > 0) {
                    $giudizio_3_medie = encode(${"giudizio_3_" . $studente['id_studente']});
                }else{
                    $giudizio_3_medie = '';
                }

                if (strlen(${"prova_esame_esame_terza_media_" . $studente['id_studente']}) > 0) {
                    $prova_esame_esame_terza_media = encode(${"prova_esame_esame_terza_media_" . $studente['id_studente']});
                }else{
                    $prova_esame_esame_terza_media = '';
                }

                $result = aggiorna_dati_diplomi_medie_orali(
                        (int) $studente['id_studente'], $giudizio_1_medie, $giudizio_2_medie, $giudizio_3_medie, $argomenti_orali_medie, $voto_esame_medie_orale, (int) $current_user, $prova_esame_esame_terza_media
                );

                $cosa = "AGGIORNAMENTO DATI ORALI DIPLOMI SCUOLE MEDIE DELLO STUDENTE " . $studente['id_studente'];
                inserisci_log_storico((int) $current_user, "DATI_STUDENTE", $cosa);
            }
        }
        $template->assign("elenco_studenti", $elenco_studenti_classe);
        //}}} </editor-fold>
        break;

    case 'risultati_finali_esami_scuole_medie_update':
        //{{{ <editor-fold defaultstate="collapsed">
        if ($id_classe != "") {
            $elenco_studenti_classe = [];
            $elenco_studenti = estrai_studenti_classe((int) $id_classe);

            foreach ($elenco_studenti as $studente) {
                if ($studente['esito_terza_media'] == 'SI') {
                    $elenco_studenti_classe[] = $studente;
                }
            }

            foreach ($elenco_studenti_classe as $studente) {
                $consiglio_terza_media = '';
                $giudizio_finale_1_medie = '';
                $giudizio_finale_2_medie = '';
                $giudizio_finale_3_medie = '';
                $giudizio_sintetico_esame_terza_media = '';
                $giudizio_descrittivo_finale_esame_terza_media = '';
                $consiglio_orientativo_trentino = '';
                $consiglio_terza_media_ministeriale = '';

                if (strlen(${"consiglio_terza_media_" . $studente['id_studente']}) > 0) {
                    $consiglio_terza_media = encode(${"consiglio_terza_media_" . $studente['id_studente']});
                }

                if (strlen(${"giudizio_finale_1_" . $studente['id_studente']}) > 0) {
                    $giudizio_finale_1_medie = encode(${"giudizio_finale_1_" . $studente['id_studente']});
                }

                if (strlen(${"giudizio_finale_2_" . $studente['id_studente']}) > 0) {
                    $giudizio_finale_2_medie = encode(${"giudizio_finale_2_" . $studente['id_studente']});
                }

                if (strlen(${"giudizio_finale_3_" . $studente['id_studente']}) > 0) {
                    $giudizio_finale_3_medie = encode(${"giudizio_finale_3_" . $studente['id_studente']});
                }

                if (strlen(${"giudizio_sintetico_esame_terza_media_" . $studente['id_studente']}) > 0) {
                    $giudizio_sintetico_esame_terza_media = encode(${"giudizio_sintetico_esame_terza_media_" . $studente['id_studente']});
                }

                if (strlen(${"giudizio_descrittivo_finale_esame_terza_media_" . $studente['id_studente']}) > 0) {
                    $giudizio_descrittivo_finale_esame_terza_media = encode(${"giudizio_descrittivo_finale_esame_terza_media_" . $studente['id_studente']});
                }

                // consiglio orientativo
                $consiglio_orientativo_trentino = explode(',',  encode(${"consiglio_orientativo_trentino_" . $studente['id_studente']}));
                aggiorna_consiglio_orientativo_trentino((int) $studente['id_studente'], $consiglio_orientativo_trentino);




                $consiglio_orientativo_ministeriale = encode(${"consiglio_orientativo_ministeriale_" . $studente['id_studente']});
                
                $result = aggiorna_dati_diplomi_medie_finali(
                    (int) $studente['id_studente'], $giudizio_finale_1_medie, $giudizio_finale_2_medie, $giudizio_finale_3_medie, $consiglio_terza_media, $giudizio_sintetico_esame_terza_media, (int) $current_user,'NO',$giudizio_descrittivo_finale_esame_terza_media,$consiglio_orientativo_ministeriale
                );

                $cosa = "AGGIORNAMENTO DATI FINALI DIPLOMI SCUOLE MEDIE DELLO STUDENTE " . $studente['id_studente'];
                inserisci_log_storico((int) $current_user, "DATI_STUDENTE", $cosa);
            }
        }
        $template->assign("elenco_studenti", $elenco_studenti_classe);
        //}}} </editor-fold>
        break;

    case "esami_2021_scuole_medie_update":
        //{{{ <editor-fold defaultstate="collapsed">
        if ($id_classe != "") {
            $elenco_studenti_classe = [];
            $elenco_studenti = estrai_studenti_classe((int) $id_classe);

            foreach ($elenco_studenti as $studente) {
                if ($studente['esito_terza_media'] == 'SI') {
                    $elenco_studenti_classe[] = $studente;
                }
            }

            foreach ($elenco_studenti_classe as $studente) {

                $elaborato_studente_esame_terza_media = '';
                $prova_esame_esame_terza_media = '';
                $giudizio_descrittivo_finale_esame_terza_media = '';
                $voto_esame_medie_orale = '';
                $giudizio_sintetico_esame_terza_media = '';


                if (strlen(${"elaborato_studente_esame_terza_media_" . $studente['id_studente']}) > 0) {
                    $elaborato_studente_esame_terza_media = encode(${"elaborato_studente_esame_terza_media_" . $studente['id_studente']});
                }

                if (strlen(${"prova_esame_esame_terza_media_" . $studente['id_studente']}) > 0) {
                    $prova_esame_esame_terza_media = encode(${"prova_esame_esame_terza_media_" . $studente['id_studente']});
                }

                if (strlen(${"giudizio_descrittivo_finale_esame_terza_media_" . $studente['id_studente']}) > 0) {
                    $giudizio_descrittivo_finale_esame_terza_media = encode(${"giudizio_descrittivo_finale_esame_terza_media_" . $studente['id_studente']});
                }

                if (strlen(${"orale_" . $studente['id_studente']}) > 0) {
                    $voto_esame_medie_orale = intval(encode(${"orale_" . $studente['id_studente']}));
                }

                if (strlen(${"giudizio_sintetico_esame_terza_media_" . $studente['id_studente']}) > 0) {
                    $giudizio_sintetico_esame_terza_media = encode(${"giudizio_sintetico_esame_terza_media_" . $studente['id_studente']});
                }
                elseif ($voto_esame_medie_orale != '' && $studente["voto_ammissione_medie"] != '') { // uso calcolo giudizio finale anno 20/21, se campo non impostato
                    $giudizio_sintetico_esame_terza_media = round(
                            (
                            floatval($studente["voto_ammissione_medie"]) +
                            floatval($voto_esame_medie_orale)
                            ) / 2
                    );
                }

                $result = aggiorna_dati_esami_2021_medie((int) $studente['id_studente'], $elaborato_studente_esame_terza_media, $prova_esame_esame_terza_media, $giudizio_descrittivo_finale_esame_terza_media, $voto_esame_medie_orale, $giudizio_sintetico_esame_terza_media, (int) $current_user);

                // consiglio orientativo
                $consiglio_orientativo_trentino = explode(',',  encode(${"consiglio_orientativo_trentino_" . $studente['id_studente']}));
                aggiorna_consiglio_orientativo_trentino((int) $studente['id_studente'], $consiglio_orientativo_trentino);


                $cosa = "AGGIORNAMENTO ESAMI 2021 SCUOLE MEDIE DELLO STUDENTE " . $studente['id_studente'];
                inserisci_log_storico((int) $current_user, "DATI_STUDENTE", $cosa);
            }
        }
        $template->assign("elenco_studenti", $elenco_studenti_classe);
        //}}} </editor-fold>

        break;

    case 'date_orali_scuole_medie_update':
        //{{{ <editor-fold defaultstate="collapsed">
        if ($id_classe > 0) {
            $elenco_studenti = estrai_studenti_classe((int) $id_classe);

            foreach ($elenco_studenti as $studente) {
                if ($studente['esito_terza_media'] == 'SI') {
                    $elenco_studenti_classe[] = $studente;
                }
            }

            foreach ($elenco_studenti_classe as $studente) {
                $data_orale = mktime(
                        ${"ora_orale_" . $studente['id_studente']}, ${"ora_orale_" . $studente['id_studente'] . "_m"}, 0, ${"data_orale_" . $studente['id_studente'] . "Month"}, ${"data_orale_" . $studente['id_studente'] . "Day"}, ${"data_orale_" . $studente['id_studente'] . "Year"}
                );
                $ordine_esame_orale = intval(${"ordine_esame_orale_" . $studente['id_studente']});

                $result = aggiorna_date_orali_medie(
                        (int) $studente['id_studente'], $data_orale, $ordine_esame_orale, (int) $current_user
                );

                $cosa = "AGGIORNAMENTO DATI DIPLOMI SCUOLE MEDIE DELLO STUDENTE " . $studente['id_studente'];
                inserisci_log_storico((int) $current_user, "DATI_STUDENTE", $cosa);
            }

            $elenco_studenti = estrai_studenti_classe((int) $id_classe);

            foreach ($elenco_studenti as $studente) {
                if ($studente['esito_terza_media'] == 'SI') {
                    $elenco_studenti_agg[] = $studente;
                }
            }
        } else {
            echo "!?!";
        }
        //$template->assign("elenco_studenti",$elenco_studenti_classe);
        $template->assign("elenco_studenti", $elenco_studenti_agg);
        //}}} </editor-fold>
        break;

    case 'scheda_raccordo_update':
        //{{{ <editor-fold defaultstate="collapsed">
        if ($id_classe != "") {
            $elenco_studenti_classe = [];
            $elenco_studenti = estrai_studenti_classe((int) $id_classe);

            foreach ($elenco_studenti as $studente) {
                if ($studente['esito_terza_media'] == 'SI') {
                    $elenco_studenti_classe[] = $studente;
                }
            }

            foreach ($elenco_studenti_classe as $studente) {
                $dati_campi_liberi_tipo_A = estrai_valori_campi_liberi_studente((int) $studente['id_studente'], 'ESAME_A');
                $dati_campi_liberi_tipo_B = estrai_valori_campi_liberi_studente((int) $studente['id_studente'], 'ESAME_B');
                foreach ($dati_campi_liberi_tipo_A as $id_campo => $campo_libero) {
                    $id_valore_campo_libero = ${"id_valore_campo_libero_" . $studente['id_studente'] . "_" . $id_campo};
                    $valore_precomp_select = ${"studente_" . $studente['id_studente'] . "_" . $id_campo};

                    if ($valore_precomp_select != 'elimina') {
                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                            $nuovo_valore_campo = [];
                            $nuovo_valore_campo['id_valore_precomp'] = $valore_precomp_select;
                            $nuovo_valore_campo['valore_testuale'] = ${"valore_testuale_" . $studente['id_studente'] . "_" . $id_campo};
                        } else {
                            $nuovo_valore_campo = $valore_precomp_select;
                        }

                        $result = inserisci_valore_campo_libero((int) $id_valore_campo_libero, (int) $studente['id_studente'], (int) $id_campo, $nuovo_valore_campo, $campo_libero['tipo_valore'], (int) $current_user);
                    } else {
                        $result = inserisci_valore_campo_libero((int) $id_valore_campo_libero, (int) $studente['id_studente'], (int) $id_campo, 'elimina', $campo_libero['tipo_valore'], (int) $current_user);
                    }
                }
                foreach ($dati_campi_liberi_tipo_B as $id_campo => $campo_libero) {
                    $id_valore_campo_libero = ${"id_valore_campo_libero_" . $studente['id_studente'] . "_" . $id_campo};
                    $valore_precomp_select = ${"studente_" . $studente['id_studente'] . "_" . $id_campo};

                    if ($valore_precomp_select != 'elimina') {
                        if ($campo_libero['tipo_valore'] == 'PRECOMPILATO_TESTO') {
                            $nuovo_valore_campo = [];
                            $nuovo_valore_campo['id_valore_precomp'] = $valore_precomp_select;
                            $nuovo_valore_campo['valore_testuale'] = ${"valore_testuale_" . $studente['id_studente'] . "_" . $id_campo};
                        } else {
                            $nuovo_valore_campo = $valore_precomp_select;
                        }

                        $result = inserisci_valore_campo_libero((int) $id_valore_campo_libero, (int) $studente['id_studente'], (int) $id_campo, $nuovo_valore_campo, $campo_libero['tipo_valore'], (int) $current_user);
                    } else {
                        $result = inserisci_valore_campo_libero((int) $id_valore_campo_libero, (int) $studente['id_studente'], (int) $id_campo, 'elimina', $campo_libero['tipo_valore'], (int) $current_user);
                    }
                }

                $cosa = "AGGIORNAMENTO DATI SCHEDA RACCORDO (CAMPI LIBERI) SCUOLE MEDIE DELLO STUDENTE " . $studente['id_studente'];
                inserisci_log_storico((int) $current_user, "DATI_STUDENTE", $cosa);
            }
        }

        $template->assign("elenco_studenti", $elenco_studenti_classe);
        //}}} </editor-fold>
        break;
}
