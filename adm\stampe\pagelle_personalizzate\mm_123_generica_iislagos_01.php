<?php
/*
 * Tipo: pagella medie iislagos
 * mm_123_generica_iislagos_01
 * Richie<PERSON> da: iislagos
 */

/*

INSERT INTO elenco_stampe_personalizzate
(tipo_stampa, valore, descrizione, stato_attivazione, note, ordinamento)
VALUES
('H','mm_123_generica_iislagos_01','Pagella Scuola Secondaria di I Grado', 1, 'iislagos', 9);

 */

$formato = 'A4';
$orientamento = 'P';

if ($periodo_pagella == "intermedia") {
    $periodo = 27;
    $formato = 'A3';
    $orientamento = 'L';
}
else {
    $periodo = 29;
    $formato = 'A3';
    $orientamento = 'L';
}

function genera_stampa(&$pdf, $studente, $parametri_stampa) {
    //{{{ <editor-fold defaultstate="collapsed" desc="generazione stampa">
    //{{{ <editor-fold defaultstate="collapsed" desc="Estrazione dati e dizionario">
    $as_attuale = estrai_parametri_singoli("ANNO_SCOLASTICO_ATTUALE");
    $id_studente = $studente['id_studente'];
    $id_classe = $parametri_stampa['id_classe'];
    $periodo = $parametri_stampa['periodo'];
    $formato = $parametri_stampa['formato'];
    $comune_data_p1 = ucwords(strtolower($studente['descrizione_comuni'])) . ',  ' . $parametri_stampa['data_p1_day']."/".$parametri_stampa['data_p1_month']."/".$parametri_stampa['data_p1_year'];

    $data_p1 = $parametri_stampa['data_p1_day']."/".$parametri_stampa['data_p1_month']."/".$parametri_stampa['data_p1_year'];
    $data_attestato = $parametri_stampa['data_day']."/".$parametri_stampa['data_month']."/".$parametri_stampa['data_year'];

    if ($studente['descrizione_nascita'] == "COMUNE ESTERO" || !$studente['descrizione_nascita'])
    {
        $comune_nascita = $studente['citta_nascita_straniera'];
        $provincia_nascita = '';
        $stato = estrai_nazione($studente['stato_nascita']);
        if ($stato[0] != 'ERROR')
        {
            $provincia_nascita = $stato['descrizione'];
        }
    }
    else
    {
        $comune_nascita = $studente['descrizione_nascita'];
        $provincia_nascita = $studente['provincia_nascita_da_comune'];
    }

    $materie_studente = estrai_materie_multi_classe_studente((int) $id_studente);
    $voti_pagellina = estrai_voti_pagellina_studente_multi_classe($id_classe, 27, $studente['id_studente']);
    $voti_pagella_pentamestre = estrai_voti_pagellina_studente_multi_classe($id_classe, 29, $studente['id_studente']);
    if ($periodo < 29) {
        $voti_pagella_pentamestre = [];
    }

    $assenze_finale = $monteore_finale = 0;
    $scrutinato = false;
    $arr_voti = $condotta = $religione = [];
    $condotta_cmp_st = $giudizi = [];

    foreach ($materie_studente as $materia)
    {
        $id_materia = $materia['id_materia'];
        $monteore_finale += $voti_pagella_pentamestre[$id_materia]['monteore_totale'];
        $assenze_finale += $voti_pagella_pentamestre[$id_materia]['ore_assenza'];

        if ($materia['in_media_pagelle'] != 'NV' && //$materia['in_media_pagelle'] != 'NO' &&
            ( !in_array($materia['tipo_materia'], ['RELIGIONE','ALTERNATIVA', 'CONDOTTA', 'SOSTEGNO', 'OPZIONALE']) )
            )
        {
            $arr_voti[$id_materia]['1P'] = $voti_pagellina[$id_materia];
            $arr_voti[$id_materia]['2P'] = $voti_pagella_pentamestre[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'RELIGIONE') {
            $religione[$id_materia]['1P'] = $voti_pagellina[$id_materia];
            $religione[$id_materia]['2P'] = $voti_pagella_pentamestre[$id_materia];
        }

        if ($materia['in_media_pagelle'] != 'NV' && $materia['tipo_materia'] == 'CONDOTTA')
        {
            $condotta[$id_materia]['1P'] = $voti_pagellina[$id_materia];
            $condotta[$id_materia]['2P'] = $voti_pagella_pentamestre[$id_materia];

            foreach ($voti_pagellina[$id_materia]['campi_liberi'] as $campo_libero) {
                // Richieste del consiglio di classe
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    if  (stripos($campo_libero['nome'], 'giudizio globale') !== false)
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value != '') {
                            $giudizi['P1'] = $value;

                        }
                    }
                }
            }
            foreach ($voti_pagella_pentamestre[$id_materia]['campi_liberi'] as $campo_libero) {
                // Richieste del consiglio di classe
                if (in_array($id_materia, $campo_libero['abbinamenti']['materie']) || $campo_libero['abbinamenti']['materie'][0] == 0) {
                    if  (stripos($campo_libero['nome'], 'giudizio globale') !== false)
                    {
                        $value = estrai_valore_campo_libero_selezionato($campo_libero);
                        if ($value != '') {
                            $giudizi['P2'] = $value;
                        }
                    }
                }
            }
        }

        // scrutinio
        if ($voti_pagella_pentamestre[$id_materia]['voto_pagellina'] > 0 && $voti_pagella_pentamestre[$id_materia]['voto_pagellina'] != '')
        {
            $scrutinato = true;
        }
    }
    $arr_voti = $arr_voti + $condotta;

    // Validazione anno
    if ($monteore_finale > 0)
    {
        $perc_assenza = round($assenze_finale / $monteore_finale, 2) * 100;
        if ($perc_assenza < 25)
        {
            $validazione_anno = "SI";
        }
        else
        {
            $validazione_anno = ($scrutinato) ? "DEROGA" : "NO";
        }
    }
    else
    {
        $validazione_anno = "SI";
    }

    //{{{ <editor-fold defaultstate="collapsed" desc="dizionario">
    $labels = [
        "alunno"    => "Dell’alunn||min_oa||",
        "alunno_1"  => "l’alunn||min_oa||",
        "nato"      => "Nat||min_oa|| a",
        "iscritto"  => "Iscritt||min_oa|| ",
        "attestato_ammissione_1"    => "l’alunn||min_oa|| ",
        "attestato_ammissione_2"    => "è stat||min_oa|| ",
        "apprendimento" => "RILEVAZIONE DEI PROGRESSI NELL’APPRENDIMENTO E DELLO SVILUPPO PERSONALE E SOCIALE DELL’ALUNN||max_oa||"
    ];

    // Identificazione genere da dati
    if (strtoupper($studente['sesso']) == 'F') {
        $min_oa = "a";
        $min_eessa = "essa";
        $max_oa = "A";
        $max_eessa = "ESSA";
        $studente['esito'] = str_replace('Ammesso', 'Ammessa', $studente['esito']);
        $studente['esito'] = str_replace('ammesso', 'ammessa', $studente['esito']);
        $studente['esito'] = str_replace('Licenziato', 'Licenziata', $studente['esito']);
        $studente['esito'] = str_replace('licenziato', 'licenziata', $studente['esito']);
    } else {
        $min_oa = "o";
        $min_eessa = "e";
        $max_oa = "O";
        $max_eessa = "E";
    }

    // replace lettere finali per genere
    foreach ($labels as $k=>$label) {
        $labels[$k] = str_replace("||min_oa||", $min_oa, $labels[$k]);
        $labels[$k] = str_replace("||min_eessa||", $min_eessa, $labels[$k]);
        $labels[$k] = str_replace("||max_oa||", $max_oa, $labels[$k]);
        $labels[$k] = str_replace("||max_eessa||", $max_eessa, $labels[$k]);
    }
        // Validazione ammissione scrutinio
    if ((int)$studente['classe'] == 3) {
        if ( strpos( strtoupper($studente['esito']), 'NON' ) !== false ) {
            $attestato_esito = $labels['attestato_att'] . 'Non ' . $labels['attestato_att2_terze'];
        }
        else {
            $attestato_esito = $labels['attestato_att'] . $labels['attestato_att2_terze']; // promosso
        }
    }
    else {
        $attestato_esito = $labels['attestato_att'] . $studente['esito']; // 1 e 2, ammesso e non
    }

    switch ($validazione_anno) {
        case 'SI':
            $labels['scrutinio_desc_val_1'] = "[ X ] " . $labels['scrutinio_desc_val_1'];
            $labels['scrutinio_desc_val_2'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_2'];
            $labels['scrutinio_desc_val_3'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_3'];
            break;
        case 'DEROGA':
            $labels['scrutinio_desc_val_1'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_1'];
            $labels['scrutinio_desc_val_2'] = "[ X ] " . $labels['scrutinio_desc_val_2'];
            $labels['scrutinio_desc_val_3'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_3'];
            break;
        case 'NO':
            $labels['scrutinio_desc_val_1'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_1'];
            $labels['scrutinio_desc_val_2'] = "[&nbsp;&nbsp;&nbsp;] " . $labels['scrutinio_desc_val_2'];
            $labels['scrutinio_desc_val_3'] = "[ X ] " . $labels['scrutinio_desc_val_3'];
            break;
    }

    // Attestato
    $esito = $studente['esito'];
    switch(strtolower($studente['esito']))
    {
        case "licenziato":
        case "licenziata":
            $esito = " Ammess||min_oa|| al secondo Ciclo di Istruzione";
            break;
        case "non licenziato":
        case "non licenziata":
            $esito = " NON Ammess||min_oa|| al secondo Ciclo di Istruzione";
            break;
    }
    $esito = str_replace("||min_oa||", $min_oa, $esito);



    switch ($studente['classe']) {
        case 1:
            if($studente['esito_prima_media'] == 'SI') {
                $attestato_esito = "Ammess$min_oa alla classe successiva";
            }
            else {
                $attestato_esito = "Non Ammess$min_oa alla classe successiva";
            }
            break;
        case 2:
            if($studente['esito_seconda_media'] == 'SI') {
                $attestato_esito = "Ammess$min_oa alla classe successiva";
            }
            else {
                $attestato_esito = "Non Ammess$min_oa alla classe successiva";
            }
            break;
        case 3:
            if ($studente['esito_terza_media'] == 'SI' || $studente['voto_ammissione_medie'] > 5) {
                $attestato_esito =  "Ammess$min_oa agli esami di Stato con {$studente['voto_ammissione_medie']}/10";
            }
            else {
                $attestato_esito =  "Non Ammess$min_oa all'esame di Stato";
            }
            break;
    }

    $firma_img = '';
    if (file_exists('immagini_scuola/firma_dirigente.png')) {
        $firma_img = '<img src="immagini_scuola/firma_dirigente.png" height="30" width="130">';
    }

    $timbro_img = '';
    if (file_exists('immagini_scuola/timbro.png')) {
        $timbro_img = '<img src="immagini_scuola/timbro.png" height="40" width="40">';
    }

    $firme = '<table>
            <tr>
                <td width="40%">I docenti della classe:<br><br>.................................................<br><br>.................................................<br><br>.................................................<br><br>.................................................</td>
                <td width="20%"align="right"></td>
                <td width="40%" align="center"><br><br><br>.................................................<br> Il Dirigente Scolastico<br>Prof. '.$studente['nome_dirigente'].'<br><br><br><br>Firma di uno dei genitori o di chi ne fa le veci:<br><br>.................................................</td>
            </tr>
        </table>';
    $firme_a3 = '<table>
            <tr>
                <td width="40%" align="left">______________________________<br>IL/I GENITORE/I ( O CHI NE FA LE VECI)</td>
                <td width="20%"></td>
                <td width="40%" align="right">______________________________<br>IL COORDINATORE DIDATTICO</td>
            </tr>
        </table>';
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="pdf">
    $f = 'helvetica';
    $fd = 10;


    if (false)
    {
    $pdf->AddPage();
    $pdf->SetAutoPageBreak("off", 0);
    $ys = $pdf->getY();
    $page_width = $pdf->getPageWidth();
    $page_dims = $pdf->getPageDimensions();
    $ms = $page_dims['lm'];
    $mt = $page_dims['tm'];
    $md = $page_dims['rm'];
    $half_page = $page_width / 2;
    $wp = $half_page - $md - $ms;
    $x2 = $half_page + $ms;

    //{{{ <editor-fold defaultstate="collapsed" desc="p4">
    $pdf->SetFont($f, 'B', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', $labels['apprendimento'], 0, 1, false, true, 'C');
    $pdf->ln(2);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "<b>VALUTAZIONE INTERMEDIA</b>", 1, 1, false, true, 'C');
    $pdf->writeHTMLCell($wp, 35, '', '', $giudizi['P1'], 1, 1);
    $pdf->ln(2);
    $pdf->writeHTMLCell($wp, 0, '', '', $firme, 0, 1, false, true);
    $pdf->writeHTMLCell($wp, 0, '', '', "Lagos, lì $data_p1", 0, 1);
    $pdf->ln(4);
    $pdf->SetFont($f, '', $fd);
    $pdf->writeHTMLCell($wp, 0, '', '', "<b>VALUTAZIONE FINALE</b>", 1, 1, false, true, 'C');
    $pdf->writeHTMLCell($wp, 35, '', '', $giudizi['P2'], 1, 1);
    $pdf->ln(2);
    $pdf->writeHTMLCell($wp, 0, '', '', $firme, 0, 1, false, true);
    $pdf->writeHTMLCell($wp, 0, '', '', "Lagos, lì $data_attestato", 0, 1);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="p1">

    $pdf->SetY($ys);
    $pdf->Image('immagini_scuola/logo_repubblica.jpg', 305, $mt+10, 20, 20, '', '', '', false, 300, '');
    $pdf->setXY($x2, 48);
    $pdf->SetFont($f, 'I', $fd+4);
    $pdf->CellFitScale($wp, 0, "Ministero Degli Affari Esteri", 0, 1, 'C');
    $pdf->SetFont($f, '', $fd+2);
    $pdf->ln(10);
    $pdf->setX($x2);
    $pdf->writeHTMLCell($wp, 0, '', '',"Scuola Italiana Paritaria “ Enrico Mattei”<br>
Sikiru Alade Oloko Crescent off Admiralty Way (road 14) Lekki Peninsula- Lagos<br>
D.M.n. 267/3633 del 19-04-2004", 0, 1, false, true, 'C');
    $pdf->ln(10);
    $pdf->SetFont($f, '', $fd+2);
    $pdf->setX($x2);
    $pdf->CellFitScale($wp, 0, "Scuola Secondaria di I grado", 0, 1, 'C');
    $pdf->ln(15);
    $pdf->SetFont($f, 'B', $fd+7);
    $pdf->setX($x2);
    $pdf->CellFitScale($wp, 0, "ANNO SCOLASTICO $as_attuale", 0, 1, 'C');
    $pdf->ln(3);
    $pdf->setX($x2);
    $pdf->CellFitScale($wp, 0, "SCHEDA PERSONALE", 0, 1, 'C');
    $pdf->ln(15);
    $pdf->SetFont($f, '', $fd);
    $pdf->setX($x2);
    $pdf->writeHTMLCell($wp, 0, '', '',"{$labels['alunno']} <b>{$studente['cognome']} {$studente['nome']}</b>", 0, 1);
    $pdf->ln(3);
    $pdf->setX($x2);
    $pdf->writeHTMLCell($wp, 0, '', '',"{$labels['nato']} <b>$comune_nascita ($provincia_nascita)</b> il <b>{$studente['data_nascita_ext']}</b>", 0, 1);
    $pdf->ln(3);
    $pdf->setX($x2);
    $pdf->writeHTMLCell($wp, 0, '', '',"{$labels['iscritto']} alla classe <b>{$studente['classe']}</b> sezione <b>UNICA</b>", 0, 1);
    $pdf->ln(28);
    $yst = $pdf->GetY();
    $pdf->ln(2);
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->setX($x2);
    $pdf->CellFitScale($wp, 0, "ATTESTATO", 0, 1, 'C');
    $pdf->SetFont($f, '', $fd);
    $pdf->ln(4);
    $pdf->setX($x2);
    $pdf->writeHTMLCell($wp, 0, '', '', "Vista la valutazione del Consiglio di Classe si attesta che", 0, 1, false, true, 'C');
    $pdf->ln(2);
    $pdf->setX($x2);
    $pdf->writeHTMLCell($wp, 0, '', '', "{$labels['attestato_ammissione_1']} <b>{$labels['attestato_ammissione_2']} $esito</b>", 0, 1, false, true, 'C');
    $pdf->ln(6);
    $pdf->setX($x2);
    $pdf->CellFitScale($wp, 0, "Lagos, lì $data_attestato", 0, 1, 'L');
    $pdf->setX($x2);
    $pdf->CellFitScale(100, 0, "", 0, 0, 'C');
    $pdf->writeHTMLCell(0, 0, '', '', "___________________________<br>Il Dirigente Scolastico<br>Prof. {$studente['nome_dirigente']}", 0, 1, false, true, 'C');
    $pdf->ln(3);
    $pdf->writeHTMLCell($wp, $pdf->GetY()-$yst, $x2, $yst, "", 1);
    //}}} </editor-fold>

    //{{{ <editor-fold defaultstate="collapsed" desc="p2-3">
    $pdf->AddPage();
    $pdf->SetFont($f, 'B', $fd+2);
    $pdf->CellFitScale($wp, 12, "VALUTAZIONI PERIODICHE", 1, 0, 'C');
    $pdf->setX($x2);
    $pdf->CellFitScale($wp, 12, "VALUTAZIONI PERIODICHE", 1, 1, 'C');
    $pdf->SetFont($f, 'B', $fd);
    $pdf->ln(8);
    $yvt = $pdf->GetY();
    $c = 0;    $new_pg = false;
    foreach ($arr_voti as $voto)
    {
        $voto_p1 = $voto['1P'];
        $voto_p2 = $voto['2P'];
        $v1_s = $v1_l = $v2_s = $v2_l = $v1_r = $v2_r = ' ';
        foreach ($voto_p1['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto_p1['voto_pagellina']) {
                $v1_s = $voto_p1['voto_pagellina'].'/10';
                $v1_l = $significato['valore_pagella'].'/decimi';
                $v1_r = $significato['valore_pagella'];
            }
        }
        foreach ($voto_p2['significati_voto'] as $significato)
        {
            if ($significato['voto'] == $voto_p2['voto_pagellina']) {
                $v2_s = $voto_p2['voto_pagellina'].'/10';
                $v2_l = ucwords($significato['valore_pagella']).'/decimi';
                $v2_r = $significato['valore_pagella'];
            }
        }
        $desc = $voto_p1['descrizione'];
        if ($c == 7) {
            $new_pg = true;
            $pdf->SetY($yvt);
        }
        $new_pg ? $pdf->SetX($x2) : '';
        $dsc_voto = $voto_p1['tipo_materia'] == 'CONDOTTA'? "Giudizio sintetico" : "Voto in decimi";
        $pdf->MultiCell( $wp, 10, $desc, 1, 'L', false, 1, '', '', true, 1, false, true, 10, 'M', true);
        $new_pg ? $pdf->SetX($x2) : '';
        $pdf->MultiCell( 70, 14, $dsc_voto, 1, 'L', false, 0, '', '', true, 1, false, true, 14, 'M', true);
        $pdf->MultiCell( 60, 14, "I quadrimestre\n$v1_l", 1, 'C', false, 0, '', '', true, 1, false, true, 14, 'M', true);
        $pdf->MultiCell( 60, 14, "Finale\n$v2_l", 1, 'C', false, 1, '', '', true, 1, false, true, 14, 'M', true);
        $c++;
        $pdf->ln(5);
    }
    //}}} </editor-fold>
    }
    else
    {
        $pdf->AddPage();
        $pdf->SetAutoPageBreak("off", 0);
        $ys = $pdf->getY();
        $page_width = $pdf->getPageWidth();
        $page_dims = $pdf->getPageDimensions();
        $ms = $page_dims['lm'];
        $mt = $page_dims['tm'];
        $md = $page_dims['rm'];
        $half_page = $page_width / 2;
        $wp = $half_page - $md - $ms;
        $x2 = $half_page + $ms;

        //{{{ <editor-fold defaultstate="collapsed" desc="p4">
        $pdf->SetFont($f, 'B', $fd);
        $pdf->writeHTMLCell($wp, 0, '', '', $labels['apprendimento'], 0, 1, false, true, 'C');
        $pdf->ln(2);
        $pdf->SetFont($f, '', $fd);
        $pdf->writeHTMLCell($wp, 0, '', '', "<b>VALUTAZIONE INTERMEDIA</b>", 1, 1, false, true, 'C');
        $pdf->writeHTMLCell($wp, 35, '', '', $giudizi['P1'], 1, 1);
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', "Lagos, lì $data_p1", 0, 1);
        $pdf->ln(3);
        $pdf->writeHTMLCell($wp, 0, '', '', $firme_a3, 0, 1, false, true);
        $pdf->ln(6);
        $pdf->SetFont($f, '', $fd);
        $pdf->writeHTMLCell($wp, 0, '', '', "<b>VALUTAZIONE FINALE</b>", 1, 1, false, true, 'C');
        $pdf->writeHTMLCell($wp, 35, '', '', $giudizi['P2'], 1, 1);
//        $pdf->ln(2);
//        $pdf->writeHTMLCell($wp, 0, '', '', "Lagos, lì $data_attestato", 0, 1);
        $pdf->ln(4);
        $pdf->SetFont($f, 'B', $fd);
        $pdf->CellFitScale($wp, 0, "VALIDITA' DELL'ANNO SCOLASTICO (art. 5 del decreto legislativo 62 del 13 aprile 2017)", 'RLT', 1, 'C');
        $pdf->SetFont($f, '', $fd);
        $testo_attestato = "Ai fini della validità dell'anno e dell'ammissione allo scrutinio finale, l'alunn$min_oa:\n";
        switch ($validazione_anno) {
            case 'SI':
                $testo_attestato .= "[X] ha frequentato per almeno tre quarti dell'orario annuale; \n";
                $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga; \n";
                $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale. \n";
                break;
            case 'DEROGA':
                $testo_attestato .= "[   ] ha frequentato per almeno tre quarti dell'orario annuale; \n";
                $testo_attestato .= "[X] non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga; \n";
                $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale. \n";
                break;
            case 'NO':
                $testo_attestato .= "[   ] ha frequentato per almeno tre quarti dell'orario annuale; \n";
                $testo_attestato .= "[   ] non ha frequentato per almeno tre quarti dell'orario annuale, ma ha usufruito della deroga; \n";
                $testo_attestato .= "[X] non ha frequentato per almeno tre quarti dell'orario annuale. \n";
                break;
        }
        $pdf->SetFont($f, '', $fd);
        $pdf->MultiCell($wp, 23, $testo_attestato, 1, 'L');
        $pdf->ln(3);
        $pdf->writeHTMLCell($wp, 0, '', '', "Ore assenza: ".(traduci_minuti_in_ore_minuti($assenze_finale, 'ORE_ARROTONDATE')), 0, 1);
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', "Lagos, lì $data_attestato", 0, 1);
        $pdf->ln(6);
        $pdf->writeHTMLCell($wp, 0, '', '', "______________________________<br>IL/I GENITORE/I ( O CHI NE FA LE VECI)", 0, 1, false, true);
        $pdf->ln(9);
        $pdf->SetFont($f, 'B', $fd+2);
        $pdf->CellFitScale($wp, 0, "ATTESTAZIONE", 0, 1, 'C');
        $pdf->SetFont($f, '', $fd);
        $pdf->ln(1);
        $pdf->writeHTMLCell($wp, 0, '', '', "Visti gli atti d’ufficio e la valutazione dei docenti della classe, si attesta che ", 0, 1, false, true, 'C');
        $pdf->ln(2);
        $pdf->writeHTMLCell($wp, 0, '', '', "<b>{$labels['attestato_ammissione_1']} {$labels['attestato_ammissione_2']} $esito</b>", 0, 1, false, true, 'C');
        $pdf->ln(6);
        $pdf->CellFitScale($wp, 0, "Lagos, lì $data_attestato", 0, 1, 'L');
        $pdf->ln(3);
        $pdf->writeHTMLCell($wp, 0, '', '', $firme_a3, 0, 1, false, true);
        //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="p1">
        $pdf->SetY($ys);
        $pdf->Image('immagini_scuola/logo_repubblica.jpg', 305, $mt+10, 20, 20, '', '', '', false, 300, '');
        $pdf->setXY($x2, 48);
        $pdf->SetFont($f, 'I', $fd+4);
        $pdf->CellFitScale($wp, 0, "Ministero Degli Affari Esteri", 0, 1, 'C');
        $pdf->SetFont($f, '', $fd+2);
        $pdf->ln(10);
        $pdf->setX($x2);
        $pdf->writeHTMLCell($wp, 0, '', '',"Scuola Italiana Paritaria “ Enrico Mattei”<br>
    Sikiru Alade Oloko Crescent off Admiralty Way (road 14) Lekki Peninsula- Lagos<br>
    D.M.n. 267/3633 del 19-04-2004", 0, 1, false, true, 'C');
        $pdf->ln(10);
        $pdf->SetFont($f, '', $fd+2);
        $pdf->setX($x2);
        $pdf->CellFitScale($wp, 0, "Scuola Secondaria di I grado", 0, 1, 'C');
        $pdf->ln(20);
        $pdf->SetFont($f, 'B', $fd+7);
        $pdf->setX($x2);
        $pdf->CellFitScale($wp, 0, "Documento di valutazione", 0, 1, 'C');
        $pdf->ln(3);
        $pdf->ln(15);
        $pdf->setX($x2);
        $pdf->CellFitScale($wp, 0, "ANNO SCOLASTICO $as_attuale", 0, 1, 'C');
        $pdf->ln(3);
        $yst = $pdf->GetY();
        $pdf->ln(3);
        $pdf->SetFont($f, 'B', $fd);
        $pdf->setX($x2);
        $pdf->CellFitScale($wp, 0, "Dati anagrafici dello studente", 0, 1, 'C');
        $pdf->ln(5);
        $pdf->SetFont($f, '', $fd);
        $pdf->setX($x2);
        $pdf->writeHTMLCell($wp, 0, '', '',"{$labels['alunno_1']} <b>{$studente['cognome']} {$studente['nome']}</b>", 0, 1);
        $pdf->ln(4);
        $pdf->setX($x2);
        $pdf->writeHTMLCell($wp, 0, '', '',"{$labels['nato']} <b>$comune_nascita ($provincia_nascita)</b> il <b>{$studente['data_nascita_ext']}</b>", 0, 1);
        $pdf->ln(9);
        $pdf->setX($x2);
        $pdf->writeHTMLCell($wp, 0, '', '',"{$labels['iscritto']} alla classe <b>{$studente['classe']}</b> sezione <b>UNICA</b>", 0, 1);
        $pdf->ln(5);
        $pdf->setX($x2);
        $pdf->writeHTMLCell($wp, $pdf->GetY()-$yst, $x2, $yst, "", 1,1);
        $pdf->ln(10);
        $pdf->setX($x2);
        $pdf->CellFitScale($wp/2, 0, "Lagos, lì $data_attestato", 0, 0, 'L');
        $pdf->writeHTMLCell($wp/2, 0, '', '', "Il Coodinatore delle Attività Didattiche<br><br>____________________________", 0, 1, false, true, 'R');
        $pdf->ln(5);
        $pdf->setX($x2);
        $pdf->CellFitScale($wp/2, 0, "", 0, 0, 'L');
        $pdf->writeHTMLCell($wp, 0, '', '', $timbro_img, 0, 1, false, true);
        //}}} </editor-fold>

        //{{{ <editor-fold defaultstate="collapsed" desc="p2-3">
        $pdf->AddPage();
        $pdf->SetFont($f, 'B', $fd+2);
        $pdf->CellFitScale($wp, 12, "VALUTAZIONI PERIODICHE", 1, 0, 'C');
        $pdf->setX($x2);
        $pdf->CellFitScale($wp, 12, "VALUTAZIONI PERIODICHE", 1, 1, 'C');
        $pdf->SetFont($f, 'B', $fd);
        $pdf->ln(8);
        $yvt = $pdf->GetY();
        $c = 0;    $new_pg = false;
        foreach ($arr_voti as $voto)
        {
            $voto_p1 = $voto['1P'];
            $voto_p2 = $voto['2P'];
            $v1_s = $v1_l = $v2_s = $v2_l = $v1_r = $v2_r = ' ';
            foreach ($voto_p1['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voto_p1['voto_pagellina']) {
                    $v1_s = $voto_p1['voto_pagellina'].'/10';
                    $v1_l = $significato['valore_pagella'].'/decimi';
                    $v1_r = $significato['valore_pagella'];
                }
            }
            foreach ($voto_p2['significati_voto'] as $significato)
            {
                if ($significato['voto'] == $voto_p2['voto_pagellina']) {
                    $v2_s = $voto_p2['voto_pagellina'].'/10';
                    $v2_l = ucwords($significato['valore_pagella']).'/decimi';
                    $v2_r = $significato['valore_pagella'];
                }
            }
            $desc = $voto_p1['descrizione'];

            if ($c == 7) {
                $new_pg = true;
                $pdf->SetY($yvt);
            }
            $new_pg ? $pdf->SetX($x2) : '';
            $dsc_voto = $voto_p1['tipo_materia'] == 'CONDOTTA'? "Giudizio sintetico" : "Voto in decimi";
            $pdf->MultiCell( $wp, 10, $desc, 1, 'L', false, 1, '', '', true, 1, false, true, 10, 'M', true);
            $new_pg ? $pdf->SetX($x2) : '';
            $pdf->MultiCell( 70, 14, $dsc_voto, 1, 'L', false, 0, '', '', true, 1, false, true, 14, 'M', true);
            $pdf->MultiCell( 60, 14, "I quadrimestre\n$v1_l", 1, 'C', false, 0, '', '', true, 1, false, true, 14, 'M', true);
            $pdf->MultiCell( 60, 14, "Finale\n$v2_l", 1, 'C', false, 1, '', '', true, 1, false, true, 14, 'M', true);
            $c++;
            $pdf->ln(5);
        }

        //}}} </editor-fold>
    }
    //}}} </editor-fold>
    //}}} </editor-fold>
}

$elenco_studenti = estrai_studenti_classe($id_classe, true);

$parametri_stampa = [
    'id_classe'         => $id_classe,
    'data_day'          => $data_Day,
    'data_month'        => $data_Month,
    'data_year'         => $data_Year,
    'data_p1_day'          => $data_p1_Day,
    'data_p1_month'        => $data_p1_Month,
    'data_p1_year'         => $data_p1_Year,
    'periodo_pagella'   => $periodo_pagella,
    'periodo'           => $periodo,
    'orientamento'      => $orientamento,
    'formato'           => $formato
];

switch ($output_stampa) {
    case "SITO_GENITORI":
        // Upload sito genitori

        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];

        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file . '.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $file = messengerFindDocs($file_name, $anno_inizio . '/' . $anno_fine, (int) $id_stud_per_stampa_sito, ['PAGELLE']);
            $content = file_get_contents($pagella);

            // NOTA: presume vi sia al max un solo file per i criteri di ricerca
            if ($file[0]['id']) {
                messengerUpdateFile($file[0]['id'], $content);
            } else {
                // Destinatari: Studente + genitori
                $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);

                messengerSaveFile([
                    'content'    => $content,
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => $file_name,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $periodo_pagella
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ]);
            }

            if (file_exists($pagella)) {
                $pagelle_generate[] = "{$studente['registro']}. {$studente['cognome']} {$studente['nome']}";
                unlink($pagella);
            }
        }

        if (empty($pagelle_generate)) {
            echo "Errore generazione pagelle (contattare l'Assistenza)";
        } else {
            echo "Generate correttamente le pagelle per gli studenti:<br>" . implode('<br>', $pagelle_generate);
        }
        break;

    case "ZIP":
        // ZIP per firma digitale
        $rel_dir = 'tmp_pdf/';
        $base_dir = '/var/www-source/mastercom/' . $rel_dir;
        $temp_dir = $base_dir . substr(md5(rand(0, 1000000)), 0, 12);
        $dir = $temp_dir . '/';

        exec('mkdir ' . $temp_dir);

        //cancello tutte i file temporanei fatti da più di 1 ora
        exec('find ' . $base_dir . '/*.zip -mmin +60 -exec rm -fr {} \;');
        foreach ($elenco_studenti as $studente) {
            $id_stud_per_stampa_sito = $studente['id_studente'];

            $cf_studente = $studente['codice_fiscale'];
            $file = $dir . $id_stud_per_stampa_sito . '_' . $cf_studente . '_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf";
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $pdf->Output($file, "F");
        }
        $nome = 'export' . date('YmdHi') . '.zip';
        exec('zip -j ' . $base_dir . $nome . ' ' . $dir . '*_' . $tipo_stampa . '_' . $periodo_pagella . '_' . $anno_inizio . $anno_fine . '_' . date('Ymd') . ".pdf");
        exec('rm -fr ' . $dir);
        //Reindirizzamento JavaScript
        echo "<HTML><SCRIPT>document.location='" . $rel_dir . $nome . "';</SCRIPT></HTML>";
        break;

    case "SEGRETERIA_DIGITALE":
        //{{{ <editor-fold defaultstate="collapsed" desc="pdf singoli per mc2">
        $nome_pagella_per_file = 'Pagella ' . $periodo_pagella . ' ' . $anno_inizio . '-' . $anno_fine;
        $pagelle_generate = [];
        $external_data = [];

        $periodo = estrai_parametri_singoli('PERIODO_PAGELLA_IN_USO',(int) $id_classe, 'classe');

        if ($periodo == '29' || $periodo == '9') {
            $descrizione_periodo = "finale";
        } else {
            $descrizione_periodo = "intermedia";
        }

        foreach ($elenco_studenti as $studente)
        {
            $id_stud_per_stampa_sito = $studente['id_studente'];
            $classe_studente = $studente['classe'].$studente['sezione'].' - '.$studente['descrizione_indirizzi'];
            $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
            genera_stampa($pdf, $studente, $parametri_stampa);
            $file_name = $nome_pagella_per_file.'_'.strtoupper($studente['cognome']).'_'.strtoupper($studente['nome']).'.pdf';
            $pagella = MC_PATH . '/tmp_pdf/' . $file_name;

            if (file_exists($pagella)) {
                unlink($pagella);
            }
            $pdf->Output($pagella, "F");

            $recipients = messengerGetStudentsRecipients((int) $id_stud_per_stampa_sito);
            $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

            foreach ($pagelle as $pagella) {
                $external_data[basename($pagella)] = [
                    'hidden'     => false,
                    'mime'       => 'application/pdf',
                    'name'       => basename($pagella),
                    'class'      => $classe_studente,
                    'owner'      => messengerGetUserID($current_user),
                    'properties' => [
                        'userId' => $id_stud_per_stampa_sito,
                        'year'   => "{$anno_inizio}/{$anno_fine}",
                        'period' => $descrizione_periodo
                    ],
                    'recipients' => $recipients,
                    'tags'       => ['PAGELLE']
                ];
            }
        }

        $pagelle = glob(MC_PATH . '/tmp_pdf/' . $nome_pagella_per_file .'_*.pdf');

        $url_send_file = get_mc2_url()."/mc2-api/archive/document";
        $data = [
            'origin_id' => 3,
            'dossier'   => "Pagelle Mastercom {$anno_inizio}/{$anno_fine}",
        ];

        $i = 0;
        foreach ($pagelle as $pagella) {
            $data['file['.$i.']'] = new CURLFile($pagella);
            $i++;
        }

        $data['external_data'] = json_encode(array_values($external_data));

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url_send_file);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible;)");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: multipart/form-data']);
        curl_setopt($ch, CURLOPT_FRESH_CONNECT, 1);
        curl_setopt($ch, CURLOPT_FORBID_REUSE, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 100);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        $result = curl_exec($ch);

        if ($result === FALSE) {
            echo "Errore durante l\'invio della pagella.";
            curl_close($ch);
        } else{
            echo 'Pagella inviata correttamente.';
            curl_close($ch);
        }
        //}}} </editor-fold>
        break;

    default:
        // Caso stampa classica - un PDF con tutte le pagelle
        $pdf = new NEXUS_PDF($orientamento, 'mm', $formato);
        foreach ($elenco_studenti as $studente) {
            genera_stampa($pdf, $studente, $parametri_stampa);
        }
        $pdf->Output($tipo_stampa . '_' . $periodo_pagella . '_' . date('Y-m-d_H-i') . '.pdf', 'D');
        break;
}

exit;
