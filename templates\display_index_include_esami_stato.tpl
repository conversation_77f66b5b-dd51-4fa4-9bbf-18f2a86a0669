{if $form_stato == 'amministratore'}
    {include file="header_amministratore.tpl"}
{elseif $versione_registro_docente == 'VERSIONE_2'}
    {include file="$header_professore.tpl"}
{else}
    {include file="header_professore.tpl"}
{/if}
<br>

<script type="text/javascript">
    var valle_aosta_abilitata = '{$valle_aosta_abilitata}';
    $(window).on("load", function() {
        $('[id^=pop_giudizio_prove_scritte_scuole_medie_]').hide();
        $('[id^=pop_giudizio_prove_scritte_mat_scuole_medie_]').hide();
        $('[id^=pop_giudizio_prove_scritte_ing_scuole_medie_]').hide();
    });
        // consiglio orientativo tn

    $(document).ready(function () {

        var elems_orientamento_studenti = $('.select_orientamento');
        var valori_consiglio_trentino = {$valori_consiglio_orientativo_trentino};

        // consiglio orientativo
        elems_orientamento_studenti.each( (index, elem) => {
            var id_studente = elem.id.replace( /^\D+/g, '');

            // prepare the data
            var source =
            {
                datatype: "json",
                datafields: [
                    { name: 'id_consiglio_orientativo_trentino' },
                    { name: 'descrizione' }
                ],
                id: 'id_consiglio_orientativo_trentino',
                localdata: valori_consiglio_trentino
            };
            var dataAdapter = new $.jqx.dataAdapter(source);
            // Create a jqxDropDownList
            $("#"+elem.id).jqxDropDownList({ checkboxes: true, source: dataAdapter, displayMember: "descrizione", valueMember: "id_consiglio_orientativo_trentino", width: 200, height: 30,});

            // pre-seleziono gli elementi dello studente e tooltip
            var studente_ordinamenti = $('input[name="'+'consiglio_orientativo_trentino_'+id_studente+'"]').val().split(",");

            studente_ordinamenti.forEach(id_ordinamento => {
                var item_checked = $("#"+elem.id).jqxDropDownList('getItemByValue', id_ordinamento);
                $("#"+elem.id).jqxDropDownList('checkItem', item_checked);
            });

            // tooltip orientamento studente
            var consigli_checked = $("#"+elem.id).jqxDropDownList('getCheckedItems');

            var studente_ordinamenti_labels = consigli_checked.map(function(elem){
                return elem.label;
            });
            var txt_studente_ordinamenti = '';
            if (studente_ordinamenti_labels.length != 0)
            {
                studente_ordinamenti_labels.forEach(label => txt_studente_ordinamenti += ' &#9679; '+label+'<br>');
            }
            $("#orientamento_studente_"+id_studente).html(txt_studente_ordinamenti);

            // evento modifica checking elements
            $("#"+elem.id).on('checkChange', function (event) {
                if (event.args) {
                    var item = event.args.item;
                    if (item) {
                        // aggiorna consigli
                        var consigli_checked = $("#"+elem.id).jqxDropDownList('getCheckedItems');
                        var studente_ordinamenti = consigli_checked.map(function(elem){
                            return elem.value;
                        }).join(",");
                        $('input[name="'+'consiglio_orientativo_trentino_'+id_studente+'"]').val(studente_ordinamenti);


                        // tooltip orientamento studente
                        var studente_ordinamenti_labels = consigli_checked.map(function(elem){
                            return elem.label;
                        });
                        var txt_studente_ordinamenti = '';
                        if (studente_ordinamenti_labels.length != 0)
                        {
                            studente_ordinamenti_labels.forEach(label => txt_studente_ordinamenti += ' &#9679; '+label+'<br>');
                        }
                        $("#orientamento_studente_"+id_studente).html(txt_studente_ordinamenti);
                    }
                }
            });
        });

        if ({$anno_inizio}>=2022) {
            // inglese
            var elems_tipo_voto_esame_medie_inglese = $('.select_tipo_voto_esame_medie_inglese');
            var valori_tipo_voto_esame_medie_inglese = [
                {
                    "val_tipo_voto_esame_medie_inglese" : "2",
                    "descrizione" : "Questionario di comprensione di un testo"
                },
                {
                    "val_tipo_voto_esame_medie_inglese" : "5",
                    "descrizione" : "Completamento, riscrittura o trasformazione di un testo"
                },
                {
                    "val_tipo_voto_esame_medie_inglese" : "7",
                    "descrizione" : "Elaborazione di un dialogo"
                },
                {
                    "val_tipo_voto_esame_medie_inglese" : "1",
                    "descrizione" : "Lettera o e-mail personale"
                },
                {
                    "val_tipo_voto_esame_medie_inglese" : "9",
                    "descrizione" : "Sintesi di un testo"
                }
            ];
            elems_tipo_voto_esame_medie_inglese.each( (index, elem) => {
                var id_studente = elem.id.replace( /^\D+/g, '');

                // prepare the data
                var source =
                {
                    datatype: "json",
                    datafields: [
                        { name: 'val_tipo_voto_esame_medie_inglese' },
                        { name: 'descrizione' }
                    ],
                    id: 'val_tipo_voto_esame_medie_inglese',
                    localdata: valori_tipo_voto_esame_medie_inglese
                };
                var dataAdapter = new $.jqx.dataAdapter(source);
                // Create a jqxDropDownList
                $("#"+elem.id).jqxDropDownList({ checkboxes: true, source: dataAdapter, displayMember: "descrizione", valueMember: "val_tipo_voto_esame_medie_inglese", width: 400, height: 30, dropDownHeight: 155});

                // pre-seleziono gli elementi dello studente
                var studente_valori = $('input[name="'+'ing_tipo_'+id_studente+'"]').val().split(",");

                studente_valori.forEach(val_tipo => {
                    var item_checked = $("#"+elem.id).jqxDropDownList('getItemByValue', val_tipo);
                    $("#"+elem.id).jqxDropDownList('checkItem', item_checked);
                });

                // tooltip
                var tipi_checked = $("#"+elem.id).jqxDropDownList('getCheckedItems');

                var studente_tipi_labels = tipi_checked.map(function(elem){
                    return elem.label;
                });
                var txt_studente_tipi = '';
                if (studente_tipi_labels.length != 0)
                {
                    studente_tipi_labels.forEach(label => txt_studente_tipi += ' &#9679; '+label+'<br>');
                }
                $("#tipo_voto_esame_medie_inglese_"+id_studente).html(txt_studente_tipi);

                // evento modifica checking elements
                $("#"+elem.id).on('checkChange', function (event) {
                    if (event.args) {
                        var item = event.args.item;
                        if (item) {
                            // aggiorna tipi
                            var valori_checked = $("#"+elem.id).jqxDropDownList('getCheckedItems');
                            var studente_valori = valori_checked.map(function(elem){
                                return elem.value;
                            }).join(",");
                            $('input[name="'+'ing_tipo_'+id_studente+'"]').val(studente_valori);

                            // tooltip
                            var studente_tipi_labels = valori_checked.map(function(elem){
                                return elem.label;
                            });
                            var txt_studente_tipi = '';
                            if (studente_tipi_labels.length != 0)
                            {
                                studente_tipi_labels.forEach(label => txt_studente_tipi += ' &#9679; '+label+'<br>');
                            }
                            $("#tipo_voto_esame_medie_inglese_"+id_studente).html(txt_studente_tipi);
                        }
                    }
                });
            });

            // seconda lingua
            var elems_tipo_voto_esame_medie_seconda_lingua = $('.select_tipo_voto_esame_medie_seconda_lingua');
            if (valle_aosta_abilitata == 'SI') {
                var valori_tipo_voto_esame_medie_seconda_lingua = [
                    {
                        "val_tipo_voto_esame_medie_seconda_lingua" : "11",
                        "descrizione" : "Production écrite: Rédaction d'un texte se rapportant à des événements réels ou imaginaires, sous forme d'article de presse, de récit, de lettre"
                    },
                    {
                        "val_tipo_voto_esame_medie_seconda_lingua" : "12",
                        "descrizione" : "Compréhension écrite"
                    }
                ];
            } else {
                var valori_tipo_voto_esame_medie_seconda_lingua = [
                    {
                        "val_tipo_voto_esame_medie_seconda_lingua" : "2",
                        "descrizione" : "Questionario di comprensione di un testo"
                    },
                    {
                        "val_tipo_voto_esame_medie_seconda_lingua" : "5",
                        "descrizione" : "Completamento, riscrittura o trasformazione di un testo"
                    },
                    {
                        "val_tipo_voto_esame_medie_seconda_lingua" : "7",
                        "descrizione" : "Elaborazione di un dialogo"
                    },
                    {
                        "val_tipo_voto_esame_medie_seconda_lingua" : "1",
                        "descrizione" : "Lettera o e-mail personale"
                    },
                    {
                        "val_tipo_voto_esame_medie_seconda_lingua" : "9",
                        "descrizione" : "Sintesi di un testo"
                    }
                ];
            }
            elems_tipo_voto_esame_medie_seconda_lingua.each( (index, elem) => {
                var id_studente = elem.id.replace( /^\D+/g, '');

                // prepare the data
                var source =
                {
                    datatype: "json",
                    datafields: [
                        { name: 'val_tipo_voto_esame_medie_seconda_lingua' },
                        { name: 'descrizione' }
                    ],
                    id: 'val_tipo_voto_esame_medie_seconda_lingua',
                    localdata: valori_tipo_voto_esame_medie_seconda_lingua
                };
                var dataAdapter = new $.jqx.dataAdapter(source);
                // Create a jqxDropDownList
                $("#"+elem.id).jqxDropDownList({ checkboxes: true, source: dataAdapter, displayMember: "descrizione", valueMember: "val_tipo_voto_esame_medie_seconda_lingua", width: 400, height: 30, dropDownHeight: 155});

                // pre-seleziono gli elementi dello studente
                var studente_valori = $('input[name="'+'sec_lin_tipo_'+id_studente+'"]').val().split(",");

                studente_valori.forEach(val_tipo => {
                    var item_checked = $("#"+elem.id).jqxDropDownList('getItemByValue', val_tipo);
                    $("#"+elem.id).jqxDropDownList('checkItem', item_checked);
                });

                // tooltip
                var tipi_checked = $("#"+elem.id).jqxDropDownList('getCheckedItems');

                var studente_tipi_labels = tipi_checked.map(function(elem){
                    return elem.label;
                });
                var txt_studente_tipi = '';
                if (studente_tipi_labels.length != 0)
                {
                    studente_tipi_labels.forEach(label => txt_studente_tipi += ' &#9679; '+label+'<br>');
                }
                $("#tipo_voto_esame_medie_seconda_lingua_"+id_studente).html(txt_studente_tipi);

                // evento modifica checking elements
                $("#"+elem.id).on('checkChange', function (event) {
                    if (event.args) {
                        var item = event.args.item;
                        if (item) {
                            // aggiorna tipi
                            var valori_checked = $("#"+elem.id).jqxDropDownList('getCheckedItems');
                            var studente_valori = valori_checked.map(function(elem){
                                return elem.value;
                            }).join(",");
                            $('input[name="'+'sec_lin_tipo_'+id_studente+'"]').val(studente_valori);

                            // tooltip
                            var studente_tipi_labels = valori_checked.map(function(elem){
                                return elem.label;
                            });
                            var txt_studente_tipi = '';
                            if (studente_tipi_labels.length != 0)
                            {
                                studente_tipi_labels.forEach(label => txt_studente_tipi += ' &#9679; '+label+'<br>');
                            }
                            $("#tipo_voto_esame_medie_seconda_lingua_"+id_studente).html(txt_studente_tipi);
                        }
                    }
                });
            });
        }

        if ('{$anno_scolastico}' == '2020/2021')
        {
            $('select[name="pubb_primo_scritto"]').prop('disabled', 'disabled');
            $('select[name="pubb_secondo_scritto"]').prop('disabled', 'disabled');
            $('select[name="pubb_terzo_scritto"]').prop('disabled', 'disabled');

            $('select[name="pubb_primo_scritto"] option[value="NO"]').attr('selected', 'selected').prop('selected', 'selected');
            $('select[name="pubb_primo_scritto"]').val("NO").change();
            $('select[name="pubb_secondo_scritto"] option[value="NO"]').attr('selected', 'selected').prop('selected', 'selected');
            $('select[name="pubb_secondo_scritto"]').val("NO").change();
            $('select[name="pubb_terzo_scritto"] option[value="NO"]').attr('selected', 'selected').prop('selected', 'selected');
            $('select[name="pubb_terzo_scritto"]').val("NO").change();

            $('.esame_scritti').hide();
        }
    });


    function cambiaScheda(idScheda, classeSchede=null){
        if (classeSchede) {
            $('.'+classeSchede+':not(#'+idScheda+')').hide();
        }
        $('#'+idScheda).show();
    }

</script>

<script type="text/javascript" language="javascript" src="javascript/display_index_include_esami_stato.js"></script>

<table width='100%' class="sfondo_base_generico bordo_generico_rilievo">
    <tr>
        {if $stato_espansione == "1"}
            <td valign='top' width='25%' colspan='2'>
                {include file="include_indirizzi_classi.tpl"}
            </td>
        </tr>
        {if $dati_classe.tipo_indirizzo == '4'}
            <tr>
                <td width='100%'  colspan='2' valign='top'>
                    {if $id_classe > 0}
                        {*}/{{{ intestazione pulsanti sopra elenco classi *}
                        <table width='100%' style="border-collapse: separate;">
                            <tr>
                                <td colspan=4 class='sfondo_contrasto_generico titolo_funzione'>
                                    Esami di stato scuole medie classe: {$classe} {$indirizzo}
                                </td>
                            </tr>
                            <tr><td><br></td></tr>
                            {if $messaggio != ''}
                            <tr>
                                <td colspan="4" align='center'>
                                    <font color='#ff6666' size='3'>{$messaggio}</font>
                                </td>
                            </tr>
                            {/if}
                            <tr>
                                <td colspan=4>
                                    {if $dati_commissione.composizione_approvata == 'SI'}
                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI'}
                                            <input type='button' id='bottone_folder_ruoli' value='Ruoli commissione' onclick="switch_folder_medie('ruoli');" disabled>
                                        {else}
                                            <input type='button' id='bottone_folder_ruoli' value='Ruoli commissione' onclick="switch_folder_medie('ruoli');" style="display:none;">
                                        {/if}
                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_verbali == 'SI'}
                                            <input type='button' id='bottone_folder_verbali' value='Verbali' onclick="switch_folder_medie('verbali');">
                                        {else}
                                            <input type='button' id='bottone_folder_verbali' value='Verbali' onclick="switch_folder_medie('verbali');" style='display:none;'>
                                        {/if}
                                        {if $anno_scolastico != '2020/2021' and ($dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_cal_scr == 'SI')}
                                            <input type='button' id='bottone_folder_calendario_scritti' value='Calendario Scritti' onclick="switch_folder_medie('calendario_scritti');">
                                        {else}
                                            <input type='button' id='bottone_folder_calendario_scritti' value='Calendario Scritti' onclick="switch_folder_medie('calendario_scritti');" style='display:none;'>
                                        {/if}
                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_cal_orali == 'SI'}
                                            <input type='button' id='bottone_folder_calendario_orali' value='Calendario Colloqui' onclick="switch_folder_medie('calendario_orali');">
                                        {else}
                                            <input type='button' id='bottone_folder_calendario_orali' value='Calendario Colloqui' onclick="switch_folder_medie('calendario_orali');" style='display:none;'>
                                        {/if}
                                        {if $anno_scolastico != '2020/2021' and ($dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento == 'SI')}
                                            <input type='button' id='bottone_folder_risultati' value='Prove Scritte' onclick="switch_folder_medie('risultati');">
                                        {else}
                                            <input type='button' id='bottone_folder_risultati' value='Prove Scritte' onclick="switch_folder_medie('risultati');" style='display:none;'>
                                        {/if}
                                        {if $anno_scolastico != '2020/2021' and ($dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento_orali == 'SI')}
                                            <input type='button' id='bottone_folder_risultati_orali' value='Colloqui' onclick="switch_folder_medie('risultati_orali');">
                                        {else}
                                            <input type='button' id='bottone_folder_risultati_orali' value='Colloqui' onclick="switch_folder_medie('risultati_orali');" style='display:none;'>
                                        {/if}
                                        {if $anno_scolastico != '2020/2021' and ($dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento_finali == 'SI')}
                                            <input type='button' id='bottone_folder_risultati_finali' value='Votazione Finale' onclick="switch_folder_medie('risultati_finali');">
                                        {else}
                                            <input type='button' id='bottone_folder_risultati_finali' value='Votazione Finale' onclick="switch_folder_medie('risultati_finali');" style='display:none;'>
                                        {/if}
                                        {if $anno_scolastico == '2020/2021' and ($dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento == 'SI')}
                                            <input type='button' id='bottone_folder_esami_2021' value='Esami 2021' onclick="switch_folder_medie('esami_2021');">
                                        {else}
                                            <input type='button' id='bottone_folder_esami_2021' value='Esami 2021' onclick="switch_folder_medie('esami_2021');" style='display:none;'>
                                        {/if}
                                        {if $anno_inizio <= '2019' and ($dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_scheda_raccordo == 'SI')}
                                            <input type='button' id='bottone_folder_scheda_raccordo' value='Scheda Raccordo' onclick="switch_folder_medie('scheda_raccordo');">
                                        {else}
                                            <input type='button' id='bottone_folder_scheda_raccordo' value='Scheda Raccordo' onclick="switch_folder_medie('scheda_raccordo');" style='display:none;'>
                                        {/if}
                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_scheda_candidato == 'SI'}
                                            <input type='button' id='bottone_folder_scheda_personale' value='Scheda Candidato' onclick="switch_folder_medie('scheda_personale');">
                                        {else}
                                            <input type='button' id='bottone_folder_scheda_personale' value='Scheda Candidato' onclick="switch_folder_medie('scheda_personale');" style='display:none;'>
                                        {/if}
                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_stampa == 'SI'}
                                            <input type='button' id='bottone_folder_stampe' value='Stampe' onclick="switch_folder_medie('stampe');">
                                        {else}
                                            <input type='button' id='bottone_folder_stampe' value='Stampe' onclick="switch_folder_medie('stampe');" style='display:none;'>
                                        {/if}
                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_registro == 'SI'}
                                            <input type='button' id='bottone_folder_registro' value='Registro Esami' onclick="switch_folder_medie('registro');">
                                        {else}
                                            <input type='button' id='bottone_folder_registro' value='Registro Esami' onclick="switch_folder_medie('registro');" style='display:none;'>
                                        {/if}
                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_pubblicazione == 'SI'}
                                            <input type='button' id='bottone_folder_pubb' value='Pubblicazione Dati' onclick="switch_folder_medie('pubb');">
                                        {else}
                                            <input type='button' id='bottone_folder_pubb' value='Pubblicazione Dati' onclick="switch_folder_medie('pubb');" style='display:none;'>
                                        {/if}
                                    {/if}
                                </td>
                                <td align='right'>
                                    {if $dati_commissione.composizione_approvata == 'SI'}
                                        <input type='button' id='bottone_folder_tutti' value='Tutti' onclick="switch_folder_medie('tutti');">
                                    {/if}
                                </td>
                            </tr>

                            {if $dati_presidente.id_utente == $current_user or $superutente == 'SI'}
                                <table width='100%' extra_tag='ruoli' style="border-collapse: separate;">
                                    {* {{{ *}
                                    <tr class='es_sfondo_ruoli_commissari'>
                                        <td align='center' rowspan="2" class='es_titolo_colonna'>
                                            COMMISSARIO
                                        </td>
                                        <td align='center' rowspan="2" class='es_titolo_colonna'>
                                            CARICA
                                        </td>
                                        <td align='center' colspan="11" class='es_titolo_colonna'>
                                            FUNZIONI
                                        </td>
                                    </tr>
                                    <tr class='es_sfondo_ruoli_commissari'>
                                        <td align='center' class='es_titolo_colonna'>
                                            VERBALI
                                        </td>
                                        {if $anno_scolastico != '2020/2021'}
                                        <td align='center' class='es_titolo_colonna'>
                                            CAL.SCR.
                                        </td>
                                        {/if}
                                        <td align='center' class='es_titolo_colonna'>
                                            CAL.OR.
                                        </td>
                                        {if $anno_scolastico != '2020/2021'}
                                        <td align='center' class='es_titolo_colonna'>
                                            PROVE SCRITTE
                                        </td>
                                        <td align='center' class='es_titolo_colonna'>
                                            COLLOQUI
                                        </td>
                                        <td align='center' class='es_titolo_colonna'>
                                            VOTAZIONE FINALE
                                        </td>
                                        {else}
                                        <td align='center' class='es_titolo_colonna'>
                                            ESAMI 20/21
                                        </td>
                                        {/if}
                                        {if $anno_inizio <= '2020'}
                                        <td align='center' class='es_titolo_colonna'>
                                            SCHEDA DI RACCORDO
                                        </td>
                                        {/if}
                                        <td align='center' class='es_titolo_colonna'>
                                            SCHEDA.CAND.
                                        </td>
                                        <td align='center' class='es_titolo_colonna'>
                                            STAMPE
                                        </td>
                                        <td align='center' class='es_titolo_colonna'>
                                            REG.ESAMI
                                        </td>
                                        <td align='center' class='es_titolo_colonna'>
                                            PUBBLICAZIONE
                                        </td>
                                    </tr>
                                    <form method='post' action='{$SCRIPT_NAME}'>
                                        {foreach from=$elenco_commissari.interni item=professore}
                                            {if $professore.selezionato == "SI"}
                                                <tr class='es_sfondo_ruoli_commissari'>
                                                    <td>
                                                        {$professore.cognome} {$professore.nome}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="ruolo_interno_" indice=$professore.id_professore value=$professore.dati_commissario.ruolo_interno}
                                                        ###Nessuno@@@
                                                        V###Vicepresidente@@@
                                                        S###Segretario@@@
                                                        VS###Vicepresidente e Segretario
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_verbali_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_verbali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    {if $anno_scolastico != '2020/2021'}
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_cal_scr_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_cal_scr}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    {/if}
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_cal_orali_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_cal_orali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    {if $anno_scolastico != '2020/2021'}
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_inserimento_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_inserimento}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_inserimento_orali_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_inserimento_orali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_inserimento_finali_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_inserimento_finali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    {else} {* esami 20/21*}
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_inserimento_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_inserimento}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    {/if}
                                                    {if $anno_inizio <= '2020'}
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_scheda_raccordo_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_scheda_raccordo}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    {/if}
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_scheda_candidato_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_scheda_candidato}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_stampa_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_stampa}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_registro_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_registro}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_pubblicazione_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_pubblicazione}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                            {/if}
                                        {/foreach}
                                        {foreach from=$elenco_commissari.esterni item=professore}
                                            {if $professore.selezionato == "SI" and $dati_presidente.id_utente != $professore.id_utente}
                                                <tr class='es_sfondo_ruoli_commissari'>
                                                    <td>
                                                        {$professore.cognome} {$professore.nome}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="ruolo_interno_" indice=$professore.id_utente value=$professore.dati_commissario.ruolo_interno}
                                                        ###Nessuno@@@
                                                        V###Vicepresidente@@@
                                                        S###Segretario@@@
                                                        VS###Vicepresidente e Segretario
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_verbali_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_verbali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_cal_scr_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_cal_scr}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_cal_orali_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_cal_orali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_inserimento_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_inserimento}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_inserimento_orali_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_inserimento_orali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_inserimento_finali_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_inserimento_finali}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_scheda_raccordo_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_scheda_raccordo}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_scheda_candidato_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_scheda_candidato}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_stampa_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_stampa}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_registro_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_registro}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                    <td align="center">
                                                        {mastercom_auto_select name="permesso_pubblicazione_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_pubblicazione}
                                                        NO###NO@@@SI###SI
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                            {/if}
                                        {/foreach}
                                        <tr class='es_sfondo_ruoli_commissari'>
                                            <td colspan=13 align='center'>
                                                <input type='submit' value='Salva'>
                                                <input type='hidden' name='form_folder' value='ruoli'>
                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                <input type='hidden' name='stato_secondario' value='aggiorna_funzioni_commissione'>
                                                <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                <input type='hidden' name='classe' value='{$classe}'>
                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                            </td>
                                        </tr>
                                    </form>

                                    {* }}} *}
                                </table>
                            {/if}
                            {if $dati_commissione.composizione_approvata == 'SI'}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_verbali == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='verbali'>
                                        {* {{{ *}
                                        <tr class='es_sfondo_verbali'>
                                            <td colspan='2' align='center' class='titolo_funzione'>
                                                STAMPA VERBALI ESAMI DI STATO
                                            </td>
                                        </tr>
                                        <tr class='es_sfondo_verbali'>
                                            <td align='center' class='es_titolo_colonna'>
                                                Selezionare verbale storico salvato:
                                            </td>
                                            <td align='center' class='es_titolo_colonna'>
                                                Selezionare verbale:
                                            </td>
                                        </tr>
                                        <tr class='es_sfondo_verbali'>
                                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                            <td align='center'>
                                                {mastercom_auto_select name="tipo_storico_verbale_stato_medie" size='30' array_dati=$mat_storico_verbali_stato_medie ondblclick="
                                                    this.form.action='documenti.php';
                                                    this.form.target='print_window';
                                                    this.form.form_azione.value='carica_storico_documento';
                                                    document.getElementById('hid_form_id_storico_documento').value = this.value;
                                                    this.form.submit();
                                                "}
                                                {/mastercom_auto_select}
                                                <br>
                                                <input type='button' value='Apri' onclick="
                                                        this.form.action = 'documenti.php';
                                                        this.form.target = 'print_window';
                                                        this.form.form_azione.value = 'carica_storico_documento';
                                                        document.getElementById('hid_form_id_storico_documento').value = this.form.tipo_storico_verbale_stato_medie.value;
                                                        this.form.submit();
                                                       ">
                                                <input type='button' value='Download' onclick="
                                                        this.form.action = 'documenti.php';
                                                        this.form.target = 'print_window';
                                                        this.form.form_azione.value = 'download_storico_documento';
                                                        document.getElementById('hid_form_id_storico_documento').value = this.form.tipo_storico_verbale_stato_medie.value;
                                                        this.form.submit();
                                                       ">
                                                <input type='button' value='Elimina' onclick="es_elimina(this,'verbali_medie');">
                                                <input type='hidden' id='hid_form_id_storico_documento' name='form_id_storico_documento' value=''>
                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                <input type='hidden' name='form_azione' value=''>
                                                <input type='hidden' name='form_folder' value='verbali'>
                                                <input type='hidden' name='mat_classi[]' value='{$id_classe}'>
                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                            </td>
                                        </form>
                                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                            <td align='center'>
                                                {mastercom_auto_select name="tipo_verbale_stato_medie" size='30' array_dati=$mat_verbali_stato_medie ondblclick="
													this.form.action='documenti.php';
													this.form.target='print_window';
													document.getElementById('hid_form_id_modello_documento').value = this.value;
													this.form.submit();
												"}
                                                {/mastercom_auto_select}

                                                <br>
                                                <input type='button' value='Apri' onclick="
                                                        this.form.action = 'documenti.php';
                                                        this.form.target = 'print_window';
                                                        document.getElementById('hid_form_id_modello_documento').value = this.form.tipo_verbale_stato_medie.value;
                                                        this.form.submit();
                                                       ">
                                                <input type='hidden' id='hid_form_id_modello_documento' name='form_id_modello_documento' value=''>
                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                <input type='hidden' name='form_azione' value='genera_documento'>
                                                <input type='hidden' name='form_folder' value='verbali'>
                                                <input type='hidden' name='mat_classi[]' value='{$id_classe}'>
                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                            </td>
                                        </form>
                                        <form name="form_reload">
                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                            <input type='hidden' name='stato_secondario' value=''>
                                            <input type='hidden' name='form_folder' value=''>
                                            <input type='hidden' name='form_id_storico_documento' value=''>
                                            <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                            <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                            <input type='hidden' name='classe' value='{$classe}'>
                                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                                            <input type='hidden' name='id_classe_selezionata' value='{$id_classe}'>
                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                        </form>
                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_scheda_candidato == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='scheda_personale'>
                                        {* {{{ *}
                                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='center' colspan='2' class='titolo_funzione'>
                                                    STAMPA SCHEDA PERSONALE DEL CANDIDATO
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='center' colspan='2' class='padding_cella_generica'>
                                                    Premere stampa per stampare la scheda del candidato degli studenti:
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='right' width ="60%">
                                                    Selezionare il formato della carta da utilizzare:
                                                </td>
                                                <td width ="40%" class='padding_cella_generica'>
                                                    <SELECT name="formato_pagina_medie">
                                                        <OPTION selected value="A4">A4</OPTION>
                                                        <OPTION value="A3">A3</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare nel caso dell'A3 se si vuole stampare il fronte, il retro o entrambi:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <SELECT name="stampa_fronte_retro_medie">
                                                        <OPTION selected value="SOLO_FRONTE">Solo fronte</OPTION>
                                                        <OPTION value="SOLO_RETRO">Solo retro</OPTION>
                                                        <OPTION value="FRONTE_RETRO">Fronte e retro</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare la data da stampare:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    {html_select_date prefix="data_" start_year=$anno_inizio month_format="%m" field_order="DMY"}
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare se stampare lo spazio firme dei commissari:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <SELECT name="stampa_firma_commissari">
                                                        <OPTION selected value="SI">SI</OPTION>
                                                        <OPTION value="NO">NO</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare se stampare il giudizio stampato in pagina 2:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <SELECT name="stampa_pag2_giudizio_si_no">
                                                        <OPTION selected value="SI">SI</OPTION>
                                                        <OPTION value="NO">NO</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare il tipo del giudizio stampato in pagina 2:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <SELECT name="stampa_pag2_giudizio">
                                                        <OPTION selected value="GIUDIZIO">Giudizio</OPTION>
                                                        <OPTION value="RIGHE">Righe vuote</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                                <td align='center' colspan='4' class='padding_cella_generica'>
                                                    <input type='submit' name='bottone' value='Stampa'>
                                                    <input type='hidden' name='form_folder' value='registro'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_secondario' value='stampa_scheda_personale_candidato_medie'>
                                                    <input type='hidden' name='form_target' value='blank'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                        </form>
                                        </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                            <td align='center' colspan='2' class='padding_cella_generica'>
                                                Selezionare quale modulo aprire:
                                            </td>
                                        </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                            <td align='center' colspan='2' class='padding_cella_generica'>
                                                {mastercom_auto_select name="tipo_modulo" array_dati=$mat_moduli ondblclick="
                                                    this.form.action='documenti.php';
                                                    this.form.target='print_window';
                                                    document.getElementById('hid_form_id_modello_documento_moduli').value = this.value;
                                                    this.form.submit();
                                                "}
                                                {/mastercom_auto_select}
                                                <br>
                                                <input type='button' value='Apri' onclick="
                                                        this.form.action = 'documenti.php';
                                                        this.form.target = 'print_window';
                                                        document.getElementById('hid_form_id_modello_documento_moduli').value = this.form.tipo_modulo.value;
                                                        this.form.submit();
                                                       ">
                                                <input type='hidden' id='hid_form_id_modello_documento_moduli' name='form_id_modello_documento' value=''>
                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                <input type='hidden' name='form_azione' value='genera_documento'>
                                                <input type='hidden' name='form_folder' value='scheda_personale'>
                                                <input type='hidden' name='mat_classi[]' value='{$id_classe}'>
                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                            </td>
                                        </form>
                                        </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                            <td align='center' colspan='2' class='padding_cella_generica'>
                                                Selezionare i moduli già salvati:
                                            </td>
                                        </tr>
                                            <tr class='es_sfondo_scheda_candidato'>
                                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                            <td align='center' colspan='2' class='padding_cella_generica'>
                                                {mastercom_auto_select name="tipo_storico_modulo" size='10' array_dati=$mat_storico_moduli ondblclick="
                                                    this.form.action='documenti.php';
                                                    this.form.target='print_window';
                                                    this.form.form_azione.value='carica_storico_documento';
                                                    document.getElementById('hid_form_id_storico_documento_moduli').value = this.value;
                                                    this.form.submit();
                                                "}
                                                {/mastercom_auto_select}
                                                <br>
                                                <input type='button' value='Apri' onclick="
                                                        this.form.action = 'documenti.php';
                                                        this.form.target = 'print_window';
                                                        this.form.form_azione.value = 'carica_storico_documento';
                                                        document.getElementById('hid_form_id_storico_documento_moduli').value = this.form.tipo_storico_modulo.value;
                                                        this.form.submit();
                                                       ">
                                                <input type='button' value='Download' onclick="
                                                        this.form.action = 'documenti.php';
                                                        this.form.target = 'print_window';
                                                        this.form.form_azione.value = 'download_storico_documento';
                                                        document.getElementById('hid_form_id_storico_documento_moduli').value = this.form.tipo_storico_modulo.value;
                                                        this.form.submit();
                                                       ">
                                                    <input type='button' value='Elimina' onclick="es_elimina(this,'scheda_personale');">

                                                <input type='hidden' id='hid_form_id_storico_documento_moduli' name='form_id_storico_documento' value=''>
                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                <input type='hidden' name='form_azione' value=''>
                                                <input type='hidden' name='form_folder' value='scheda_personale'>
                                                <input type='hidden' name='mat_classi[]' value='{$id_classe}'>
                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                            </td>
                                        </form>

                                        <form name="form_reload">
                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                            <input type='hidden' name='stato_secondario' value=''>
                                            <input type='hidden' name='form_folder' value=''>
                                            <input type='hidden' name='form_id_storico_documento' value=''>
                                            <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                            <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                            <input type='hidden' name='classe' value='{$classe}'>
                                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                                            <input type='hidden' name='id_classe_selezionata' value='{$id_classe}'>
                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                        </form>

                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_stampa == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='stampe'>
                                        {* {{{ *}
                                        <tr class='es_sfondo_stampe'>
                                            <td colspan='2' align='center' class='titolo_funzione'>
                                                STAMPE NECESSARIE PER GLI ESAMI DI STATO
                                            </td>
                                        </tr>
                                        <tr class='es_sfondo_stampe'>
                                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                            <td width='60%' align='right' class='padding_cella_generica'>
                                                Selezionare il tipo di documento che si vuole stampare:
                                            </td>
                                            <td width='40%' class='padding_cella_generica'>
                                                <SELECT name="tipo_documento">
                                                    <OPTION value="elenco_candidati">Elenco Candidati</OPTION>
                                                    <OPTION value="elenco_candidati_per_documento">Elenco Candidati per documento di riconoscimento</OPTION>
                                                    {if $anno_scolastico != '2020/2021'}
                                                    <OPTION value="elenco_prove_scritte">Elenco Candidati per prove scritte</OPTION>
                                                    {/if}
                                                    <OPTION value="elenco_prove_orali">Elenco Candidati per prove orali</OPTION>
                                                    <OPTION value="elenco_candidati_con_crediti">Elenco totale Candidati con crediti scolastici</OPTION>
                                                    <OPTION value="elenco_candidati_interni_con_crediti">Elenco Candidati Interni con crediti scolastici</OPTION>
                                                    <OPTION value="elenco_candidati_esterni_con_crediti">Elenco Candidati Esterni con crediti scolastici</OPTION>
                                                    {if $anno_scolastico != '2020/2021'}
                                                    <OPTION value="elenco_risultati_prove_scritte">Elenco Risultati Prove Scritte</OPTION>
                                                    {/if}
                                                    <OPTION value="elenco_risultati_finali">Elenco Risultati Finali</OPTION>
                                                    <OPTION value="elenco_risultati_finali_solo_esito">Elenco Risultati Finali con il solo esito</OPTION>
                                                    <OPTION value="dichiarazione_commissari">Dichiarazioni dei Commissari di non relazione con Candidati</OPTION>
                                                    <OPTION value="registro_esami_stato">Statistiche registro degli Esami di Stato</OPTION>
                                                    <OPTION value="stampa_calendari_orali">Stampa del calendario degli esami orali</OPTION>
                                                    <OPTION value="riepilogo_calendari_orali">Stampa del riepilogo del calendario degli esami orali</OPTION>
                                                    {if $anno_scolastico != '2020/2021'}
                                                    <OPTION value="elenco_prove_scritte_per_studente">Stampa risultati degli scritti per singolo studente</OPTION>
                                                    {/if}
                                                    <OPTION value="scheda_competenze">Stampa la scheda delle competenze</OPTION>
                                                    <OPTION value="scheda_raccordo">Stampa la scheda di raccordo</OPTION>
                                                </SELECT>
                                            </td>
                                        </tr>
                                        <tr class='es_sfondo_stampe'>
                                            <td align='right' class='padding_cella_generica'>
                                                Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                                            </td>
                                            <td class='padding_cella_generica'>
                                                <input type='text' name='dimensione_font' value='8' size='3'>
                                                </font>
                                            </td>
                                            </tr>
                                            <tr class='es_sfondo_stampe esame_scritti'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare per le stampe che lo necessitano il tipo di prova scritta:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    {if $anno_inizio < '2018'}
                                                    <SELECT name="tipo_prova_selezionato">
                                                        <OPTION selected value="GENERICO">Prove scritte generiche</OPTION>
                                                        <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                        <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                        <OPTION value="TERZA">Terza prova scritta</OPTION>
                                                    </SELECT>
                                                    {elseif $anno_inizio == '2019'}
                                                    <SELECT name="tipo_prova_selezionato">
                                                        <OPTION selected value="NESSUNA"> --- </OPTION>
                                                        <OPTION value="GENERICO">Prove scritte generiche</OPTION>
                                                        <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                        <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                    </SELECT>
                                                    {elseif $anno_inizio == '2020'}
                                                    <SELECT name="tipo_prova_selezionato">
                                                        <OPTION selected value="NESSUNA"> --- </OPTION>
                                                        <OPTION value="GENERICO">Prove scritte generiche</OPTION>
                                                        <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                        <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                    </SELECT>
                                                    {else}
                                                    <SELECT name="tipo_prova_selezionato">
                                                        <OPTION selected value="GENERICO">Prove scritte generiche</OPTION>
                                                        <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                        <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                        <OPTION value="TERZA">Terza prova scritta</OPTION>
                                                        {if $valle_aosta_abilitata == 'SI'}
                                                        <OPTION value="QUARTA">Quarta prova scritta</OPTION>
                                                        {/if}
                                                    </SELECT>
                                                    {/if}
                                                    </font>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_stampe'>
                                                <td align='center' colspan='2' class='padding_cella_generica'>
                                                    <input type='submit' name='bottone' value='Stampa'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_update'>
                                                    <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                    <input type='hidden' name='form_folder' value='stampe'>
                                                    <input type='hidden' name='form_target' value='blank'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                        </form>
                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_registro == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='registro'>
                                        {* {{{ *}
                                        <tr class='es_sfondo_registro'>
                                            <td colspan='2' align='center' class='titolo_funzione'>
                                                STAMPA REGISTRO ESAMI DI STATO IN CARTA LIBERA
                                            </td>
                                        </tr>
                                        <tr class='es_sfondo_registro'>
                                        <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                            <td align='right' width='60%' class='padding_cella_generica'>
                                                Digitare la dimensione del font da utilizzare (numero compreso tra 8 e 16):
                                            </td>
                                            <td width='40%' class='padding_cella_generica'>
                                                <input type='text' name='dimensione_font' value='12' size='3'>
                                            </td>
                                            </tr>
                                            <tr class='es_sfondo_registro'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare il numero di registro generale di partenza da utilizzare:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <input type='text' name='numero_registro_generale' value='' size='6'>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_registro'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Stampare la sezione relativa alle lingue straniere:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <SELECT name="stampa_sezione_lingue">
                                                        <OPTION selected value="NO"> NO </OPTION>
                                                        <OPTION value="SI">SI</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_registro'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Stampare il voto d'ammissione all'esame:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <SELECT name="stampa_sezione_voto_ammissione">
                                                        <OPTION selected value="NO">NO</OPTION>
                                                        <OPTION value="SI">SI</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_registro'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Stampare la dicitura relativa alla firma omessa:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    <SELECT name="stampa_firma_omessa">
                                                        <OPTION selected value="NO"> NO </OPTION>
                                                        <OPTION value="SI">SI</OPTION>
                                                    </SELECT>
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_registro'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Selezionare la data da stampare:
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    {html_select_date prefix="data_" start_year=$anno_inizio month_format="%m" field_order="DMY"}
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_registro'>
                                                <td align='center' colspan='2' class='padding_cella_generica'>
                                                    <input type='submit' name='bottone' value='Stampa'>
                                                    <input type='hidden' name='form_folder' value='registro'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_secondario' value='stampa_registro_esami_stato_carta_libera_medie'>
                                                    <input type='hidden' name='form_target' value='blank'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                        </form>
                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_cal_scr == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='calendario_scritti'>
                                        {* {{{ *}
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            <tr class="es_sfondo_cal_base">
                                                <td colspan='5' align='center' class='titolo_funzione'>
                                                    CALENDARIO DELLE PROVE SCRITTE
                                                </td>
                                            </tr>
                                            <tr class="es_sfondo_cal_base">
                                                <td align='center' class='es_titolo_colonna'>
                                                    PROVA
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    MATERIA
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    GIORNO
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    DURATA
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    ORARIO
                                                </td></b>
                                            </tr>
                                            <tr class="es_sfondo_cal_base">
                                                <td align='center'>
                                                    1°
                                                </td>
                                                <td align='left'>
                                                    Italiano
                                                </td>
                                                <td align='center'>
                                                    {mastercom_smart_date time=$array_calendari.esami.italiano.data  prefix="data_esame_italiano_" start_year="2012"}
                                                </td>
                                                <td align='center'>
                                                    <input type='text' value="{$array_calendari.esami.italiano.durata}" name="durata_esame_italiano" size="2"> ore
                                                </td>
                                                <td align='center'>
                                                    Dalle {html_select_time time=$array_calendari.esami.italiano.ora_inizio prefix="dalle_esame_italiano_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.italiano.ora_fine prefix="alle_esame_italiano_" display_seconds=false minute_interval="5"}
                                                </td>
                                            </tr>
                                            {if $anno_inizio < '2017'}
                                                <tr class="es_sfondo_cal_base">
                                                    <td align='center'>
                                                        2°
                                                    </td>
                                                    <td align='left'>
                                                        Inglese
                                                    </td>
                                                    <td align='center'>
                                                        {mastercom_smart_date time=$array_calendari.esami.inglese.data prefix="data_esame_inglese_" start_year="2012"}
                                                    </td>
                                                    <td align='center'>
                                                        <input type='text' value="{$array_calendari.esami.inglese.durata}" name="durata_esame_inglese" size="2"> ore
                                                    </td>
                                                    <td align='center'>
                                                        Dalle {html_select_time time=$array_calendari.esami.inglese.ora_inizio prefix="dalle_esame_inglese_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.inglese.ora_fine prefix="alle_esame_inglese_" display_seconds=false minute_interval="5"}
                                                    </td>
                                                </tr>
                                            {/if}
                                            <tr class="es_sfondo_cal_base">
                                                <td align='center'>
                                                    {if $anno_inizio < '2017'}
                                                        3°
                                                    {else}
                                                        2°
                                                    {/if}
                                                </td>
                                                <td align='left'>
                                                    Competenze logico-matematiche
                                                </td>
                                                <td align='center'>
                                                    {mastercom_smart_date time=$array_calendari.esami.matematica.data prefix="data_esame_matematica_" start_year="2012"}
                                                </td>
                                                <td align='center'>
                                                    <input type='text' value="{$array_calendari.esami.matematica.durata}" name="durata_esame_matematica" size="2"> ore
                                                </td>
                                                <td align='center'>
                                                    Dalle {html_select_time time=$array_calendari.esami.matematica.ora_inizio prefix="dalle_esame_matematica_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.matematica.ora_fine prefix="alle_esame_matematica_" display_seconds=false minute_interval="5"}
                                                </td>
                                            </tr>
                                            {if $anno_inizio >'2021'}
                                                <tr class="es_sfondo_cal_base">
                                                    <td align='center'>
                                                        3°
                                                    </td>
                                                    <td align='left' colspan="10">
                                                         Prova scritta di lingue straniere
                                                    </td>
                                                </tr>
                                                <tr class="es_sfondo_cal_base">
                                                    <td></td>
                                                    <td align='left' class="padding-left-3">
                                                        - Inglese
                                                    </td>
                                                    {if $valle_aosta_abilitata == 'SI'}
                                                        <td align='center'>
                                                            {mastercom_smart_date time=$array_calendari.esami.inglese.data prefix="data_esame_inglese_" start_year="2012"}
                                                        </td>
                                                        <td align='center'>
                                                            <input type='text' value="{$array_calendari.esami.inglese.durata}" name="durata_esame_inglese" size="2"> ore
                                                        </td>
                                                        <td align='center'>
                                                            Dalle {html_select_time time=$array_calendari.esami.inglese.ora_inizio prefix="dalle_esame_inglese_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.inglese.ora_fine prefix="alle_esame_inglese_" display_seconds=false minute_interval="5"}
                                                        </td>
                                                    {else}
                                                        <td rowspan="2" align='center'>
                                                            {mastercom_smart_date time=$array_calendari.esami.inglese.data prefix="data_esame_inglese_" start_year="2012"}
                                                        </td>
                                                        <td rowspan="2" align='center'>
                                                            <input type='text' value="{$array_calendari.esami.inglese.durata}" name="durata_esame_inglese" size="2"> ore
                                                        </td>
                                                        <td rowspan="2" align='center'>
                                                            Dalle {html_select_time time=$array_calendari.esami.inglese.ora_inizio prefix="dalle_esame_inglese_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.inglese.ora_fine prefix="alle_esame_inglese_" display_seconds=false minute_interval="5"}
                                                        </td>
                                                    {/if}
                                                </tr>
                                                <tr class="es_sfondo_cal_base">
                                                    <td></td>
                                                    <td align='left' class="padding-left-3">
                                                        {if $valle_aosta_abilitata == 'SI'}
                                                        - Francese
                                                            <td align='center'>
                                                                {mastercom_smart_date time=$array_calendari.esami.seconda_lingua.data prefix="data_esame_seconda_lingua_" start_year="2012"}
                                                            </td>
                                                            <td align='center'>
                                                                <input type='text' value="{$array_calendari.esami.seconda_lingua.durata}" name="durata_esame_seconda_lingua" size="2"> ore
                                                            </td>
                                                            <td align='center'>
                                                                Dalle {html_select_time time=$array_calendari.esami.seconda_lingua.ora_inizio prefix="dalle_esame_seconda_lingua_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.seconda_lingua.ora_fine prefix="alle_esame_seconda_lingua_" display_seconds=false minute_interval="5"}
                                                            </td>
                                                       {else}
                                                        - 2° lingua comunitaria
                                                        {/if}
                                                    </td>
                                                </tr>
                                            {/if}
                                            {if $anno_inizio < '2021'}
                                                <tr class="es_sfondo_cal_base">
                                                    <td align='center'>
                                                        {if $anno_inizio < '2017'}
                                                            4°
                                                        {else}
                                                            3°
                                                        {/if}
                                                    </td>
                                                    <td align='left'>
                                                        {if $anno_inizio < '2017'}
                                                            2° lingua comunitaria
                                                        {else}
                                                            Lingue straniere
                                                        {/if}
                                                    </td>
                                                    <td align='center'>
                                                        {mastercom_smart_date time=$array_calendari.esami.seconda_lingua.data prefix="data_esame_seconda_lingua_" start_year="2012"}
                                                    </td>
                                                    <td align='center'>
                                                        <input type='text' value="{$array_calendari.esami.seconda_lingua.durata}" name="durata_esame_seconda_lingua" size="2"> ore
                                                    </td>
                                                    <td align='center'>
                                                        Dalle {html_select_time time=$array_calendari.esami.seconda_lingua.ora_inizio prefix="dalle_esame_seconda_lingua_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.seconda_lingua.ora_fine prefix="alle_esame_seconda_lingua_" display_seconds=false minute_interval="5"}
                                                    </td>
                                                </tr>
                                            {/if}
                                            {if $anno_inizio < '2017'}
                                                <tr class="es_sfondo_cal_base">
                                                    <td align='center'>
                                                        5°
                                                    </td>
                                                    <td align='left'>
                                                        Prova nazionale
                                                    </td>
                                                    <td align='center'>
                                                        {mastercom_smart_date time=$array_calendari.esami.invalsi.data prefix="data_esame_invalsi_" start_year="2012"}
                                                    </td>
                                                    <td align='center'>
                                                        <input type='text' value="{$array_calendari.esami.invalsi.durata}" name="durata_esame_invalsi" size="2"> ore
                                                    </td>
                                                    <td align='center'>
                                                        Dalle {html_select_time time=$array_calendari.esami.invalsi.ora_inizio prefix="dalle_esame_invalsi_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.esami.invalsi.ora_fine prefix="alle_esame_invalsi_" display_seconds=false minute_interval="5"}
                                                    </td>
                                                </tr>
                                            {/if}
                                            <tr class="es_sfondo_cal_base">
                                                <td colspan='5' align='center'>
                                                    <br>
                                                </td>
                                            </tr>
                                            <tr class="es_sfondo_cal_base">
                                                <td colspan='5' align='center' class='titolo_funzione'>
                                                    CALENDARIO CORREZIONE DELLE PROVE SCRITTE
                                                </td>
                                            </tr>
                                            <tr class="es_sfondo_cal_base">
                                                <td align='center' class='es_titolo_colonna'>
                                                    PROVA
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    MATERIA
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    GIORNO
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    DURATA
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    ORARIO
                                                </td></b>
                                            </tr>
                                            <tr class="es_sfondo_cal_base">
                                                <td align='center'>
                                                    1°
                                                </td>
                                                <td align='left'>
                                                    Italiano
                                                </td>
                                                <td align='center'>
                                                    {mastercom_smart_date time=$array_calendari.correzione.italiano.data prefix="data_correzione_italiano_" start_year="2012"}
                                                </td>
                                                <td align='center'>
                                                    <input type='text' value="{$array_calendari.correzione.italiano.durata}" name="durata_correzione_italiano" size="2"> ore
                                                </td>
                                                <td align='center'>
                                                    Dalle {html_select_time time=$array_calendari.correzione.italiano.ora_inizio prefix="dalle_correzione_italiano_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.italiano.ora_fine prefix="alle_correzione_italiano_" display_seconds=false minute_interval="5"}
                                                </td>
                                            </tr>
                                            {if $anno_inizio < '2017'}
                                                <tr class="es_sfondo_cal_base">
                                                    <td align='center'>
                                                        2°
                                                    </td>
                                                    <td align='left'>
                                                        Inglese
                                                    </td>
                                                    <td align='center'>
                                                        {mastercom_smart_date time=$array_calendari.correzione.inglese.data prefix="data_correzione_inglese_" start_year="2012"}
                                                    </td>
                                                    <td align='center'>
                                                        <input type='text' value="{$array_calendari.correzione.inglese.durata}" name="durata_correzione_inglese" size="2"> ore
                                                    </td>
                                                    <td align='center'>
                                                        Dalle {html_select_time time=$array_calendari.correzione.inglese.ora_inizio prefix="dalle_correzione_inglese_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.inglese.ora_fine prefix="alle_correzione_inglese_" display_seconds=false minute_interval="5"}
                                                    </td>
                                                </tr>
                                            {/if}
                                            <tr class="es_sfondo_cal_base">
                                                <td align='center'>
                                                    {if $anno_inizio < '2017'}
                                                        3°
                                                    {else}
                                                        2°
                                                    {/if}
                                                </td>
                                                <td align='left'>
                                                    Competenze logico-matematiche
                                                </td>
                                                <td align='center'>
                                                    {mastercom_smart_date time=$array_calendari.correzione.matematica.data prefix="data_correzione_matematica_" start_year="2012"}
                                                </td>
                                                <td align='center'>
                                                    <input type='text' value="{$array_calendari.correzione.matematica.durata}" name="durata_correzione_matematica" size="2"> ore
                                                </td>
                                                <td align='center'>
                                                    Dalle {html_select_time time=$array_calendari.correzione.matematica.ora_inizio prefix="dalle_correzione_matematica_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.matematica.ora_fine prefix="alle_correzione_matematica_" display_seconds=false minute_interval="5"}
                                                </td>
                                            </tr>
                                            {if $anno_inizio >'2021'}
                                                <tr class="es_sfondo_cal_base">
                                                    <td align='center'>
                                                        3°
                                                    </td>
                                                    <td align='left' colspan="10">
                                                         Prova scritta di lingue straniere
                                                    </td>
                                                </tr>
                                                <tr class="es_sfondo_cal_base">
                                                    <td></td>
                                                    <td align='left' class="padding-left-3">
                                                        - Inglese
                                                        </td>
                                                    {if $valle_aosta_abilitata == 'SI'}
                                                        <td align='center'>
                                                            {mastercom_smart_date time=$array_calendari.correzione.inglese.data prefix="data_correzione_inglese_" start_year="2012"}
                                                        </td>
                                                        <td align='center'>
                                                            <input type='text' value="{$array_calendari.correzione.inglese.durata}" name="durata_correzione_inglese" size="2"> ore
                                                        </td>
                                                        <td align='center'>
                                                            Dalle {html_select_time time=$array_calendari.correzione.inglese.ora_inizio prefix="dalle_correzione_inglese_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.inglese.ora_fine prefix="alle_correzione_inglese_" display_seconds=false minute_interval="5"}
                                                        </td>
                                                    {else}
                                                        <td rowspan="2" align='center'>
                                                            {mastercom_smart_date time=$array_calendari.correzione.inglese.data prefix="data_correzione_inglese_" start_year="2012"}
                                                        </td>
                                                        <td rowspan="2" align='center'>
                                                            <input type='text' value="{$array_calendari.correzione.inglese.durata}" name="durata_correzione_inglese" size="2"> ore
                                                        </td>
                                                        <td rowspan="2" align='center'>
                                                            Dalle {html_select_time time=$array_calendari.correzione.inglese.ora_inizio prefix="dalle_correzione_inglese_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.inglese.ora_fine prefix="alle_correzione_inglese_" display_seconds=false minute_interval="5"}
                                                        </td>
                                                    {/if}
                                                </tr>
                                                <tr class="es_sfondo_cal_base">
                                                    <td></td>
                                                    <td align='left' class="padding-left-3">
                                                        {if $valle_aosta_abilitata == 'SI'}
                                                        - Francese
                                                        <td align='center'>
                                                            {mastercom_smart_date time=$array_calendari.correzione.seconda_lingua.data prefix="data_correzione_seconda_lingua_" start_year="2012"}
                                                        </td>
                                                        <td align='center'>
                                                            <input type='text' value="{$array_calendari.correzione.seconda_lingua.durata}" name="durata_correzione_seconda_lingua" size="2"> ore
                                                        </td>
                                                        <td align='center'>
                                                            Dalle {html_select_time time=$array_calendari.correzione.seconda_lingua.ora_inizio prefix="dalle_correzione_seconda_lingua_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.seconda_lingua.ora_fine prefix="alle_correzione_seconda_lingua_" display_seconds=false minute_interval="5"}
                                                        </td>
                                                        {else}
                                                        - 2° lingua comunitaria
                                                        {/if}
                                                    </td>
                                                </tr>
                                            {/if}
                                            {if $anno_inizio < '2021'}
                                                <tr class="es_sfondo_cal_base">
                                                    <td align='center'>
                                                        {if $anno_inizio < '2017'}
                                                            4°
                                                        {else}
                                                            3°
                                                        {/if}
                                                    </td>
                                                    <td align='left'>
                                                        {if $anno_inizio < '2017'}
                                                            2° lingua comunitaria
                                                        {else}
                                                            Lingue straniere
                                                        {/if}
                                                    </td>
                                                    <td align='center'>
                                                        {mastercom_smart_date time=$array_calendari.correzione.seconda_lingua.data prefix="data_correzione_seconda_lingua_" start_year="2012"}
                                                    </td>
                                                    <td align='center'>
                                                        <input type='text' value="{$array_calendari.correzione.seconda_lingua.durata}" name="durata_correzione_seconda_lingua" size="2"> ore
                                                    </td>
                                                    <td align='center'>
                                                        Dalle {html_select_time time=$array_calendari.correzione.seconda_lingua.ora_inizio prefix="dalle_correzione_seconda_lingua_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.seconda_lingua.ora_fine prefix="alle_correzione_seconda_lingua_" display_seconds=false minute_interval="5"}
                                                    </td>
                                                </tr>
                                            {/if}
                                            {if $anno_inizio < '2017'}
                                            <tr class="es_sfondo_cal_base">
                                                <td align='center'>
                                                    5°
                                                </td>
                                                <td align='left'>
                                                    Prova nazionale
                                                </td>
                                                <td align='center'>
                                                    {mastercom_smart_date time=$array_calendari.correzione.invalsi.data prefix="data_correzione_invalsi_" start_year="2012"}
                                                </td>
                                                <td align='center'>
                                                    <input type='text' value="{$array_calendari.correzione.invalsi.durata}" name="durata_correzione_invalsi" size="2"> ore
                                                </td>
                                                <td align='center'>
                                                    Dalle {html_select_time time=$array_calendari.correzione.invalsi.ora_inizio prefix="dalle_correzione_invalsi_" display_seconds=false minute_interval="5"} alle {html_select_time time=$array_calendari.correzione.invalsi.ora_fine prefix="alle_correzione_invalsi_" display_seconds=false minute_interval="5"}
                                                </td>
                                            </tr>
                                            {/if}
                                            <tr class="es_sfondo_cal_base">
                                                <td colspan='5' align='center'>
                                                    <input type='submit' name='bottone' value='Salva'>
                                                    <input type='hidden' name='form_folder' value='calendario_scritti'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='stato_secondario' value='salva_calendario_scritti'>
                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                    <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                        </form>
                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}
                                {if $anno_inizio < '2021'}
                                    {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_scheda_raccordo == 'SI'}
                                        <table width='100%' style='display:none;border-collapse: separate;' extra_tag='scheda_raccordo'>
                                            {* {{{ *}
                                            <tr class='es_sfondo_cal_scuro'>
                                                <td colspan='4' align='center' class='titolo_funzione'>
                                                    SCHEDA DI RACCORDO
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_cal_scuro'>
                                            <form method='post' action='{$SCRIPT_NAME}'>
                                                <td align='center' class='es_titolo_colonna'>
                                                    N&deg;
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    STUDENTE
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    DATI GENERALI
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    COMPETENZE TRASVERSALI<br>ATTEGGIAMENTO NEI CONFRONTI DELLO STUDIO
                                                </td>
                                                </tr>

                                                {foreach from=$dati_studenti item=studente}
                                                    <tr class='{cycle values="es_sfondo_orali_chiaro,es_sfondo_orali_scuro"}'>
                                                        <td align='center' class='sottotitolo_testo'>
                                                            {$studente[3]}
                                                        </td>
                                                        <td class='sottotitolo_testo'>
                                                            {$studente.cognome} {$studente.nome}
                                                        </td>
                                                        <td align='center'>
                                                            <table width='100%'>
                                                                {foreach from=$studente.campi_liberi_tipo_A item=campo}
                                                                    <tr>
                                                                        <td align='right' class='sottotitolo_testo'>
                                                                            {$campo.descrizione}:
                                                                        </td>
                                                                        <td>
                                                                            {assign var="nome_variabile" value=$campo.nome_variabile}
                                                                            {mastercom_auto_select name="studente_`$nome_variabile`" array_dati=$campo.array_dati value=$campo.id_valore_precomp_selezionato}
                                                                            {/mastercom_auto_select}
                                                                            {if $campo.tipo_valore == 'PRECOMPILATO_TESTO'}
                                                                                <br>
                                                                                <input type='text' size='20' name='valore_testuale_{$nome_variabile}' value='{$campo.valore_testuale}'>
                                                                            {/if}
                                                                        </td>
                                                                    </tr>
                                                                    <input type='hidden' name='id_valore_campo_libero_{$nome_variabile}' value='{$campo.id_valore_campo_libero}'>
                                                                {/foreach}
                                                            </table>
                                                        </td>
                                                        <td align='center'>
                                                            <table width='100%'>
                                                                {foreach from=$studente.campi_liberi_tipo_B item=campo}
                                                                    <tr>
                                                                        <td align='right' class='sottotitolo_testo'>
                                                                            {$campo.descrizione}:
                                                                        </td>
                                                                        <td>
                                                                            {assign var="nome_variabile" value=$campo.nome_variabile}
                                                                            {mastercom_auto_select name="studente_`$nome_variabile`" array_dati=$campo.array_dati value=$campo.id_valore_precomp_selezionato}
                                                                            {/mastercom_auto_select}
                                                                            {if $campo.tipo_valore == 'PRECOMPILATO_TESTO'}
                                                                                <br>
                                                                                <input type='text' size='20' name='valore_testuale_{$nome_variabile}' value='{$campo.valore_testuale}'>
                                                                            {/if}
                                                                        </td>
                                                                    </tr>
                                                                    <input type='hidden' name='id_valore_campo_libero_{$nome_variabile}' value='{$campo.id_valore_campo_libero}'>
                                                                {/foreach}
                                                            </table>
                                                        </td>
                                                    </tr>
                                                {/foreach}
                                                <tr class='es_sfondo_cal_scuro'>
                                                    <td colspan='4' align='center'>
                                                        <input type='hidden' name='form_folder' value='scheda_raccordo'>
                                                        <input type='submit' name='bottone' value='Salva'>
                                                        <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                        <input type='hidden' name='id_classe_selezionata' value='{$id_classe}'>
                                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                        <input type='hidden' name='stato_secondario' value='scheda_raccordo_update'>
                                                        <input type='hidden' name='classe' value='{$classe}'>
                                                        <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </td>
                                            </form>
                                            </tr>
                                            {* }}} *}
                                        </table>
                                    {/if}
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_cal_orali == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='calendario_orali'>
                                        {* {{{ *}
                                        <tr class="es_sfondo_cal_base">
                                            <td colspan='6' align='center' class='sottotitolo_testo_bold'>
                                                IMPOSTA CALENDARI COLLOQUI
                                            </td>
                                        </tr>
                                        <tr class="es_sfondo_cal_base">
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            <td align='right'>
                                                N&deg; studenti prima giornata:
                                            </td>
                                            <td>
                                                {mastercom_auto_select name="n_stud_prima_gg" value=5}
                                                1###1@@@
                                                2###2@@@
                                                3###3@@@
                                                4###4@@@
                                                5###5@@@
                                                6###6@@@
                                                7###7@@@
                                                8###8@@@
                                                9###9@@@
                                                10###10@@@
                                                11###11@@@
                                                12###12@@@
                                                13###13@@@
                                                14###14@@@
                                                15###15@@@
                                                16###16@@@
                                                17###17@@@
                                                18###18@@@
                                                19###19@@@
                                                20###20
                                                {/mastercom_auto_select}
                                            </td>
                                            <td align='right'>
                                                N&deg; massimo studenti giornalieri:
                                            </td>
                                            <td>
                                                {mastercom_auto_select name="n_max_stud_gg" value=5}
                                                2###2@@@
                                                3###3@@@
                                                4###4@@@
                                                5###5@@@
                                                6###6@@@
                                                7###7@@@
                                                8###8@@@
                                                9###9@@@
                                                10###10@@@
                                                11###11@@@
                                                12###12@@@
                                                13###13@@@
                                                14###14@@@
                                                15###15@@@
                                                16###16@@@
                                                17###17@@@
                                                18###18@@@
                                                19###19@@@
                                                20###20
                                                {/mastercom_auto_select}
                                            </td>
                                            <td align='right'>
                                                N&deg; minimo studenti giornalieri:
                                            </td>
                                            <td>
                                                {mastercom_auto_select name="n_min_stud_gg" value=2}
                                                1###1@@@
                                                2###2@@@
                                                3###3@@@
                                                4###4@@@
                                                5###5@@@
                                                6###6@@@
                                                7###7@@@
                                                8###8@@@
                                                9###9@@@
                                                10###10@@@
                                                11###11@@@
                                                12###12@@@
                                                13###13@@@
                                                14###14@@@
                                                15###15@@@
                                                16###16@@@
                                                17###17@@@
                                                18###18@@@
                                                19###19@@@
                                                20###20
                                                {/mastercom_auto_select}
                                            </td>
                                            </tr>
                                            <tr class="es_sfondo_cal_base">
                                            <form method='post' action='{$SCRIPT_NAME}'>
                                                <td align='right'>
                                                    Data inizio esami:
                                                </td>
                                                <td>
                                                    {mastercom_smart_date prefix="data_inizio_" start_year="2009"}
                                                </td>
                                                <td>
                                                    Ora inizio orali:{mastercom_smart_hours name="ora_inizio" time="0"}
                                                </td>
                                                <td>Durata singolo orale:
                                                {mastercom_auto_select name="min_durata" value=0}
                                                0###0@@@
                                                5###5@@@
                                                10###10@@@
                                                15###15@@@
                                                20###20@@@
                                                25###25@@@
                                                30###30@@@
                                                35###35@@@
                                                40###40@@@
                                                45###45@@@
                                                50###50@@@
                                                55###55@@@
                                                60###60@@@
                                                65###65@@@
                                                70###70@@@
                                                75###75@@@
                                                80###80@@@
                                                85###85@@@
                                                90###90
                                                {/mastercom_auto_select}
                                                </td>
                                                <td align='right'>
                                                    Lettera di partenza:
                                                </td>
                                                <td colspan=2>
                                                    {mastercom_auto_select name="lettera_partenza"}
                                                    A###A@@@
                                                    B###B@@@
                                                    C###C@@@
                                                    D###D@@@
                                                    E###E@@@
                                                    F###F@@@
                                                    G###G@@@
                                                    H###H@@@
                                                    I###I@@@
                                                    J###J@@@
                                                    K###K@@@
                                                    L###L@@@
                                                    M###M@@@
                                                    N###N@@@
                                                    O###O@@@
                                                    P###P@@@
                                                    Q###Q@@@
                                                    R###R@@@
                                                    S###S@@@
                                                    T###T@@@
                                                    U###U@@@
                                                    V###V@@@
                                                    W###W@@@
                                                    X###X@@@
                                                    Y###Y@@@
                                                    Z###Z
                                                    {/mastercom_auto_select}
                                                </td>
                                                </tr>
                                                <tr class="es_sfondo_cal_base">
                                                    <td colspan='6' align='center'>
                                                        <input type='hidden' name='form_folder' value='calendario_orali'>
                                                        <input type='submit' name='bottone' value='Calcola calendario'>
                                                        <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                        <input type='hidden' name='id_classe_selezionata' value='{$id_classe}'>
                                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                        <input type='hidden' name='stato_secondario' value='genera_calendario'>
                                                        <input type='hidden' name='classe' value='{$classe}'>
                                                        <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </td>
                                            </form>
                                            </tr>
                                            {* }}} *}
                                    </table>
                                    <table width='100%' style='display:none;' extra_tag='calendario_orali'>
                                        {* {{{ *}
                                        <tr class='es_sfondo_cal_scuro'>
                                            <td colspan='6' align='center' class='sottotitolo_testo_bold'>
                                                DATE COLLOQUI
                                            </td>
                                        </tr>
                                        <tr class='es_sfondo_cal_scuro'>
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            <td align='center' class='es_titolo_colonna'>
                                                N&deg;
                                            </td>
                                            <td align='center' class='es_titolo_colonna'>
                                                STUDENTE
                                            </td>
                                            <td align='center' class='es_titolo_colonna'>
                                                DATA ORALE
                                            </td>
                                            <td align='center' class='es_titolo_colonna'>
                                                ORDINE NEL GIORNO
                                            </td>
                                            </tr>
                                            {section name=cont loop=$elenco_studenti}
                                                <tr class='{cycle values="es_sfondo_orali_chiaro,es_sfondo_orali_scuro"}'>
                                                    <td align='center' class='sottotitolo_testo'>
                                                        {$elenco_studenti[cont][3]}
                                                    </td>
                                                    <td class='sottotitolo_testo'>
                                                        {$elenco_studenti[cont][2]} {$elenco_studenti[cont][1]}
                                                    </td>
                                                    <td align='center'>
                                                        {mastercom_smart_date prefix="data_orale_`$elenco_studenti[cont][0]`" start_year="2009" time=$elenco_studenti[cont].data_orale} alle ore:
                                                        {mastercom_smart_hours name="ora_orale_`$elenco_studenti[cont][0]`" time=$elenco_studenti[cont].data_orale}
                                                    </td>
                                                    <td align='center'>
                                                        {mastercom_auto_select name="ordine_esame_orale_`$elenco_studenti[cont][0]`" value=$elenco_studenti[cont].ordine_esame_orale}
                                                        0###0@@@
                                                        1###1@@@
                                                        2###2@@@
                                                        3###3@@@
                                                        4###4@@@
                                                        5###5@@@
                                                        6###6@@@
                                                        7###7@@@
                                                        8###8@@@
                                                        9###9@@@
                                                        10###10@@@
                                                        11###11@@@
                                                        12###12@@@
                                                        13###13@@@
                                                        14###14@@@
                                                        15###15@@@
                                                        16###16@@@
                                                        17###17@@@
                                                        18###18@@@
                                                        19###19@@@
                                                        20###20@@@
                                                        21###21@@@
                                                        22###22@@@
                                                        23###23@@@
                                                        24###24@@@
                                                        25###25@@@
                                                        26###26@@@
                                                        27###27@@@
                                                        28###28@@@
                                                        29###29@@@
                                                        30###30
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                            {/section}
                                        {* }}} *}
                                        <tr class='es_sfondo_cal_scuro'>
                                            <td colspan='6' align='center'>
                                                <div>
                                                    <div colspan='14' align='center'>
                                                        <input type='submit' name='bottone' value='Salva'>
                                                        <input type='hidden' name='form_folder' value='risultati'>
                                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                        <input type='hidden' name='stato_secondario' value='date_orali_scuole_medie_update'>
                                                        <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                        <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                        <input type='hidden' name='classe' value='{$classe}'>
                                                        <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </div>
                                                </div>
                                            </form>
                                            </td>
                                        </tr>
                                    </table>
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento == 'SI'}
                                    {if $anno_inizio >= '2022'}
                                        <table width='100%' style='display:none;border-collapse: separate; padding:5px;' extra_tag='risultati'>
                                            <tr class='es_sfondo_esami_scuro'>
                                                <td colspan='15' align='center' class='titolo_funzione'>
                                                    <div >
                                                        <h1>INSERIMENTO PROVE SCRITTE</h1>
                                                    </div>
                                                    <div>
                                                        <input type="button"
                                                               name="scheda_italiano"
                                                               value="Italiano"
                                                               onclick="cambiaScheda('scheda_italiano', 'schede_esami_scritti');"
                                                        >

                                                        <input type="button"
                                                                name="scheda_matematica"
                                                               value="Competenze logico-matematiche"
                                                               onclick="cambiaScheda('scheda_matematica', 'schede_esami_scritti');"
                                                        >
                                                        {if $valle_aosta_abilitata == 'SI'}
                                                            <input type="button"
                                                                    name="scheda_inglese"
                                                                value="Lingua inglese e lingua francese"
                                                                onclick="cambiaScheda('scheda_inglese', 'schede_esami_scritti');"
                                                            >                                                           
                                                        {else}
                                                            <input type="button"
                                                                    name="scheda_inglese"
                                                                value="Lingua inglese e seconda lingua comunitaria"
                                                                onclick="cambiaScheda('scheda_inglese', 'schede_esami_scritti');"
                                                            >
                                                        {/if}
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                <form method='post' action='{$SCRIPT_NAME}'>

                                                    <div class='schede_esami_scritti'>
                                                        <table width='100%' style='display:none;border-collapse: separate; padding:5px;' extra_tag='risultati'>
                                                            <tr class='es_sfondo_esami_scuro'>
                                                                <td align='center' style='width: 5%; height: 50px;' class='es_titolo_colonna'>
                                                                    N&deg;
                                                                </td>
                                                                <td align='center' style='width: 35%;' class='es_titolo_colonna'>
                                                                    STUDENTE
                                                                </td>
                                                                <td style='width: 60%;' class='es_titolo_colonna'></td>
                                                            </tr>

                                                            {foreach from=$dati_studenti item=studente name='test'}
                                                                <tr class='{cycle values="es_sfondo_esami_chiaro,es_sfondo_esami_scuro"}'>
                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        {$studente.registro}
                                                                    </td>
                                                                    <td class='sottotitolo_testo'>
                                                                        {$studente.cognome} {$studente.nome}
                                                                    </td>
                                                                    <td class='sottotitolo_testo'></td>
                                                                </tr>
                                                            {/foreach}

                                                        </table>
                                                    </div>

                                                    <div id='scheda_italiano' class='schede_esami_scritti' style='display:none;'>
                                                        <table width='100%' class='highlight' style='display:none;border-collapse: separate; padding:5px;' extra_tag='risultati'>
                                                            <thead>
                                                            <tr class='es_sfondo_esami_scuro'>
                                                                <td align='center' rowspan='2' style='width: 5%;' class='es_titolo_colonna'>
                                                                    N&deg;
                                                                </td>
                                                                <td align='center' rowspan='2' style='width: 35%;' class='es_titolo_colonna'>
                                                                    STUDENTE
                                                                </td>

                                                                <td align='center' colspan='2' style='height: 25px;' class='es_titolo_colonna'>
                                                                    ITALIANO
                                                                </td>
                                                                <td align='center' rowspan="2" style='width: 15%;' class='es_titolo_colonna'>
                                                                    GIUDIZIO
                                                                </td>
                                                            </tr>
                                                            <tr class='es_sfondo_esami_scuro'>
                                                                <td align='center' style='width: 40%; height: 25px;' class='es_titolo_colonna'>
                                                                    TIPO
                                                                </td>
                                                                <td align='center' style='width: 5%;' class='es_titolo_colonna'>
                                                                    VOTO
                                                                </td>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            {foreach from=$dati_studenti item=studente name='test'}
                                                                <tr class='{cycle values="es_sfondo_esami_chiaro,es_sfondo_esami_scuro"}'>
                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        {$studente.registro}
                                                                    </td>
                                                                    <td class='sottotitolo_testo'>
                                                                        {$studente.cognome} {$studente.nome}
                                                                    </td>

                                                                    <td align='center'>
                                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                                        {*ATTENZIONE - se si modificano questi valori cambiarli anche in /adm/stampe/scheda_candidato_medie/include_pagina2_scheda_candidato_medie.php*}
                                                                        {mastercom_auto_select name="ita_tipo_`$temp_id_stud`" value=$studente.tipo_voto_esame_medie_italiano}
                                                                            6###Testo narrativo o descrittivo@@@
                                                                            4###Testo argomentativo@@@
                                                                            7###Comprensione e sintesi di un testo
                                                                        {/mastercom_auto_select}
                                                                    </td>
                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        <input type='text' value="{$studente.voto_esame_medie_italiano}" name="ita_{$studente.id_studente}" size="2">
                                                                    </td>
                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        <input type="button"
                                                                               name="mod_giudizio_prove_scritte_scuole_medie_{$studente.id_studente}"
                                                                               value="+"
                                                                               onclick="javascript:openPopupGiudizio({$studente.id_studente}, 'giudizio_prove_scritte_scuole_medie', 'popup_ita');">
                                                                         <input type="hidden"
                                                                                name="giudizio_prove_scritte_scuole_medie_{$studente.id_studente}"
                                                                                value="{$studente.giudizio_prove_scritte_scuole_medie}">
                                                                    </td>
                                                                </tr>
                                                            {/foreach}
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                    <div id='scheda_matematica' class='schede_esami_scritti' style='display:none;'>
                                                        <table width='100%' style='display:none;border-collapse: separate; padding:5px;' extra_tag='risultati'>
                                                            <thead>
                                                            <tr class='es_sfondo_esami_scuro'>
                                                                <td align='center' rowspan='2' style='width: 5%;' class='es_titolo_colonna'>
                                                                    N&deg;
                                                                </td>
                                                                <td align='center' rowspan='2' style='width: 35%;' class='es_titolo_colonna'>
                                                                    STUDENTE
                                                                </td>

                                                                <td align='center' colspan='2' style='height: 25px;' class='es_titolo_colonna'>
                                                                    COMPETENZE LOGICO MATEMATICHE
                                                                </td>
                                                                <td align='center' rowspan="2" style='width: 15%;' class='es_titolo_colonna'>
                                                                    GIUDIZIO
                                                                </td>
                                                            </tr>
                                                            <tr class='es_sfondo_esami_scuro' >
                                                                <td align='center' style='width: 40%; height: 25px;' class='es_titolo_colonna'>
                                                                    TRACCIA
                                                                </td>
                                                                <td align='center' style='width: 5%;' class='es_titolo_colonna'>
                                                                    VOTO
                                                                </td>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            {foreach from=$dati_studenti item=studente name='test'}
                                                                <tr class='{cycle values="es_sfondo_esami_chiaro,es_sfondo_esami_scuro"}'>
                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        {$studente.registro}
                                                                    </td>
                                                                    <td class='sottotitolo_testo'>
                                                                        {$studente.cognome} {$studente.nome}
                                                                    </td>
                                                                    <td align='center'>
                                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                                        {mastercom_auto_select name="mat_tipo_`$temp_id_stud`" value=$studente.tipo_voto_esame_medie_matematica}
                                                                        1###problemi articolati su una o più richieste@@@
                                                                        2###Quesiti a risposta aperta
                                                                        {/mastercom_auto_select}
                                                                    </td>
                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        <input type='text' value="{$studente.voto_esame_medie_matematica}" name="mat_{$studente.id_studente}" size="2">
                                                                    </td>

                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        <input type="button"
                                                                               name="mod_giudizio_prove_scritte_mat_scuole_medie_{$studente.id_studente}"
                                                                               value="+"
                                                                               onclick="javascript:openPopupGiudizio({$studente.id_studente}, 'giudizio_prove_scritte_mat_scuole_medie', 'popup_mat');">
                                                                         <input type="hidden"
                                                                                name="giudizio_prove_scritte_mat_scuole_medie_{$studente.id_studente}"
                                                                                value="{$studente.giudizio_prove_scritte_mat_scuole_medie}">
                                                                    </td>
                                                                </tr>
                                                            {/foreach}
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                    <div id='scheda_inglese' class='schede_esami_scritti' style='display:none;'>
                                                        <table width='100%' style='display:none;border-collapse: separate; padding:5px;' extra_tag='risultati'>
                                                            <thead>
                                                            <tr class='es_sfondo_esami_scuro' >
                                                                <td align='center' rowspan='2' style='width: 5%;' class='es_titolo_colonna'>
                                                                    N&deg;
                                                                </td>
                                                                <td align='center' rowspan='2' style='width: 35%;' class='es_titolo_colonna'>
                                                                    STUDENTE
                                                                </td>

                                                                <td align='center' style='width: 22.5%; height: 25px;' class='es_titolo_colonna'>
                                                                    LINGUA INGLESE
                                                                </td>
                                                                <td align='center' style='width: 22.5%;' class='es_titolo_colonna'>
                                                                    {if $valle_aosta_abilitata == 'SI'}
                                                                        LINGUA FRANCESE
                                                                    {else}
                                                                        SECONDA LINGUA COMUNITARIA
                                                                    {/if}
                                                                </td>
                                                                <td align='center' rowspan="2" style='width: 5%;' class='es_titolo_colonna'>
                                                                    VOTO
                                                                </td>
                                                                <td align='center' rowspan="2" style='width: 15%;' class='es_titolo_colonna'>
                                                                    GIUDIZIO
                                                                </td>
                                                            </tr>
                                                            <tr class='es_sfondo_esami_scuro'>
                                                                <td align='center' style='height: 25px;' class='es_titolo_colonna'>
                                                                    TRACCIA
                                                                </td>
                                                                <td align='center'  class='es_titolo_colonna'>
                                                                    TRACCIA
                                                                </td>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            {foreach from=$dati_studenti item=studente name='test'}
                                                                <tr class='{cycle values="es_sfondo_esami_chiaro,es_sfondo_esami_scuro"}'>
                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        {$studente.registro}
                                                                    </td>
                                                                    <td class='sottotitolo_testo'>
                                                                        {$studente.cognome} {$studente.nome}
                                                                    </td>
                                                                    <td align='center'>
                                                                        <div class="select_tipo_voto_esame_medie_inglese" style="display:inline-block;" id="select_tipo_voto_esame_medie_inglese_{$studente.id_studente}"></div>
                                                                        <span class="tooltip">&#9432;<span class="tooltiptext" id="tipo_voto_esame_medie_inglese_{$studente.id_studente}"></span></span>
                                                                        <input type='hidden' name="ing_tipo_{$studente.id_studente}" value='{$studente.tipo_voto_esame_medie_inglese}'>
                                                                    </td>
                                                                    <td align='center'>
                                                                        <div class="select_tipo_voto_esame_medie_seconda_lingua" style="display:inline-block;" id="select_tipo_voto_esame_medie_seconda_lingua_{$studente.id_studente}"></div>
                                                                        <span class="tooltip">&#9432;<span class="tooltiptext" id="tipo_voto_esame_medie_seconda_lingua_{$studente.id_studente}"></span></span>
                                                                        <input type='hidden' name="sec_lin_tipo_{$studente.id_studente}" value='{$studente.tipo_voto_esame_medie_seconda_lingua}'>
                                                                    </td>
                                                                                                                            
                                                                    {if $valle_aosta_abilitata == 'SI'}
                                                                        <td align='center' class='sottotitolo_testo'>
                                                                            Inglese: <input type='text' value="{$studente.voto_esame_medie_inglese}" name="ing_{$studente.id_studente}" size="2">
                                                                            <br>
                                                                            Francese: <input type='text' value="{$studente.voto_esame_medie_seconda_lingua}" name="sec_lin_{$studente.id_studente}" size="2">
                                                                        </td>
                                                                    {else}
                                                                        <td align='center' class='sottotitolo_testo'>
                                                                            <input type='text' value="{$studente.voto_esame_medie_inglese}" name="ing_{$studente.id_studente}" size="2">
                                                                        </td>
                                                                    {/if}


                                                                    <td align='center' class='sottotitolo_testo'>
                                                                        <input type="button"
                                                                               name="mod_giudizio_prove_scritte_ing_scuole_medie_{$studente.id_studente}"
                                                                               value="+"
                                                                               onclick="javascript:openPopupGiudizio({$studente.id_studente}, 'giudizio_prove_scritte_ing_scuole_medie', 'popup_eng');">
                                                                         <input type="hidden"
                                                                                name="giudizio_prove_scritte_ing_scuole_medie_{$studente.id_studente}"
                                                                                value="{$studente.giudizio_prove_scritte_ing_scuole_medie}">
                                                                    </td>
                                                                </tr>
                                                            {/foreach}
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                    <div>
                                                        <div colspan='14' align='center'>
                                                            <input type='submit' name='bottone' value='Salva'>
                                                            <input type='hidden' name='form_folder' value='risultati'>
                                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                            <input type='hidden' name='stato_secondario' value='risultati_esami_scuole_medie_update'>
                                                            <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                            <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                            <input type='hidden' name='classe' value='{$classe}'>
                                                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                                        </div>
                                                    </div>
                                                </form>
                                                </td>
                                            </tr>

                                        </table>
                                    {else}
                                        <table width='100%' style='display:none;border-collapse: separate; padding:5px;' extra_tag='risultati'>
                                            {* {{{ *}
                                            <form method='post' action='{$SCRIPT_NAME}'>
                                                <tr class='es_sfondo_esami_scuro'>
                                                    <td colspan='15' align='center' class='titolo_funzione'>
                                                        INSERIMENTO DATI ESAMI SCRITTI
                                                    </td>
                                                </tr>
                                                <tr class='es_sfondo_esami_scuro'>
                                                    <td align='center' rowspan='2' class='es_titolo_colonna'>
                                                        N&deg;
                                                    </td>
                                                    <td align='center' rowspan='2' class='es_titolo_colonna'>
                                                        STUDENTE
                                                    </td>
                                                    <td align='center' colspan='2' class='es_titolo_colonna'>
                                                        ITALIANO
                                                    </td>
                                                    {if $anno_inizio < '2017'}
                                                        <td align='center' colspan='2' class='es_titolo_colonna'>
                                                            INGLESE
                                                        </td>
                                                    {/if}
                                                    <td align='center' colspan='2' class='es_titolo_colonna'>
                                                        MATEMATICA
                                                    </td>
                                                    {if $anno_inizio < '2021'}
                                                        {if $anno_inizio < '2017'}
                                                            <td align='center' colspan='2' class='es_titolo_colonna'>
                                                                2° LINGUA
                                                            </td>
                                                        {else}
                                                            <td align='center' colspan='2' class='es_titolo_colonna'>
                                                                LINGUE STRANIERE
                                                            </td>
                                                        {/if}
                                                        {if $anno_inizio < '2017'}
                                                            <td align='center' colspan="1" class='es_titolo_colonna'>
                                                                INVALSI
                                                            </td>
                                                        {/if}
                                                    {/if}
                                                    <td align='center' rowspan="2" class='es_titolo_colonna'>
                                                        GIUDIZIO
                                                    </td>
                                                </tr>
                                                <tr class='es_sfondo_esami_scuro'>
                                                    <td align='center' class='es_titolo_colonna'>
                                                        TIPO
                                                    </td>
                                                    <td align='center' class='es_titolo_colonna'>
                                                        VOTO
                                                    </td>
                                                    {if $anno_inizio < '2017'}
                                                        <td align='center' class='es_titolo_colonna'>
                                                            TIPO
                                                        </td>
                                                        <td align='center' class='es_titolo_colonna'>
                                                            VOTO
                                                        </td>
                                                    {/if}
                                                    {if $anno_inizio < '2021'}
                                                        <td align='center' class='es_titolo_colonna'>
                                                            N&deg; QUES.
                                                        </td>
                                                    {else}
                                                        <td align='center' class='es_titolo_colonna'>
                                                            N&deg; TRACCIA
                                                        </td>
                                                    {/if}
                                                    <td align='center' class='es_titolo_colonna'>
                                                        VOTO
                                                    </td>
                                                    {if $anno_inizio < '2021'}
                                                        <td align='center' class='es_titolo_colonna'>
                                                            TIPO
                                                        </td>
                                                        <td align='center' class='es_titolo_colonna'>
                                                            VOTO
                                                        </td>
                                                    {/if}
                                                    {if $anno_inizio < '2017'}
                                                        <td align='center' class='es_titolo_colonna'>
                                                            TOTALE
                                                        </td>
                                                    {/if}
                                                    {*<td align='center' class='es_titolo_colonna'>
                                                        MATEMATICA
                                                    </td>
                                                    <td align='center' class='es_titolo_colonna'>
                                                        ESITO
                                                    </td>*}
                                                    {*<td align='center' class='es_titolo_colonna'>
                                                        FINALE
                                                    </td>*}
                                                </tr>
                                                {foreach from=$dati_studenti item=studente name='test'}
                                                <tr class='{cycle values="es_sfondo_esami_chiaro,es_sfondo_esami_scuro"}'>
                                                    <td align='center' class='sottotitolo_testo'>
                                                            {$studente.registro}
                                                        </td>
                                                        <td class='sottotitolo_testo'>
                                                            {$studente.cognome} {$studente.nome}
                                                        </td>
                                                        {if $anno_inizio < '2021'}
                                                            <td align='center'>
                                                                {assign var="temp_id_stud" value=$studente.id_studente}
                                                                {*ATTENZIONE - se si modificano questi valori cambiarli anche in /adm/stampe/scheda_candidato_medie/include_pagina2_scheda_candidato_medie.php*}
                                                                {mastercom_auto_select name="ita_tipo_`$temp_id_stud`" value=$studente.tipo_voto_esame_medie_italiano}
                                                                1###traccia 1@@@
                                                                2###traccia 2@@@
                                                                3###traccia 3@@@
                                                                4###testo argomentativo@@@
                                                                5###testo espositivo (relazione)@@@
                                                                6###testo narrativo o descrittivo (lettera,diario)@@@
                                                                7###traccia di comprensione e sintesi di un testo letterario, divulgativo, scientifico
                                                                {/mastercom_auto_select}
                                                            </td>
                                                        {else}
                                                            <td align='center'>
                                                                {assign var="temp_id_stud" value=$studente.id_studente}
                                                                {*ATTENZIONE - se si modificano questi valori cambiarli anche in /adm/stampe/scheda_candidato_medie/include_pagina2_scheda_candidato_medie.php*}
                                                                {mastercom_auto_select name="ita_tipo_`$temp_id_stud`" value=$studente.tipo_voto_esame_medie_italiano}
                                                                4###testo argomentativo@@@
                                                                6###testo narrativo o descrittivo (lettera,diario)@@@
                                                                7###traccia di comprensione e sintesi di un testo letterario, divulgativo, scientifico
                                                                {/mastercom_auto_select}
                                                            </td>
                                                        {/if}
                                                        <td align='center' class='sottotitolo_testo'>
                                                            <input type='text' value="{$studente.voto_esame_medie_italiano}" name="ita_{$studente.id_studente}" size="2">
                                                        </td>
                                                        {if $anno_inizio < '2017'}
                                                            <td align='center'>
                                                                {mastercom_auto_select name="ing_tipo_`$temp_id_stud`" value=$studente.tipo_voto_esame_medie_inglese}
                                                                1###lettera@@@
                                                                2###comprensione testo@@@
                                                                3###dialogo
                                                                {/mastercom_auto_select}
                                                            </td>
                                                            <td align='center' class='sottotitolo_testo'>
                                                                <input type='text' value="{$studente.voto_esame_medie_inglese}" name="ing_{$studente.id_studente}" size="2">
                                                            </td>
                                                        {/if}
                                                        <td align='center' class='sottotitolo_testo'>
                                                            <input type='text' value="{$studente.numero_quesiti_esame_medie_matematica}" name="mat_n_ques_{$studente.id_studente}" size="2">
                                                        </td>
                                                        <td align='center' class='sottotitolo_testo'>
                                                            <input type='text' value="{$studente.voto_esame_medie_matematica}" name="mat_{$studente.id_studente}" size="2">
                                                        </td>
                                                        {if $anno_inizio < '2021'}
                                                            <td align='center'>
                                                                {*ATTENZIONE - se si modificano questi valori cambiarli anche in /adm/stampe/scheda_candidato_medie/include_pagina2_scheda_candidato_medie.php*}
                                                                {mastercom_auto_select name="sec_lin_tipo_`$temp_id_stud`" value=$studente.tipo_voto_esame_medie_seconda_lingua}
                                                                1###lettera@@@
                                                                2###comprensione testo@@@
                                                                3###dialogo@@@
                                                                4###questionario a risposta chiusa o aperta@@@
                                                                5###completamento di un testo@@@
                                                                6###riordino, riscrittura o trasformazione di un testo@@@
                                                                7###elaborazione di un dialogo@@@
                                                                8###elaborazione di una lettera o mail personale@@@
                                                                9###sintesi di un testo@@@
                                                                10###prova combinata
                                                                {/mastercom_auto_select}
                                                            </td>
                                                            <td align='center' class='sottotitolo_testo'>
                                                                <input type='text' value="{$studente.voto_esame_medie_seconda_lingua}" name="sec_lin_{$studente.id_studente}" size="2">
                                                            </td>
                                                        {/if}
                                                        {if $anno_inizio < '2017'}
                                                            <td align='center' class='sottotitolo_testo'>
                                                                <input type='text' value="{$studente.voto_esame_medie_invalsi_finale}" name="invalsi_finale_{$studente.id_studente}" size="2">
                                                            </td>
                                                        {/if}
                                                        <td align='center' class='sottotitolo_testo'>
                                                            <input type="button"
                                                                   name="mod_giudizio_prove_scritte_scuole_medie_{$studente.id_studente}"
                                                                   value="+"
                                                                   onclick="javascript:openPopupGiudizio({$studente.id_studente}, 'giudizio_prove_scritte_scuole_medie', 'popup_ita');">
                                                             <input type="hidden"
                                                                    name="giudizio_prove_scritte_scuole_medie_{$studente.id_studente}"
                                                                    value="{$studente.giudizio_prove_scritte_scuole_medie}">
                                                        </td>

                                                    </tr>
                                                {/foreach}
                                                <tr class='es_sfondo_esami_scuro'>
                                                    <td colspan='14' align='center'>
                                                        <input type='submit' name='bottone' value='Salva'>
                                                        <input type='hidden' name='form_folder' value='risultati'>
                                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                        <input type='hidden' name='stato_secondario' value='risultati_esami_scuole_medie_update'>
                                                        <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                        <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                        <input type='hidden' name='classe' value='{$classe}'>
                                                        <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </td>
                                            </form>
                                            </tr>
                                            {* }}} *}
                                        </table>
                                    {/if}
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento_orali == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='risultati_orali'>
                                        {* {{{ *}
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            <tr class='es_sfondo_esami_scuro'>
                                                <td colspan='6' align='center' class='titolo_funzione'>
                                                    INSERIMENTO DATI COLLOQUI
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_esami_scuro'>
                                                <td align='center' class='es_titolo_colonna'>
                                                    N&deg;
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    STUDENTE
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    VOTO
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    ARGOMENTI
                                                </td>
                                                <td align='center' colspan="2" class='es_titolo_colonna'>
                                                    GIUDIZIO
                                                </td>
                                            </tr>
                                            {foreach from=$dati_studenti item=studente name='test'}
                                                {if $smarty.foreach.test.iteration is even}
                                                        {assign var='sfondo_esami' value='es_sfondo_esami_scuro'}
                                                    {else}
                                                        {assign var='sfondo_esami' value='es_sfondo_esami_chiaro'}
                                                    {/if}
                                                <tr class='{$sfondo_esami}'>
                                                    <td align='center' class='sottotitolo_testo'>
                                                        {$studente.registro}
                                                    </td>
                                                    <td class='sottotitolo_testo'>
                                                        {$studente.cognome} {$studente.nome}
                                                    </td>
                                                    <td align='center' class='sottotitolo_testo'>
                                                        <input type='text' value="{$studente.voto_esame_medie_orale}" name="orale_{$studente.id_studente}" size="2">
                                                    </td>
                                                    <td align='center'>
                                                        <textarea  name="argomenti_orali_{$studente.id_studente}" rows="2" cols="50">{$studente.argomenti_orali_medie}</textarea>
                                                    </td>
                                                    <td align='center' class='sottotitolo_testo'>
                                                        <textarea name="prova_esame_esame_terza_media_{$studente.id_studente}" rows='2' cols='50'>{$studente.prova_esame_esame_terza_media}</textarea>
                                                    </td>
                                                    {*<td align='right'>
                                                        Il candidato ha dimostrato di sapersi orientare:
                                                    </td>
                                                    <td>
                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                        {mastercom_auto_select name="giudizio_1_`$temp_id_stud`" value=$studente.giudizio_1_medie}
                                                        NO###-------------------------------------------------@@@
                                                        difficolta###con qualche difficoltà@@@
                                                        abb_ag###abbastanza agevolmente@@@
                                                        agev###agevolmente@@@
                                                        sicurezza###con sicurezza
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                                <tr class='{$sfondo_esami}'>
                                                    <td align='right'>
                                                        nell'ambito dei temi proposti, di saper esporre e collegare:
                                                    </td>
                                                    <td>
                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                        {mastercom_auto_select name="giudizio_2_`$temp_id_stud`" value=$studente.giudizio_2_medie}
                                                        NO###-------------------------------------------------@@@
                                                        poco_correttamente###poco correttamente@@@
                                                        non_sempre_organico###in modo non sempre organico@@@
                                                        abb_corr###in modo abbastanza corretto@@@
                                                        corr###in modo corretto@@@
                                                        autonomo###in modo autonomo e razionale
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                                <tr class='{$sfondo_esami}'>
                                                    <td align='right'>
                                                        i vari contenuti e di aver acquisito un lessico specifico:
                                                    </td>
                                                    <td>
                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                        {mastercom_auto_select name="giudizio_3_`$temp_id_stud`" value=$studente.giudizio_3_medie}
                                                        NO###-------------------------------------------------@@@
                                                        scarso###scarsamente appropriato@@@
                                                        suff###sufficientemente appropriato@@@
                                                        appr###appropriato@@@
                                                        preciso###preciso e coerente@@@
                                                        approfondito###approfondito
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>*}
                                            {/foreach}
                                            <tr class='es_sfondo_esami_scuro'>
                                                <td colspan='6' align='center'>
                                                    <input type='submit' name='bottone' value='Salva'>
                                                    <input type='hidden' name='form_folder' value='risultati'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='stato_secondario' value='risultati_esami_orali_scuole_medie_update'>
                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                    <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                        </form>
                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}
                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento_finali == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='risultati_finali'>
                                        {* {{{ *}
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            <tr class='es_sfondo_esami_scuro'>
                                                <td colspan='7' align='center' class='titolo_funzione'>
                                                    INSERIMENTO DATI VOTAZIONE FINALE
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_esami_scuro'>
                                                <td align='center' class='es_titolo_colonna'>
                                                    N&deg;
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    STUDENTE
                                                </td>
                                                <td align='center' class='es_titolo_colonna'>
                                                    MEDIA
                                                </td>
                                                <td align='center'  class='es_titolo_colonna'>
                                                    GIUDIZIO DEFINITIVO
                                                </td>
                                                <td align='center' colspan="2" class='es_titolo_colonna'>
                                                    ORIENTAMENTO
                                                </td>
                                            </tr>
                                            {foreach from=$dati_studenti item=studente name='test'}
                                                {if $smarty.foreach.test.iteration is even}
                                                    {assign var='sfondo_esami' value='es_sfondo_esami_scuro'}
                                                {else}
                                                    {assign var='sfondo_esami' value='es_sfondo_esami_chiaro'}
                                                {/if}
                                                <tr class='{$sfondo_esami}'>
                                                    <td align='center' rowspan="" class='sottotitolo_testo'>
                                                        {$studente.registro}
                                                    </td>
                                                    <td rowspan="" class='sottotitolo_testo'>
                                                        {$studente.cognome} {$studente.nome}
                                                    </td>
                                                    <td align='center'>
                                                    {if $valle_aosta_abilitata == 'SI'}
                                                        <input type='text' value="{$studente.giudizio_sintetico_esame_terza_media}" name="giudizio_sintetico_esame_terza_media_{$studente.id_studente}" style="text-align: center; width: 80px; height: 24px; font-size: 14px; border: 1px solid #ccc; border-radius: 3px;">
                                                    {else}
                                                         {if $studente.giudizio_sintetico_esame_terza_media_proposto == '10'}
                                                            <div style="text-align: center; line-height: 1.2; min-width: 100px;">
                                                                {if $studente.giudizio_sintetico_esame_terza_media != '' && $studente.giudizio_sintetico_esame_terza_media != $studente.giudizio_sintetico_esame_terza_media_proposto}
                                                                    <div style="margin-bottom: 3px;">
                                                                        <span style="cursor: help; color: #dc3545; font-size: 14px;"
                                                                              title="⚠️ ATTENZIONE: Il voto ottenuto dalla media calcolata ({$studente.giudizio_sintetico_esame_terza_media_proposto}) è diverso da quello attualmente inserito ({$studente.giudizio_sintetico_esame_terza_media}). Clicca su 'Correggi' per aggiornare la valutazione finale con quella derivata dalla media.">
                                                                            ⚠️
                                                                        </span>
                                                                    </div>
                                                                {/if}
                                                                <div style="margin: 5px 0;">
                                                                    {if $studente.giudizio_sintetico_esame_terza_media != ''}
                                                                        <input type='text' value="{$studente.giudizio_sintetico_esame_terza_media}" name="giudizio_sintetico_esame_terza_media_{$studente.id_studente}" id="input_giudizio_{$studente.id_studente}" style="text-align: center; width: 80px; height: 24px; font-size: 14px; border: 1px solid #ccc; border-radius: 3px;">
                                                                    {else}
                                                                        <input type='text' value="{$studente.giudizio_sintetico_esame_terza_media_proposto}" name="giudizio_sintetico_esame_terza_media_{$studente.id_studente}" id="input_giudizio_{$studente.id_studente}" style="text-align: center; width: 80px; height: 24px; font-size: 14px; border: 1px solid #ccc; border-radius: 3px;">
                                                                    {/if}
                                                                </div>
                                                                {if $studente.giudizio_sintetico_esame_terza_media != '' && $studente.giudizio_sintetico_esame_terza_media != $studente.giudizio_sintetico_esame_terza_media_proposto}
                                                                    <div style="margin-top: 3px;">
                                                                        <button type="button"
                                                                                onclick="copiaGiudizioSinteticoInput('{$studente.id_studente}', '{$studente.giudizio_sintetico_esame_terza_media_proposto}', this)"
                                                                                style="padding: 2px 6px; background-color: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 10px;">
                                                                            Correggi
                                                                        </button>
                                                                    </div>
                                                                {/if}
                                                            </div>
                                                        {elseif $studente.giudizio_sintetico_esame_terza_media == $studente.giudizio_sintetico_esame_terza_media_proposto}
                                                            <b>{$studente.giudizio_sintetico_esame_terza_media}</b>
                                                            <input type='hidden' value="{$studente.giudizio_sintetico_esame_terza_media}" name="giudizio_sintetico_esame_terza_media_{$studente.id_studente}" size="3">
                                                        {else}
                                                            <div style="text-align: center; line-height: 1.2; min-width: 100px;">
                                                                <div style="margin-bottom: 3px;">
                                                                    <span class="tooltip-instant" style="cursor: help; color: #dc3545; font-size: 14px;"
                                                                              title="⚠️ ATTENZIONE: Il voto ottenuto dalla media calcolata ({$studente.giudizio_sintetico_esame_terza_media_proposto}) è diverso da quello attualmente inserito ({$studente.giudizio_sintetico_esame_terza_media}). Clicca su 'Correggi' per aggiornare la valutazione finale con quella derivata dalla media.">
                                                                        ⚠️
                                                                    </span>
                                                                </div>

<style>
.tooltip-instant {
    position: relative;
}

.tooltip-instant:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    white-space: nowrap;
    z-index: 1000;
    font-size: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    transition: opacity 0s;
    opacity: 1;
}

.tooltip-instant:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    border: 5px solid transparent;
    border-top-color: #333;
    z-index: 1001;
}

/* Nasconde il tooltip nativo del browser */
.tooltip-instant[title]:hover:after {
    content: attr(title);
}

.tooltip-instant[title] {
    position: relative;
}

.tooltip-instant[title]:hover {
    overflow: visible;
}
</style>
                                                                <div style="margin: 5px 0;">
                                                                    <b id="giudizio_finale_{$studente.id_studente}">{$studente.giudizio_sintetico_esame_terza_media}</b>
                                                                </div>
                                                                <div style="margin-top: 3px;">
                                                                    <button type="button"
                                                                            onclick="copiaGiudizioSintetico('{$studente.id_studente}', '{$studente.giudizio_sintetico_esame_terza_media_proposto}', this)"
                                                                            style="padding: 2px 6px; background-color: #dc3545; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 10px;">
                                                                        Correggi
                                                                    </button>
                                                                </div>
                                                            </div>
                                                            <input type='hidden' value="{$studente.giudizio_sintetico_esame_terza_media}" name="giudizio_sintetico_esame_terza_media_{$studente.id_studente}" id="hidden_giudizio_{$studente.id_studente}" size="3">
                                                        {/if}
                                                    {/if}
                                                    </td>
                                                    <td align='center' class='sottotitolo_testo'>
                                                        <textarea name="giudizio_descrittivo_finale_esame_terza_media_{$studente.id_studente}" rows='3' cols='40'>{$studente.giudizio_descrittivo_finale_esame_terza_media}</textarea>
                                                    </td>
                                                    {* <td align='right'>
                                                        Dall'andamento del triennio, dalle prove scritte e dal colloquio pluridisciplinare è accertato che il candidato ha conseguito un grado di preparazione complessivo:
                                                    </td>
                                                    <td>
                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                        {mastercom_auto_select name="giudizio_finale_1_`$temp_id_stud`" value=$studente.giudizio_finale_1_medie}
                                                        NO###-------------------------------------------------@@@
                                                        accettabile###accettabile@@@
                                                        abb_sodd###abbastanza soddisfacente@@@
                                                        soddisfacente###soddisfacente@@@
                                                        m_sodd###molto soddisfacente
                                                        {/mastercom_auto_select}
                                                    </td>*}
                                                    <td align='right'>
                                                        Si consiglia/conferma la frequenza di:
                                                    </td>
                                                    {if $valle_aosta_abilitataaa == 'SI'}
                                                        <td>
                                                            {assign var="temp_id_stud" value=$studente.id_studente}
                                                            {mastercom_auto_select name="consiglio_terza_media_`$temp_id_stud`" value=$studente.consiglio_terza_media}
                                                            NO###-------------------------------------------------@@@
                                                            lic_cla###Liceo classico@@@
                                                            lic_art###Liceo artistico@@@
                                                            lic_mus###Liceo musicale@@@
                                                            lic_sci_ling###Liceo scientifico e linguistico@@@
                                                            lic_su###Liceo scienze umane e scientifico@@@
                                                            ist_tec###Istituzione scolastica di istruzione tecnica@@@
                                                            tec_prof###Istituto tecnico e professionale regionale@@@
                                                            lic_tec###Istituzione scolastica di istruzione liceale e tecnica@@@
                                                            prof_agr###Istituto tecnico professionale agrario@@@
                                                            prof_alb###Istituto professionale regionale alberghiero@@@
                                                            prof_indru###Istituto professionale industria e artigianato
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    {else}
                                                        {*<td>
                                                            {assign var="temp_id_stud" value=$studente.id_studente}
                                                            {mastercom_auto_select name="consiglio_terza_media_`$temp_id_stud`" value=$studente.consiglio_terza_media}
                                                            NO###-------------------------------------------------@@@
                                                            prof###un istituto professionale@@@
                                                            itis###un istituto tecnico@@@
                                                            liceo###un liceo@@@
                                                            profess###un corso di formazione professionale
                                                            {/mastercom_auto_select}
                                                        </td>*}
                                                        {if $anno_inizio < '2024'}
                                                            <td rowspan="" class='sottotitolo_testo'>
                                                                <div style="vertical-align: middle;">
                                                                    <div class="select_orientamento" style="display:inline-block;" id="select_orientamento_{$studente.id_studente}">
                                                                    </div>
                                                                    <span class="tooltip">&#9432;<span class="tooltiptext" id="orientamento_studente_{$studente.id_studente}"></span>
                                                                    </span>
                                                                </div>

                                                                <input type='hidden' name="consiglio_orientativo_trentino_{$studente.id_studente}" value='{$studente.consiglio_orientativo_trentino}'>
                                                            </td>
                                                        {else}
                                                            <td>
                                                                <select name="consiglio_orientativo_ministeriale_{$studente.id_studente}">
                                                                    <option value="0"> --- </option>
                                                                    {foreach from=$elenco_consigli item=consiglio_ministeriale}
                                                                        <option value="{$consiglio_ministeriale.id_consiglio_orientativo_template}" {if $studente.id_consiglio_orientativo == $consiglio_ministeriale.id_consiglio_orientativo_template}selected{/if}>{$consiglio_ministeriale.descrizione}</option>
                                                                    {/foreach}
                                                                </select>
                                                                {*<input type='hidden' name="consiglio_orientativo_ministeriale_{$studente.id_studente}" value='{$studente.id_consiglio_orientativo}'>*}
                                                            </td>
                                                        {/if}
                                                    {/if}
                                                </tr>
                                                {*<tr class='{$sfondo_esami}'>
                                                    <td align='right'>
                                                        e che la capacità di organizzazione e di rielaborazione delle conoscenze acquisite è:
                                                    </td>
                                                    <td>
                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                        {mastercom_auto_select name="giudizio_finale_2_`$temp_id_stud`" value=$studente.giudizio_finale_2_medie}
                                                        NO###-------------------------------------------------@@@
                                                        scarsa###scarsa@@@
                                                        quasi_suff###quasi sufficiente@@@
                                                        sufficiente###sufficiente@@@
                                                        buona###buona@@@
                                                        molto_buona###molto buona
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                                <tr class='{$sfondo_esami}'>
                                                    <td align='right'>
                                                        Il livello di maturazione globale risulta:
                                                    </td>
                                                    <td>
                                                        {assign var="temp_id_stud" value=$studente.id_studente}
                                                        {mastercom_auto_select name="giudizio_finale_3_`$temp_id_stud`" value=$studente.giudizio_finale_3_medie}
                                                        NO###-------------------------------------------------@@@
                                                        m_positivo###molto positivo@@@
                                                        positivo###positivo@@@
                                                        p_suff###pienamente sufficiente@@@
                                                        adeguato###adeguato ai livelli di partenza@@@
                                                        n_d_a###non del tutto adeguato
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>*}
                                            {/foreach}
                                            <tr class='es_sfondo_esami_scuro'>
                                                <td colspan='7' align='center'>
                                                    <input type='submit' name='bottone' value='Salva'>
                                                    <input type='hidden' name='form_folder' value='risultati'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='stato_secondario' value='risultati_finali_esami_scuole_medie_update'>
                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                    <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                        </form>
                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}


                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento == 'SI'}
                                    <table width='100%' class="" style='display:none;border-collapse: separate;' extra_tag='esami_2021'>
                                        {* {{{ *}
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            <thead>
                                            <tr class='sfondo_viola'>
                                                <td colspan='9' align='center' class='titolo_funzione testo_bianco' style="height: 40px; font-size: 22px;">
                                                    Dati Esami
                                                </td>
                                            </tr>
                                            <tr class='sfondo_viola'>
                                                <td align='center' class='es_titolo_colonna_2 testo_bianco'>
                                                    N&deg;
                                                </td>
                                                <td align='center' class='es_titolo_colonna_2 testo_bianco'>
                                                    Studente
                                                </td>
                                                <td align='center' class='es_titolo_colonna_2 testo_bianco'>
                                                    Voto Ammissione
                                                </td>
                                                <td align='center'  class='es_titolo_colonna_2 testo_bianco'>
                                                    Elaborato
                                                </td>
                                                <td align='center' class='es_titolo_colonna_2 testo_bianco'>
                                                    Giudizio prova Esame
                                                </td>
                                                <td align='center'  class='es_titolo_colonna_2 testo_bianco'>
                                                    Giudizio descrittivo Finale
                                                </td>
                                                <td align='center' class='es_titolo_colonna_2 testo_bianco'>
                                                    Voto Orale
                                                </td>
                                                <td align='center' class='es_titolo_colonna_2 testo_bianco'>
                                                    Voto Finale
                                                </td>
                                                <td align='center' class='es_titolo_colonna_2 testo_bianco'>
                                                    Orientamento
                                                </td>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {foreach from=$dati_studenti item=studente name='test'}
                                                {if $smarty.foreach.test.iteration is even}
                                                    {assign var='sfondo_esami' value='sfondo_viola_op20'}
                                                {else}
                                                    {assign var='sfondo_esami' value=''}
                                                {/if}
                                                <tr class='{$sfondo_esami}'>
                                                    <td align='center' rowspan="" class='sottotitolo_testo'>
                                                        {$studente.registro}
                                                    </td>
                                                    <td rowspan="" class='sottotitolo_testo'>
                                                        {$studente.cognome} {$studente.nome}
                                                    </td>
                                                    <td rowspan="" class='sottotitolo_testo' align="center">
                                                        {$studente.voto_ammissione_medie}
                                                    </td>
                                                    <td rowspan="" class='sottotitolo_testo'>
                                                        <textarea name="elaborato_studente_esame_terza_media_{$studente.id_studente}" rows='2' cols='20'>{$studente.elaborato_studente_esame_terza_media}</textarea>
                                                    </td>
                                                    <td rowspan="" class='sottotitolo_testo'>
                                                        <textarea name="prova_esame_esame_terza_media_{$studente.id_studente}" rows='2' cols='20'>{$studente.prova_esame_esame_terza_media}</textarea>
                                                    </td>
                                                    <td rowspan="" class='sottotitolo_testo'>
                                                        <textarea name="giudizio_descrittivo_finale_esame_terza_media_{$studente.id_studente}" rows='2' cols='20'>{$studente.giudizio_descrittivo_finale_esame_terza_media}</textarea>
                                                    </td>

                                                    <td rowspan="" class='sottotitolo_testo' align="center">
                                                        <input type="number" min="0" max="10" value="{$studente.voto_esame_medie_orale}" name="orale_{$studente.id_studente}" style="width: 80px; height: 24px; text-align: center; font-size: 14px; border: 1px solid #ccc; border-radius: 3px;">
                                                    </td>

                                                    <td rowspan="" class='sottotitolo_testo' align="center">
                                                        <b>{$studente.giudizio_sintetico_esame_terza_media}</b>
                                                        <input type='hidden' value="{$studente.giudizio_sintetico_esame_terza_media}" name="giudizio_sintetico_esame_terza_media_{$studente.id_studente}">
                                                    </td>

                                                    <td rowspan="" class='sottotitolo_testo'>
                                                        <div style="vertical-align: middle;">
                                                            <div class="select_orientamento" style="display:inline-block;" id="select_orientamento_{$studente.id_studente}">
                                                            </div>
                                                            <span class="tooltip">&#9432;<span class="tooltiptext" id="orientamento_studente_{$studente.id_studente}"></span>
                                                            </span>
                                                        </div>

                                                        <input type='hidden' name="consiglio_orientativo_trentino_{$studente.id_studente}" value='{$studente.consiglio_orientativo_trentino}'>
                                                    </td>

                                            {/foreach}
                                            </tbody>
                                            <tfoot>
                                            <tr class='padding_cella_generica'>
                                                <td colspan='9' align='center' class="padding8">
                                                    <input type='submit' name='bottone' class="btn_pieno sfondo_verde" value='Salva'>
                                                    <input type='hidden' name='form_folder' value='esami_2021'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='stato_secondario' value='esami_2021_scuole_medie_update'>
                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                    <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                            </tfoot>
                                        </form>
                                        </tr>
                                        {* }}} *}
                                    </table>
                                {/if}


                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_pubblicazione == 'SI'}
                                    <table width='100%' style='display:none;border-collapse: separate;' extra_tag='pubb'>
                                        {* {{{ *}
                                        <tr class='es_sfondo_pubblicazione'>
                                            <td align='center' colspan=2 class='titolo_funzione'>
                                                PUBBLICAZIONE DATI PROVE D&#039;ESAME
                                            </td>
                                        </tr>
                                        <form method='post' action='{$SCRIPT_NAME}'>
                                            {if $anno_inizio < '2021'}
                                                <tr class='es_sfondo_pubblicazione esame_scritti'>
                                                    <td align='right' width='60%' class='padding_cella_generica'>
                                                        Pubblicazione dati prima prova scritta
                                                    </td>
                                                    <td width='40%' class='padding_cella_generica'>
                                                        {mastercom_auto_select name="pubb_primo_scritto" value=$dati_classe.pubb_primo_scritto}
                                                        NO###Non pubblicare@@@
                                                        SI###Pubblicare
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                                <tr class='es_sfondo_pubblicazione esame_scritti'>
                                                    <td align='right' class='padding_cella_generica'>
                                                        Pubblicazione dati seconda prova scritta
                                                    </td>
                                                    <td class='padding_cella_generica'>
                                                        {mastercom_auto_select name="pubb_secondo_scritto" value=$dati_classe.pubb_secondo_scritto}
                                                        NO###Non pubblicare@@@
                                                        SI###Pubblicare
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                                    <tr class='es_sfondo_pubblicazione esame_scritti'>
                                                        <td align='right' class='padding_cella_generica'>
                                                            Pubblicazione dati terza prova scritta
                                                        </td>
                                                        <td class='padding_cella_generica'>
                                                            {mastercom_auto_select name="pubb_terzo_scritto" value=$dati_classe.pubb_terzo_scritto}
                                                            NO###Non pubblicare@@@
                                                            SI###Pubblicare
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                <tr class='es_sfondo_pubblicazione'>
                                                    <td align='right' class='padding_cella_generica'>
                                                        Pubblicazione dati prova orale
                                                    </td>
                                                    <td class='padding_cella_generica'>
                                                        {mastercom_auto_select name="pubb_orale" value=$dati_classe.pubb_orale}
                                                        NO###Non pubblicare@@@
                                                        SI###Pubblicare
                                                        {/mastercom_auto_select}
                                                    </td>
                                                </tr>
                                            {/if}
                                            <tr class='es_sfondo_pubblicazione'>
                                                <td align='right' class='padding_cella_generica'>
                                                    Aggiorna curriculum studenti
                                                </td>
                                                <td class='padding_cella_generica'>
                                                    {mastercom_auto_select name="agg_curriculum"}
                                                    NO###Non aggiornare@@@
                                                    SI###Aggiornare
                                                    {/mastercom_auto_select}
                                                </td>
                                            </tr>
                                            <tr class='es_sfondo_pubblicazione'>
                                                <td align='center' colspan=2 class='padding_cella_generica'>
                                                    <input type='submit' value='Salva'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='form_folder' value='pubb'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='stato_secondario' value='aggiorna_pubblicazione_voti_commissione'>
                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                    <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </td>
                                            </tr>
                                        </form>
                                        {* }}} *}
                                    </table>
                                {/if}
                            {/if}
                            {*}/}}} *}
                        {/if}
                    {else}
                        <tr>
                            <td width='100%' colspan='2' valign='top'>
                                {if $id_classe > 0}
                                    {*}/{{{ intestazione pulsanti sopra elenco classi *}
                                    <table width='100%' class='sfondo_setup_utenti'>
                                        <tr>
                                            <td colspan='4' class='sottotitolo_testo_bold'>
                                                Esami di stato classe: {$classe} {$indirizzo}
                                            </td>
                                            <td align='right'>
                                                {if $dati_presidente.id_utente == $current_user or $superutente == 'SI'}
                                                    <button onclick="window.open('manuali/Manuale mastercom completo.htm?Gestioneesamidistato.html', 'manuale');"><img src="icone/manuale_scuro24.gif"></button>
                                                    {else}
                                                    <button onclick="window.open('manuali/Manuale mastercom completo.htm?Gestioneesamidistato.html', 'manuale');"><img src="icone/manuale_scuro24.gif"></button>
                                                    {/if}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td colspan=4>
                                                {if $dati_commissione.composizione_approvata == 'SI'}
                                                    {if $dati_presidente.id_utente == $current_user or $superutente == 'SI'}
                                                        <input type='button' id='bottone_folder_ruoli' value='Ruoli commissione' onclick="switch_folder('ruoli');" disabled>
                                                    {else}
                                                        <input type='button' id='bottone_folder_ruoli' value='Ruoli commissione' onclick="switch_folder('ruoli');" style=visibility:hidden;'>
                                                    {/if}
                                                    {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_stampa == 'SI'}
                                                        <input type='button' id='bottone_folder_verbali' value='Verbali' onclick="switch_folder('verbali');">
                                                        <input type='button' id='bottone_folder_stampe' value='Stampe' onclick="switch_folder('stampe');">
                                                        <input type='button' id='bottone_folder_registro' value='Registro Esami' onclick="switch_folder('registro');">
                                                    {else}
                                                        <input type='button' id='bottone_folder_verbali' value='Verbali' onclick="switch_folder('verbali');" style='visibility:hidden;'>
                                                        <input type='button' id='bottone_folder_stampe' value='Stampe' onclick="switch_folder('stampe');" style='visibility:hidden;'>
                                                        <input type='button' id='bottone_folder_registro' value='Registro Esami' onclick="switch_folder('registro');" style='visibility:hidden;'>
                                                    {/if}
                                                    {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento == 'SI'}
                                                        <input type='button' id='bottone_folder_calendario' value='Gestione Dati e Calendario' onclick="switch_folder('calendario');">
                                                    {else}
                                                        <input type='button' id='bottone_folder_calendario' value='Gestione Dati e Calendario' onclick="switch_folder('calendario');" style='visibility:hidden;'>
                                                    {/if}
                                                    {if $dati_presidente.id_utente == $current_user or $superutente == 'SI'}
                                                        <input type='button' id='bottone_folder_pubb' value='Pubblicazione Dati' onclick="switch_folder('pubb');">
                                                    {else}
                                                        <input type='button' id='bottone_folder_pubb' value='Pubblicazione Dati' onclick="switch_folder('pubb');" style='visibility:hidden;'>
                                                    {/if}
                                                {/if}
                                            </td>
                                            <td align='right'>
                                                {if $dati_commissione.composizione_approvata == 'SI'}
                                                    <input type='button' id='bottone_folder_tutti' value='Tutti' onclick="switch_folder('tutti');">
                                                {/if}
                                            </td>
                                        </tr>

                                        {if $dati_presidente.id_utente == $current_user or $superutente == 'SI'}
                                            <table width='100%' extra_tag='ruoli'>
                                                {* {{{ *}
                                                <tr class='es_sfondo_ruoli_commissari'>
                                                    <td>
                                                        Commissario
                                                    </td>
                                                    <td>
                                                        Carica
                                                    </td>
                                                    <td>
                                                        Stampe
                                                    </td>
                                                    <td>
                                                        Inserimento dati (voti e calendario)
                                                    </td>
                                                </tr>
                                                <form method='post' action='{$SCRIPT_NAME}'>
                                                    {foreach from=$elenco_commissari.interni item=professore}
                                                        {if $professore.selezionato == "SI"}
                                                            <tr class='es_sfondo_ruoli_commissari'>
                                                                <td>
                                                                    {$professore.cognome} {$professore.nome}
                                                                </td>
                                                                <td>
                                                                    {mastercom_auto_select name="ruolo_interno_" indice=$professore.id_professore value=$professore.dati_commissario.ruolo_interno}
                                                                    ###Nessuno@@@
                                                                    V###Vicepresidente@@@
                                                                    S###Segretario@@@
                                                                    VS###Vicepresidente e Segretario
                                                                    {/mastercom_auto_select}
                                                                </td>
                                                                <td>
                                                                    {mastercom_auto_select name="permesso_stampa_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_stampa}
                                                                    NO###NO@@@
                                                                    SI###SI
                                                                    {/mastercom_auto_select}
                                                                </td>
                                                                <td>
                                                                    {mastercom_auto_select name="permesso_inserimento_" indice=$professore.id_professore value=$professore.dati_commissario.permesso_inserimento}
                                                                    NO###NO@@@
                                                                    SI###SI
                                                                    {/mastercom_auto_select}
                                                                </td>
                                                            </tr>
                                                        {/if}
                                                    {/foreach}
                                                    {foreach from=$elenco_commissari.esterni item=professore}
                                                        {if $professore.selezionato == "SI" and $dati_presidente.id_utente != $professore.id_utente}
                                                            <tr class='es_sfondo_ruoli_commissari'>
                                                                <td>
                                                                    {$professore.cognome} {$professore.nome}
                                                                </td>
                                                                <td>
                                                                    {mastercom_auto_select name="ruolo_interno_" indice=$professore.id_utente value=$professore.dati_commissario.ruolo_interno}
                                                                    ###Nessuno@@@
                                                                    V###Vicepresidente@@@
                                                                    S###Segretario@@@
                                                                    VS###Vicepresidente e Segretario
                                                                    {/mastercom_auto_select}
                                                                </td>
                                                                <td>
                                                                    {mastercom_auto_select name="permesso_stampa_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_stampa}
                                                                    NO###NO@@@
                                                                    SI###SI
                                                                    {/mastercom_auto_select}
                                                                </td>
                                                                <td>
                                                                    {mastercom_auto_select name="permesso_inserimento_" indice=$professore.id_utente value=$professore.dati_commissario.permesso_inserimento}
                                                                    NO###NO@@@
                                                                    SI###SI
                                                                    {/mastercom_auto_select}
                                                                </td>
                                                            </tr>
                                                        {/if}
                                                    {/foreach}
                                                    <tr class='es_sfondo_ruoli_commissari'>
                                                        <td colspan=4 align='center'>
                                                            <input type='submit' value='Salva'>
                                                        </td>
                                                    </tr>
                                                    <input type='hidden' name='form_folder' value='ruoli'>
                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                    <input type='hidden' name='stato_secondario' value='aggiorna_funzioni_commissione'>
                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                    <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                </form>
                                                {* }}} *}
                                            </table>
                                        {/if}
                                        {if $dati_commissione.composizione_approvata == 'SI'}
                                            {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_stampa == 'SI'}
                                                <table width='100%' style='display:none;' extra_tag='verbali'>
                                                    {* {{{ *}
                                                    <tr class='es_sfondo_verbali'>
                                                        <td colspan='2' align='center' class='sottotitolo_testo'>
                                                            Stampa verbali esami di stato:
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_verbali'>
                                                        <td align='center'>
                                                            Selezionare verbale storico salvato:
                                                        </td>
                                                        <td align='center'>
                                                            Selezionare verbale:
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_verbali'>
                                                    <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                                        <td align='center'>
                                                            {mastercom_auto_select name="tipo_storico_verbale_stato" size='30' array_dati=$mat_storico_verbali_stato ondblclick="
                                                                this.form.action='documenti.php';
                                                                this.form.target='print_window';
                                                                this.form.form_azione.value='carica_storico_documento';
                                                                document.getElementById('hid_form_id_storico_documento').value = this.value;
                                                                this.form.submit();
                                                            "}
                                                            {/mastercom_auto_select}
                                                            <br>
                                                            <input type='button' value='Apri' onclick="
                                                                    this.form.action = 'documenti.php';
                                                                    this.form.target = 'print_window';
                                                                    this.form.form_azione.value = 'carica_storico_documento';
                                                                    document.getElementById('hid_form_id_storico_documento').value = this.form.tipo_storico_verbale_stato.value;
                                                                    this.form.submit();
                                                                   ">
                                                            <input type='button' value='Download' onclick="
                                                                    this.form.action = 'documenti.php';
                                                                    this.form.target = 'print_window';
                                                                    this.form.form_azione.value = 'download_storico_documento';
                                                                    document.getElementById('hid_form_id_storico_documento').value = this.form.tipo_storico_verbale_stato.value;
                                                                    this.form.submit();
                                                                   ">
                                                                <input type='button' value='Elimina' onclick="es_elimina(this,'verbali');">
                                                            <input type='hidden' id='hid_form_id_storico_documento' name='form_id_storico_documento' value=''>
                                                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                            <input type='hidden' name='form_azione' value=''>
                                                            <input type='hidden' name='form_folder' value='{$form_folder}'>
                                                            <input type='hidden' name='mat_classi[]' value='{$id_classe}'>
                                                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                                        </td>
                                                    </form>
                                                    <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                                        <td align='center'>
                                                            {mastercom_auto_select name="tipo_verbale_stato" size='30' array_dati=$mat_verbali_stato ondblclick="
                                                                this.form.action='documenti.php';
                                                                this.form.target='print_window';
                                                                document.getElementById('hid_form_id_modello_documento').value = this.value;
                                                                this.form.submit();
                                                            "}
                                                            {/mastercom_auto_select}
                                                            <br>
                                                            <input type='button' value='Apri' onclick="
                                                                    this.form.action = 'documenti.php';
                                                                    this.form.target = 'print_window';
                                                                    document.getElementById('hid_form_id_modello_documento').value = this.form.tipo_verbale_stato.value;
                                                                    this.form.submit();
                                                                   ">
                                                            <input type='hidden' id='hid_form_id_modello_documento' name='form_id_modello_documento' value=''>
                                                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                            <input type='hidden' name='form_azione' value='genera_documento'>
                                                            <input type='hidden' name='form_folder' value='{$form_folder}'>
                                                            <input type='hidden' name='mat_classi[]' value='{$id_classe}'>
                                                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                                        </td>
                                                    </form>
                                                    <form name="form_reload">
                                                        <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                        <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                        <input type='hidden' name='stato_secondario' value=''>
                                                        <input type='hidden' name='form_folder' value=''>
                                                        <input type='hidden' name='form_id_storico_documento' value=''>
                                                        <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                        <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                        <input type='hidden' name='classe' value='{$classe}'>
                                                        <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                        <input type='hidden' name='id_classe_selezionata' value='{$id_classe}'>
                                                        <input type='hidden' name='current_user' value='{$current_user}'>
                                                        <input type='hidden' name='current_key' value='{$current_key}'>
                                                    </form>
                                                    </tr>
                                                    {* }}} *}
                                                </table>
                                                <table width='100%' style='display:none;' extra_tag='stampe'>
                                                    {* {{{ *}
                                                    <tr class='es_sfondo_stampe'>
                                                        <td colspan='4' align='center' class='sottotitolo_testo'>
                                                            Stampa schede personali del candidato
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                    <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                                        <td align='right'>
                                                            Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                                                        </td>
                                                        <td>
                                                            <input type='text' name='dimensione_font' value='8' size='3'>
                                                        </td>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare solo la prima parte, l'ultima o tutta la scheda:
                                                        </td>
                                                        <td>
                                                            {mastercom_auto_select name="stampa_parte" value=$stampa_parte}
                                                            PRIMA###Solo la prima@@@
                                                            ULTIMA###Solo l'ultima@@@
                                                            TUTTO###Tutta la scheda
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare il giudizio di ammissione all'esame:
                                                        </td>
                                                        <td>
                                                            {mastercom_auto_select name="stampa_giudizio_ammissione" value=$stampa_giudizio_ammissione}
                                                            SI###SI@@@
                                                            NO###NO
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare il curriculum:
                                                        </td>
                                                        <td>
                                                            {mastercom_auto_select name="stampa_curriculum" value=$stampa_curriculum}
                                                            SI###SI@@@
                                                            NO###NO
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare la colonna della media voti della pagellina di fine anno (N.B. il valore verrà compilato solo se nel sistema sono presenti tutti i dati necessari):
                                                        </td>
                                                        <td>
                                                            {mastercom_auto_select name="stampa_media_curriculum" value=$stampa_media_curriculum}
                                                            NO###NO@@@
                                                            SI###SI
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare le integrazioni dei crediti separate dai crediti stessi:
                                                        </td>
                                                        <td >
                                                            {mastercom_auto_select name="visualizza_crediti_estesi" value=$visualizza_crediti_estesi}
                                                            SI###SI@@@
                                                            NO###NO
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare la sezione dei debiti:
                                                        </td>
                                                        <td >
                                                            {mastercom_auto_select name="stampa_debiti" value=$stampa_debiti}
                                                            SI###SI@@@
                                                            NO###NO
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare, alla fine, la firma del dirigente scolastico
                                                        </td>
                                                        <td >
                                                            {mastercom_auto_select name="stampa_firma_dirigente" value=$stampa_firma_dirigente}
                                                            NO###NO@@@
                                                            SI###SI
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='right'>
                                                            Selezionare la pagina in cui si desidera stampare la firma del candidato per il colloquio:
                                                        </td>
                                                        <td width='25%'>
                                                            {mastercom_auto_select name="stampa_firma_colloquio" value=$stampa_firma_colloquio}
                                                            prima_pagina###Prima Pagina@@@
                                                            terza_pagina###Terza Pagina
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare i dati del documento di identità del candidato:
                                                        </td>
                                                        <td width='25%'>
                                                            {mastercom_auto_select name="stampa_documento_identita" value=$stampa_documento_identita}
                                                            SI###SI@@@
                                                            NO###NO
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='right'>
                                                            Selezionare se si desidera filtrare il curriculum:
                                                        </td>
                                                        <td>
                                                            <input type="checkbox" name="no_iscritto" value="1" checked>Eliminare le righe di iscrizione
                                                        </td>
                                                        <td colspan='2'></td>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='right'></td>
                                                        <td>
                                                            <input type="checkbox" name="no_giudizio_sospeso" value="1" checked>Eliminare le righe di giudizio sospeso
                                                        </td>
                                                        <td colspan='2'></td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td colspan='2'></td>
                                                        <td align='right'>
                                                            Selezionare se si desidera stampare la firma del segreterio del CDC dopoil curriculum:
                                                        </td>
                                                        <td width='25%'>
                                                            {mastercom_auto_select name="stampa_firma_segretario_CDC"}
                                                            NO###NO@@@
                                                            SI###SI
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='right'>
                                                            Selezionare se si desidera la riga di religione nei dati dello studente:
                                                        </td>
                                                        <td width='25%'>
                                                            {mastercom_auto_select name="riga_religione"}
                                                            SI###SI@@@
                                                            NO###NO
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        <td align='right'>
                                                            Selezionare se si desidera la riga del "voto diploma medie" nei dati dello studente:
                                                        </td>
                                                        <td width='25%'>
                                                            {mastercom_auto_select name="riga_voto_diploma_medie"}
                                                            SI###SI@@@
                                                            NO###NO
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                        <td align='center' colspan='4'>
                                                            <input type='submit' name='bottone' value='Stampa'>
                                                            <input type='hidden' name='form_folder' value='stampe'>
                                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                            <input type='hidden' name='stato_secondario' value='stampa_scheda_personale_candidato'>
                                                            <input type='hidden' name='form_target' value='blank'>
                                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                                        </td>
                                                     </form>
                                                    </tr>
                                                    {* }}} *}
                                                </table>
                                                <table width='100%' style='display:none;border-collapse: separate;' extra_tag='stampe'>
                                                    {* {{{ *}
                                                    <tr class='es_sfondo_stampe'>
                                                        <td colspan='4' align='center' class='sottotitolo_testo'>
                                                            Stampe necessarie per gli Esami di Stato
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_stampe'>
                                                    <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                                        <td colspan='2' align='right'>
                                                            Selezionare il tipo di documento che si vuole stampare:
                                                        </td>
                                                        <td colspan='2'>
                                                            <SELECT name="tipo_documento">
                                                                <OPTION value="elenco_candidati">Elenco Candidati</OPTION>
                                                                <OPTION value="elenco_candidati_per_documento">Elenco Candidati per documento di riconoscimento</OPTION>
                                                                {if $anno_scolastico != '2020/2021'}
                                                                <OPTION value="elenco_prove_scritte">Elenco Candidati per prove scritte</OPTION>
                                                                {/if}
                                                                <OPTION value="elenco_prove_orali">Elenco Candidati per prove orali</OPTION>
                                                                <OPTION value="elenco_candidati_con_crediti">Elenco totale Candidati con crediti scolastici</OPTION>
                                                                <OPTION value="elenco_candidati_interni_con_crediti">Elenco Candidati Interni con crediti scolastici</OPTION>
                                                                <OPTION value="elenco_candidati_esterni_con_crediti">Elenco Candidati Esterni con crediti scolastici</OPTION>
                                                                {if $anno_scolastico != '2020/2021'}
                                                                <OPTION value="elenco_risultati_prove_scritte">Elenco Risultati Prove Scritte</OPTION>
                                                                {/if}
                                                                <OPTION value="elenco_risultati_finali">Elenco Risultati Finali</OPTION>
                                                                <OPTION value="elenco_risultati_finali_solo_esito">Elenco Risultati Finali con il solo esito</OPTION>
                                                                <OPTION value="dichiarazione_commissari">Dichiarazioni dei Commissari di non relazione con Candidati</OPTION>
                                                                <OPTION value="registro_esami_stato">Registro degli Esami di Stato</OPTION>
                                                                <OPTION value="stampa_calendari_orali">Stampa del calendario degli esami orali</OPTION>
                                                                <OPTION value="riepilogo_calendari_orali">Stampa del riepilogo del calendario degli esami orali</OPTION>
                                                                {if $anno_scolastico != '2020/2021'}
                                                                <OPTION value="elenco_prove_scritte_per_studente">Stampa risultati degli scritti per singolo studente</OPTION>
                                                                <OPTION value="tabellone_risultati_esposizione">Stampa il tabellone dei risultati delle prove scritte per esposizione</OPTION>
                                                                {/if}
                                                            </SELECT>
                                                        </td>
                                                        </tr>
                                                        <tr class='es_sfondo_stampe'>
                                                            <td align='right'>
                                                                Digitare la dimensione del font da utilizzare (numero compreso tra 6 e 20):
                                                            </td>
                                                            <td>
                                                                <input type='text' name='dimensione_font' value='8' size='3'>
                                                            </td>
                                                            <td align='right'>
                                                                Selezionare per le stampe che lo necessitano il tipo di prova scritta:
                                                            </td>
                                                             {if $anno_inizio < '2018'}
                                                            <td>
                                                                <SELECT name="tipo_prova_selezionato">
                                                                    <OPTION selected value="GENERICO">Prove scritte generiche</OPTION>
                                                                    <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                                    <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                                    <OPTION value="TERZA">Terza prova scritta</OPTION>
                                                                </SELECT>
                                                            </td>
                                                            {elseif $anno_inizio == '2019'}
                                                            <td>
                                                                <SELECT name="tipo_prova_selezionato">
                                                                    <OPTION selected value="NESSUNA"> --- </OPTION>
                                                                    <OPTION value="GENERICO">Prove scritte generiche</OPTION>
                                                                    <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                                    <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                                </SELECT>
                                                            </td>
                                                            {elseif $anno_inizio == '2020'}
                                                            <SELECT name="tipo_prova_selezionato">
                                                                <OPTION selected value="NESSUNA"> --- </OPTION>
                                                                <OPTION value="GENERICO">Prove scritte generiche</OPTION>
                                                                <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                                <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                            </SELECT>
                                                            {else}
                                                            <td>
                                                                <SELECT name="tipo_prova_selezionato">
                                                                    <OPTION selected value="GENERICO">Prove scritte generiche</OPTION>
                                                                    <OPTION value="PRIMA">Prima prova scritta</OPTION>
                                                                    <OPTION value="SECONDA">Seconda prova scritta</OPTION>
                                                                </SELECT>
                                                            </td>
                                                            {/if}
                                                        </tr>
                                                        <tr class='es_sfondo_stampe'>
                                                            <td align='center' colspan='4'>
                                                                <input type='submit' name='bottone' value='Stampa'>
                                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                                <input type='hidden' name='stato_secondario' value='stampa_elenchi_particolari_update'>
                                                                <input type='hidden' name='tipo_stampa' value='{$tipo_stampa}'>
                                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                                <input type='hidden' name='classe' value='{$classe}'>
                                                                <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                                <input type='hidden' name='form_folder' value='stampe'>
                                                                <input type='hidden' name='form_target' value='blank'>
                                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                                            </td>
                                                    </form>
                                                    </tr>
                                                    {* }}} *}
                                                </table>
                                                <table width='100%' style='display:none;border-collapse: separate;' extra_tag='registro'>
                                                    {* {{{ *}
                                                    <tr class='es_sfondo_registro'>
                                                        <td colspan='4' align='center'>
                                                            Stampa registro esami di stato in carta libera
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_registro'>
                                                    <form method='post' action='{$SCRIPT_NAME}' target='_blank'>
                                                        <td align='right'>
                                                            Selezionare il formato della carta da utilizzare:
                                                        </td>
                                                        <td>
                                                            <SELECT name="formato_pagina_selezionato">
                                                                <OPTION selected value="A4">A4</OPTION>
                                                                <OPTION value="A3">A3</OPTION>
                                                                <OPTION value="PERSONALIZZATO">Personalizzato</OPTION>
                                                            </SELECT>
                                                        </td>
                                                        <td align='right'>
                                                            Digitare la dimensione del font da utilizzare (numero compreso tra 4 e 7):
                                                        </td>
                                                        <td>
                                                            <input type='text' name='dimensione_font' value='5' size='3'>
                                                        </td>
                                                        </tr>
                                                        <tr class='es_sfondo_registro'>
                                                            <td align='right'>
                                                                Nel caso di formato pagina personalizzato selezionare l'altezza del foglio:
                                                            </td>
                                                            <td>
                                                                <input type='text' name='altezza_foglio' value='0' size='3'>
                                                            </td>
                                                            <td align='right'>
                                                                Nel caso di formato pagina personalizzato selezionare la larghezza del foglio:
                                                            </td>
                                                            <td>
                                                                <input type='text' name='larghezza_foglio' value='0' size='3'>
                                                            </td>
                                                        </tr>
                                                        <tr class='es_sfondo_registro'>
                                                            <td align='right'>
                                                                Selezionare l'orientamento della pagina:
                                                            </td>
                                                            <td>
                                                                <SELECT name="orientamento_pagina">
                                                                    <OPTION selected value="L">Orizzontale</OPTION>
                                                                    <OPTION value="P">Verticale</OPTION>
                                                                </SELECT>
                                                            </td>
                                                            <td align='right'>
                                                                Selezionare il numero di registro generale di partenza da utilizzare:
                                                            </td>
                                                            <td>
                                                                <input type='text' name='numero_registro_generale' value='' size='6'>
                                                            </td>
                                                        </tr>
                                                        <tr class='es_sfondo_registro'>
                                                            <td align='right'>
                                                                Selezionare la data che si desidera far comparire nella stampa della pagella:
                                                            </td>
                                                            <td>
                                                                {html_select_date prefix="data_" start_year=$anno_inizio end_year=$anno_fine month_format="%m" field_order="DMY"}
                                                            </td>
                                                            <td align='right'>
                                                                Selezionare se si desidera stampare la scheda in bianco:
                                                            </td>
                                                            <td>
                                                                <SELECT name="stampa_voti">
                                                                    <OPTION selected value="SI">Stampare i voti</OPTION>
                                                                    <OPTION value="NO">Lasciare in bianco</OPTION>
                                                                </SELECT>
                                                            </td>
                                                        </tr>
                                                        <tr class='es_sfondo_registro'>
                                                            <td align='right'>
                                                                Selezionare se si vuole il dettaglio degli argomenti delle prove orali o solo il punteggio ottenuto:
                                                            </td>
                                                            <td>
                                                                <SELECT name="stampa_argomenti_orali">
                                                                    <OPTION value="NO">NO</OPTION>
                                                                    <OPTION selected value="SI">SI</OPTION>
                                                                </SELECT>
                                                            </td>
                                                            <td align='right'>
                                                                Selezionare se si vuole stampare il giudizio di ammissione:
                                                            </td>
                                                            <td>
                                                                <SELECT name="stampa_giudizio_ammissione">
                                                                    <OPTION selected value="NO">NO</OPTION>
                                                                    <OPTION value="SI">SI</OPTION>
                                                                </SELECT>
                                                            </td>
                                                        </tr>
                                                        <tr class='es_sfondo_registro'>
                                                            <td align='center' colspan='4'>
                                                                <input type='submit' name='bottone' value='Stampa'>
                                                                <input type='hidden' name='form_folder' value='registro'>
                                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                                <input type='hidden' name='stato_secondario' value='stampa_registro_esami_stato_carta_libera'>
                                                                <input type='hidden' name='form_target' value='blank'>
                                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                                            </td>
                                                    </form>
                                                    </tr>
                                                    {* }}} *}
                                                </table>
                                            {/if}
                                            {if $dati_presidente.id_utente == $current_user or $superutente == 'SI' or $permessi_commissario.permesso_inserimento == 'SI'}
                                                <table width='100%' style='display:none;' extra_tag='calendario'>
                                                    {* {{{ *}
                                                    <tr class="es_sfondo_cal_base">
                                                        <td colspan='4' align='center' class='sottotitolo_testo'>
                                                            Imposta dati prove studenti:
                                                        </td>
                                                    </tr>
                                                    <tr class="es_sfondo_cal_base">
                                                    <form method='post' action='{$SCRIPT_NAME}'>
                                                        <td>
                                                            Selezionare il tipo dati degli studenti da modificare e se usare la data dell&quot;esame orale come filtro:
                                                        </td>
                                                        {if $anno_inizio < '2018'}
                                                            <td>
                                                                {mastercom_auto_select name="tipo_dati"}
                                                                primo_scritto###Dati prima prova scritta@@@
                                                                secondo_scritto###Dati seconda prova scritta@@@
                                                                terzo_scritto###Dati terza prova scritta@@@
                                                                prova_orale###Dati prova orale@@@
                                                                dati_vari###Dati aggiuntivi
                                                                {/mastercom_auto_select}
                                                            </td>
                                                        {else}
                                                             <td>
                                                                {mastercom_auto_select name="tipo_dati"}
                                                                primo_scritto###Dati prima prova scritta@@@
                                                                secondo_scritto###Dati seconda prova scritta@@@
                                                                prova_orale###Dati prova orale@@@
                                                                dati_vari###Dati aggiuntivi
                                                                {/mastercom_auto_select}
                                                            </td>
                                                        {/if}
                                                        <td  width='20%' valign='middle'>
                                                            <input type='radio' name='usa_data_esame_orale' value='SI'>
                                                            {mastercom_smart_date prefix="data_esame_" start_year="2009"}
                                                        </td>
                                                        <td valign='middle' width='10%'>
                                                            <input checked type='radio' name='usa_data_esame_orale' value='NO'>
                                                            Tutti
                                                        </td>
                                                        </tr>
                                                        <tr class="es_sfondo_cal_base">
                                                            <td colspan=4 align='center'>
                                                                <input type='submit' name='bottone' value='Salva'>

                                                                <input type='hidden' name='form_folder' value='calendario'>
                                                                <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                                <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                                <input type='hidden' name='stato_secondario' value='multi_dati_diplomi_display'>
                                                                <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                                <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                                <input type='hidden' name='classe' value='{$classe}'>
                                                                <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                                <input type='hidden' name='current_user' value='{$current_user}'>
                                                                <input type='hidden' name='current_key' value='{$current_key}'>
                                                            </td>
                                                    </form>
                                                    </tr>
                                                    {* }}} *}
                                                </table>
                                                <table width='100%' style='display:none;' extra_tag='calendario'>
                                                    {* {{{ *}
                                                    <tr class="es_sfondo_cal_base">
                                                        <td colspan='6' align='center' class='sottotitolo_testo'>
                                                            Imposta calendari colloqui:
                                                        </td>
                                                    </tr>
                                                    <tr class="es_sfondo_cal_base">
                                                    <form method='post' action='{$SCRIPT_NAME}'>
                                                        <td align='right'>
                                                            N&deg; studenti prima giornata:
                                                        </td>
                                                        <td>
                                                            {mastercom_auto_select name="n_stud_prima_gg" value=5}
                                                            1###1@@@
                                                            2###2@@@
                                                            3###3@@@
                                                            4###4@@@
                                                            5###5@@@
                                                            6###6@@@
                                                            7###7@@@
                                                            8###8@@@
                                                            9###9@@@
                                                            10###10@@@
                                                            11###11@@@
                                                            12###12@@@
                                                            13###13@@@
                                                            14###14@@@
                                                            15###15@@@
                                                            16###16@@@
                                                            17###17@@@
                                                            18###18@@@
                                                            19###19@@@
                                                            20###20
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        <td align='right'>
                                                            N&deg; massimo studenti giornalieri:
                                                        </td>
                                                        <td>
                                                            {mastercom_auto_select name="n_max_stud_gg" value=5}
                                                            2###2@@@
                                                            3###3@@@
                                                            4###4@@@
                                                            5###5@@@
                                                            6###6@@@
                                                            7###7@@@
                                                            8###8@@@
                                                            9###9@@@
                                                            10###10@@@
                                                            11###11@@@
                                                            12###12@@@
                                                            13###13@@@
                                                            14###14@@@
                                                            15###15@@@
                                                            16###16@@@
                                                            17###17@@@
                                                            18###18@@@
                                                            19###19@@@
                                                            20###20
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        <td align='right'>
                                                            N&deg; minimo studenti giornalieri:
                                                        </td>
                                                        <td>
                                                            {mastercom_auto_select name="n_min_stud_gg" value=2}
                                                            1###1@@@
                                                            2###2@@@
                                                            3###3@@@
                                                            4###4@@@
                                                            5###5@@@
                                                            6###6@@@
                                                            7###7@@@
                                                            8###8@@@
                                                            9###9@@@
                                                            10###10@@@
                                                            11###11@@@
                                                            12###12@@@
                                                            13###13@@@
                                                            14###14@@@
                                                            15###15@@@
                                                            16###16@@@
                                                            17###17@@@
                                                            18###18@@@
                                                            19###19@@@
                                                            20###20
                                                            {/mastercom_auto_select}
                                                        </td>
                                                        </tr>
                                                        <tr class="es_sfondo_cal_base">
                                                        <form method='post' action='{$SCRIPT_NAME}'>
                                                            <td align='right'>
                                                                Data inizio esami:
                                                            </td>
                                                            <td colspan=2>
                                                                {mastercom_smart_date prefix="data_inizio_" start_year="2009"}
                                                            </td>
                                                            <td align='right'>
                                                                Lettera di partenza:
                                                            </td>
                                                            <td colspan=2>
                                                                {mastercom_auto_select name="lettera_partenza"}
                                                                A###A@@@
                                                                B###B@@@
                                                                C###C@@@
                                                                D###D@@@
                                                                E###E@@@
                                                                F###F@@@
                                                                G###G@@@
                                                                H###H@@@
                                                                I###I@@@
                                                                J###J@@@
                                                                K###K@@@
                                                                L###L@@@
                                                                M###M@@@
                                                                N###N@@@
                                                                O###O@@@
                                                                P###P@@@
                                                                Q###Q@@@
                                                                R###R@@@
                                                                S###S@@@
                                                                T###T@@@
                                                                U###U@@@
                                                                V###V@@@
                                                                W###W@@@
                                                                X###X@@@
                                                                Y###Y@@@
                                                                Z###Z
                                                                {/mastercom_auto_select}
                                                            </td>
                                                            </tr>
                                                            <tr class="es_sfondo_cal_base">
                                                                <td colspan='6' align='center'>
                                                                    <input type='hidden' name='form_folder' value='calendario'>
                                                                    <input type='submit' name='bottone' value='Calcola calendario'>
                                                                    <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                                    <input type='hidden' name='id_classe_selezionata' value='{$id_classe}'>
                                                                    <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                                    <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                                    <input type='hidden' name='stato_secondario' value='genera_calendario'>
                                                                    <input type='hidden' name='classe' value='{$classe}'>
                                                                    <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                                    <input type='hidden' name='current_user' value='{$current_user}'>
                                                                    <input type='hidden' name='current_key' value='{$current_key}'>
                                                                </td>
                                                        </form>
                                                        </tr>
                                                        {* }}} *}
                                                </table>
                                            {/if}
                                            {if $dati_presidente.id_utente == $current_user or $superutente == 'SI'}
                                                <table width='100%' style='display:none;' extra_tag='pubb'>
                                                    {* {{{ *}
                                                    <tr class='es_sfondo_pubblicazione'>
                                                        <td align='center' colspan='2' class='sottotitolo_testo'>
                                                            Pubblicazione dati prove d&quot;esame
                                                        </td>
                                                    </tr>
                                                    <form method='post' action='{$SCRIPT_NAME}'>
                                                    <tr class='es_sfondo_pubblicazione esame_scritti'>
                                                        <td align='right' class='padding_cella_generica'>
                                                            Pubblicazione dati prima prova scritta
                                                        </td>
                                                        <td class='padding_cella_generica'>
                                                            {mastercom_auto_select name="pubb_primo_scritto" value=$dati_classe.pubb_primo_scritto}
                                                            NO###Non pubblicare@@@
                                                            SI###Pubblicare
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_pubblicazione esame_scritti'>
                                                        <td align='right' class='padding_cella_generica'>
                                                            Pubblicazione dati seconda prova scritta
                                                        </td>
                                                        <td class='padding_cella_generica'>
                                                            {mastercom_auto_select name="pubb_secondo_scritto" value=$dati_classe.pubb_secondo_scritto}
                                                            NO###Non pubblicare@@@
                                                            SI###Pubblicare
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    {if $anno_inizio < '2018'}
                                                    <tr class='es_sfondo_pubblicazione esame_scritti'>
                                                        <td align='right' class='padding_cella_generica'>
                                                            Pubblicazione dati terza prova scritta
                                                        </td>
                                                        <td class='padding_cella_generica'>
                                                            {mastercom_auto_select name="pubb_terzo_scritto" value=$dati_classe.pubb_terzo_scritto}
                                                            NO###Non pubblicare@@@
                                                            SI###Pubblicare
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    {/if}
                                                    <tr class='es_sfondo_pubblicazione'>
                                                        <td align='right' class='padding_cella_generica'>
                                                            Pubblicazione dati prova orale
                                                        </td>
                                                        <td class='padding_cella_generica'>
                                                            {mastercom_auto_select name="pubb_orale" value=$dati_classe.pubb_orale}
                                                            NO###Non pubblicare@@@
                                                            SI###Pubblicare
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_pubblicazione'>
                                                        <td align='right' class='padding_cella_generica'>
                                                            Aggiorna curriculum studenti
                                                        </td>
                                                        <td class='padding_cella_generica'>
                                                            {mastercom_auto_select name="agg_curriculum"}
                                                            NO###Non aggiornare@@@
                                                            SI###Aggiornare
                                                            {/mastercom_auto_select}
                                                        </td>
                                                    </tr>
                                                    <tr class='es_sfondo_pubblicazione'>
                                                        <td align='center' colspan=2>
                                                            <input type='submit' value='Salva'>
                                                            <input type='hidden' name='form_stato' value='{$form_stato}'>
                                                            <input type='hidden' name='form_folder' value='pubb'>
                                                            <input type='hidden' name='stato_principale' value='{$stato_principale}'>
                                                            <input type='hidden' name='stato_secondario' value='aggiorna_pubblicazione_voti_commissione'>
                                                            <input type='hidden' name='indirizzo' value='{$indirizzo}'>
                                                            <input type='hidden' name='id_indirizzo' value='{$id_indirizzo}'>
                                                            <input type='hidden' name='classe' value='{$classe}'>
                                                            <input type='hidden' name='id_classe' value='{$id_classe}'>
                                                            <input type='hidden' name='current_user' value='{$current_user}'>
                                                            <input type='hidden' name='current_key' value='{$current_key}'>
                                                        </td>
                                                    </tr>
                                                    </form>
                                                    {* }}} *}
                                                </table>
                                            {/if}
                                        {/if}
                                        {*}/}}} *}
                                    {/if}
                                {/if}
                            {else}
                                <td valign='center' width='24'>
                                    {include file="include_menu_compresso.tpl"}
                                </td>
                                <td valign='top' width='*'>
                                    {include file="include_multi_dati_diplomi.tpl"}
                                {/if}
                            </td>
                </tr>
            </table>
            <script type="text/javascript" language="javascript">
                var form_folder = '{$form_folder}';

                {if $dati_classe.tipo_indirizzo == '4'}
                    {literal}
                        if (form_folder != '')
                        {
                            switch_folder_medie(form_folder);
                        }
                    {/literal}
                {else}
                    {literal}
                        if(form_folder != '')
                        {
                            switch_folder(form_folder);
                        }
                    {/literal}
                {/if}
            </script>

            {foreach from=$dati_studenti item=studente name='test'}
            <div id="pop_giudizio_prove_scritte_scuole_medie_{$studente.id_studente}" title="Giudizio prova Italiano {$studente.cognome} {$studente.nome}" style="font-size:80%">
                Inserire un messaggio<br><br>
                <textarea rows="6" cols="60"
                       name="popup_ita[{$studente.id_studente}]"
                       size="80"></textarea>
            </div>
            <div id="pop_giudizio_prove_scritte_mat_scuole_medie_{$studente.id_studente}" title="Giudizio prova scritta comp. logico-matematiche {$studente.cognome} {$studente.nome}" style="font-size:80%">
                Inserire un messaggio<br><br>
                <textarea rows="6" cols="60"
                       name="popup_mat[{$studente.id_studente}]"
                       size="80"></textarea>
            </div>
            <div id="pop_giudizio_prove_scritte_ing_scuole_medie_{$studente.id_studente}" title="Giudizio prova inglese e seconda lingua comunitaria {$studente.cognome} {$studente.nome}" style="font-size:80%">
                Inserire un messaggio<br><br>
                <textarea rows="6" cols="60"
                       name="popup_eng[{$studente.id_studente}]"
                       size="80"></textarea>
            </div>
            {/foreach}
    {if $form_stato == 'amministratore'}
        {include file="footer_amministratore.tpl"}
    {else}
        {include file="footer_professore.tpl"}
    {/if}