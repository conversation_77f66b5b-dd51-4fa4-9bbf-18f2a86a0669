<HTML>
    <HEAD>
        <TITLE>{$titolo} - {$nome}</TITLE>
        <meta name="google" value="notranslate">
        <link rel="stylesheet" href="css/style.css">
        <link rel="shortcut icon" type="image/x-icon" href="icone/mastercom.ico">
        <link rel="stylesheet" type="text/css" href="css/jquery-ui-1.8.16.custom.css" />
        <link rel="stylesheet" href="css/style.css">
        <script type="text/javascript" src="libs/jquery/jquery-2.1.1.min.js"></script>
        <script type="text/javascript" src="libs/jquery-ui-1.14.1/jquery-ui.min.js"></script>
        <script type="text/javascript" src="javascript/messenger.js"></script>
        <script type="text/javascript" src="/mastertek-api/ckeditor-mastercom/ckeditor.js"></script>
        <script type="text/javascript" src="javascript/anylink.js"></script>

		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.base.css" type="text/css" />
		<link rel="stylesheet" href="libs/jqwidgets/styles/jqx.darkblue.css" type="text/css" />
		<!--link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css"-->

		<script type="text/javascript" src="libs/jqwidgets/jqx-all.js"></script>
		<script type="text/javascript" src="libs/jqwidgets/globalization/globalize.culture.it-IT.js"></script>
    <script type="text/javascript">
$(document).ready(function () {
            // prepare the data
            var data = [
                { Person: "Bovet Daniel", q1: 0, q2: 15, tot: 7},
                { Person: "Capecchi Mario", q1: 10, q2: 0, tot: 5},
                { Person: "Carducci Giosuè", q1: 2, q2: 5, tot: 4},
                { Person: "Deledda Grazia", q1: 15, q2: 5, tot: 5},
                { Person: "Dulbecco Renato", q1: 25, q2: 45, tot: 35},
                { Person: "Fermi Enrico", q1: 2, q2: 5, tot: 5},
                { Person: "Giacconi Riccardo", q1: 50, q2: 75, tot: 65},
                { Person: "Levi-Montalcini Rita", q1: 0, q2: 0, tot: 0},
                { Person: "Marconi Guglielmo", q1: 0, q2: 0, tot: 0},
                { Person: "Montale Eugenio", q1: 60, q2: 30, tot: 45},
                { Person: "Pirandello Luigi", q1: 0, q2: 0, tot: 0}
                ];
            // prepare jqxChart settings
            var settings = {
                title: "Assenze Studenti",
                description: "Percentuale assenze studenti",
                enableAnimations: true,
                showLegend: true,
                padding: { left: 5, top: 5, right: 10, bottom: 5 },
                titlePadding: { left: 90, top: 0, right: 0, bottom: 10 },
                source: data,
                xAxis:
                {
                    dataField: 'Person',
                    unitInterval: 1,
					labels: {
                        angle: 90,
                        horizontalAlignment: 'left',
                        verticalAlignment: 'center',
                        rotationPoint: 'right',
                        offset: { x: 0, y: -5 }
                    },
                    tickMarks: {
                        visible: true,
                        interval: 1,
                        color: '#BCBCBC'
                    },
					bands: [
						{ minValue: 2.5, maxValue: 3.5, fillColor: 'lightblue', opacity: 0.8 }
					]
				},
                colorScheme: 'scheme11',
                seriesGroups:
                    [
                        {
                            orientation: 'horizontal',
                            type: 'rangecolumn',
                            columnsGapPercent: 120,
                            valueAxis:
                            {
                                flip: true,
                                minValue: 0,
                                maxValue: 100,
                                unitInterval: 5,
                                title: { text: '% Assenze' },
                                tickMarks: { color: '#BCBCBC' }
                            },
                            series: [
                                    { dataFieldFrom: 0, dataFieldTo: 'q1', displayText: '1° Quadrimestre', opacity: 1, color: '#e11f7f' },
                                    { dataFieldFrom: 0, dataFieldTo: 'q2', displayText: '2° Quadrimestre', opacity: 1, color: '#aac739' },
                                    { dataFieldFrom: 0, dataFieldTo: 'tot', displayText: 'Complessivo', opacity: 1, color: '#8a1fdf' }
                                ],
                            bands:
                            [
                                { minValue: 25, maxValue: 100, color: '#FF0000', opacity: 0.2, lineWidth: 3 }
                            ]
                        }
                    ]
            };
            // setup the chart
            $('#chartContainer').jqxChart(settings);
        });
</script>
</head>
<body class='default'>
	<div style='height: 600px; width: 682px;'>
    <div id='host' style="margin: 0 auto; width:1200px; height:800px;">
		<div id='chartContainer' style="width:800px; height:450px; position: relative; left: 0px; top: 0px;">
		</div>
	</div>
    </div>
</body>
</html>
